import { Button } from "#/ui/button";
import { type FunctionObjectType } from "@braintrust/typespecs";
import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type TransactionId } from "braintrust/util";
import isEqual from "lodash.isequal";
import { TransactionIdField } from "@braintrust/local/query";
import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { type Mode } from "./types";
import { useRouter } from "next/navigation";
import { cn } from "#/utils/classnames";

export function FunctionEditorSubmitButton({
  mode,
  type,
  getOutputPrompt,
  prompt,
  isSlugTouchedRef,
  isDirtyRef,
  setIsDirty,
  setError,
  upsert,
  hasLintErrors,
  initialDirtyFunctionComparisonBase,
  updating,
  setUpdating,
  orgName,
  projectName,
  className,
}: {
  mode: Mode;
  type: FunctionObjectType;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  prompt: SyncedPlaygroundBlock;
  isSlugTouchedRef: RefObject<boolean>;
  isDirtyRef?: RefObject<boolean>;
  setIsDirty?: Dispatch<SetStateAction<boolean>>;
  setError: Dispatch<SetStateAction<string | null>>;
  upsert: (
    prompt: UIFunction,
    isSlugTouchedRef: boolean,
  ) => Promise<TransactionId | null>;
  hasLintErrors: boolean;
  initialDirtyFunctionComparisonBase: UIFunction | null;
  updating: boolean;
  setUpdating: Dispatch<SetStateAction<boolean>>;
  orgName: string;
  projectName: string;
  className?: string;
}) {
  const saveButtonRef = useRef<HTMLButtonElement>(null);
  useHotkeys(
    "Mod+S",
    () => {
      saveButtonRef.current?.click();
    },
    {
      enableOnFormTags: true,
      enableOnContentEditable: true, // codemirror is contenteditable
      preventDefault: true,
    },
  );

  const [dirtyFunctionComparisonBase, setDirtyFunctionComparisonBase] =
    useState<UIFunction | null>(initialDirtyFunctionComparisonBase);
  const isDirty = useMemo(
    () =>
      !isEqual(
        makeComparisonPrompt(dirtyFunctionComparisonBase),
        makeComparisonPrompt(getOutputPrompt(prompt)),
      ),
    [dirtyFunctionComparisonBase, getOutputPrompt, prompt],
  );
  useEffect(() => {
    if (isDirtyRef) {
      isDirtyRef.current = isDirty;
    }
    if (setIsDirty) {
      setIsDirty(isDirty);
    }
  }, [isDirty, isDirtyRef, setIsDirty]);

  const router = useRouter();
  const onSubmit = async () => {
    const outputPrompt = getOutputPrompt(prompt);

    if (!outputPrompt.name || outputPrompt.name.trim() === "") {
      setError("Name cannot be empty");
      return;
    }
    if (!outputPrompt.slug || outputPrompt.slug.trim() === "") {
      setError("Slug cannot be empty");
      return;
    }

    setError(null);
    setUpdating(true);

    try {
      await upsert(outputPrompt, isSlugTouchedRef.current);
      setDirtyFunctionComparisonBase(outputPrompt);

      if (mode.type === "view_unsaved") {
        router.replace(
          getSavedPromptLink({
            orgName,
            projectSlug: projectName,
            promptId: outputPrompt.id,
            type,
          }),
        );
      }
    } catch (e) {
      setError(`${e}`);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Button
      className={cn("flex-none", className)}
      size="xs"
      variant="primary"
      type="submit"
      ref={saveButtonRef}
      disabled={
        mode.type === "view_unsaved" ? false : !isDirty || hasLintErrors
      }
      onClick={async (e) => {
        e.preventDefault();
        onSubmit();
      }}
      isLoading={updating}
    >
      {mode.type === "update" ? "Save version" : `Save as custom ${type}`}
    </Button>
  );
}

function makeComparisonPrompt(prompt: UIFunction | null) {
  if (prompt == null) return null;

  const cleanedPromptData = {
    ...prompt.prompt_data,
    // strip position
    ...(prompt.prompt_data?.options?.position
      ? { options: { ...prompt.prompt_data?.options, position: undefined } }
      : {}),
    // strip origin
    origin: undefined,
  };

  return {
    id: prompt.id,
    [TransactionIdField]: prompt[TransactionIdField],
    project_id: prompt.project_id,
    name: prompt.name,
    slug: prompt.slug,
    description: prompt.description,
    metadata: prompt.metadata,
    prompt_data: cleanedPromptData,
    function_data: prompt.function_data,
    function_type: prompt.function_type,
    tags: prompt.tags,
  };
}

import { ALLOWED_ORIGIN, WHITELISTED_ORIGINS } from "./env";
import { InternalServerError } from "./util";

export type StaticOrigin =
  | boolean
  | string
  | RegExp
  | Array<boolean | string | RegExp>;

export function checkOrigin(
  requestOrigin: string | undefined,
  callback: (err: Error | null, origin?: StaticOrigin) => void,
) {
  if (!requestOrigin) {
    return callback(null, true);
  }

  // the origins can be glob patterns
  for (const origin of WHITELISTED_ORIGINS || []) {
    if (
      (origin instanceof RegExp && origin.test(requestOrigin)) ||
      origin === requestOrigin
    ) {
      return callback(null, requestOrigin);
    }
  }

  return callback(null, false);
}

export const BRAINTRUST_APP_ORIGIN_HEADER = "x-bt-app-origin";
export const ORIGIN_HEADER = "origin";

export function extractAllowedOrigin(originHeader: string | undefined): string {
  if (!ALLOWED_ORIGIN) {
    throw new InternalServerError("ALLOWED_ORIGIN is not set");
  }
  let allowedOrigin: string = ALLOWED_ORIGIN;
  checkOrigin(originHeader, (err, origin) => {
    if (!err && originHeader && origin) {
      allowedOrigin = originHeader;
    }
  });
  return allowedOrigin;
}

export const baseAllowedHeaders = [
  "Content-Type",
  "X-Amz-Date",
  "Authorization",
  "X-Api-Key",
  "X-Amz-Security-Token",
  "x-bt-auth-token",
  "x-bt-parent",
  "x-bt-source",
  "x-bt-app-origin",
  "x-bt-query-plan",
];

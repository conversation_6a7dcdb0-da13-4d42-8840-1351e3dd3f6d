import { get } from "lodash";
import { isEmpty } from "braintrust/util";
import {
  MessageField,
  SpanSpec,
  deserializeIfString,
  translateModelParams,
  notHandled,
  handled,
  mustParseJson,
} from "./attributes";
import { translateGenAIMessages } from "./gen-ai";

export const crewAISpanSpec: SpanSpec = {
  input: (attributes) => {
    // If there is no CrewAI namespace at all, do not handle
    if (get(attributes, "crewai") === undefined) {
      return notHandled;
    }

    // Crew-level task description → derive a user message
    const tasksRaw = get(attributes, "crewai.crew.tasks");
    if (typeof tasksRaw === "string" && tasksRaw !== "") {
      const description = extractFirstQuotedField(tasksRaw, "description");
      if (description) {
        return handled({
          isLLM: true,
          data: [{ role: "user", content: description }],
        });
      }
    }

    const inputMessages = get(attributes, "crewai.input.messages");
    if (inputMessages !== undefined && !isEmpty(inputMessages)) {
      const result = translateGenAIMessages({
        value: typeof inputMessages === "string" ? undefined : inputMessages,
        valueJson:
          typeof inputMessages === "string" ? inputMessages : undefined,
      });
      if (result !== undefined) {
        return handled(result);
      }
    }

    const result = translateCrewAIPrompt({
      value: get(attributes, "crewai.prompt"),
      valueJson: get(attributes, "crewai.prompt_json"),
      defaultRole: "user",
    });
    if (result === undefined) {
      return notHandled;
    }
    return handled(result);
  },
  output: (attributes) => {
    // If there is no CrewAI namespace at all, do not handle
    if (get(attributes, "crewai") === undefined) {
      return notHandled;
    }

    // Crew-level final result → derive assistant message
    const resultRaw = get(attributes, "crewai.crew.result");
    if (typeof resultRaw === "string" && resultRaw !== "") {
      const extracted = extractCodeFenceOrText(resultRaw);
      if (extracted) {
        return handled({
          isLLM: true,
          data: [{ role: "assistant", content: extracted }],
        });
      }
    }

    const outputMessages = get(attributes, "crewai.output.messages");
    if (outputMessages !== undefined && !isEmpty(outputMessages)) {
      const result = translateGenAIMessages({
        value: typeof outputMessages === "string" ? undefined : outputMessages,
        valueJson:
          typeof outputMessages === "string" ? outputMessages : undefined,
      });
      if (result !== undefined) {
        return handled(result);
      }
    }

    const result = translateCrewAIPrompt({
      value: get(attributes, "crewai.completion"),
      valueJson: get(attributes, "crewai.completion_json"),
      defaultRole: "assistant",
    });
    if (result === undefined) {
      return notHandled;
    }
    return handled(result);
  },
  metadata: (attributes, events) => {
    // If there is no CrewAI namespace at all, do not handle
    if (get(attributes, "crewai") === undefined) {
      return notHandled;
    }
    const metadata = translateModelParams(
      deserializeIfString(get(attributes, "crewai.request")),
    );

    const rawModelName = get(attributes, "crewai.request.model");
    if (rawModelName && typeof rawModelName === "string") {
      metadata.model = cleanModelName(rawModelName);
    }

    // Selective CrewAI metadata
    const crewMeta: Record<string, unknown> = {};
    const crewName = get(attributes, "crewai.crew.name");
    if (typeof crewName === "string") crewMeta.name = crewName;
    const crewId = get(attributes, "crewai.crew.id");
    if (typeof crewId === "string") crewMeta.id = crewId;
    const crewProcess = get(attributes, "crewai.crew.process");
    if (typeof crewProcess === "string") crewMeta.process = crewProcess;
    const flags: Array<[string, string]> = [
      ["verbose", "crewai.crew.verbose"],
      ["planning", "crewai.crew.planning"],
      ["memory", "crewai.crew.memory"],
      ["cache", "crewai.crew.cache"],
      ["share_crew", "crewai.crew.share_crew"],
    ];
    for (const [key, path] of flags) {
      const v = get(attributes, path);
      if (typeof v === "string") crewMeta[key] = parseBooleanString(v);
    }
    if (Object.keys(crewMeta).length > 0) {
      metadata.crewai = crewMeta;
    }

    if (isEmpty(metadata) || Object.keys(metadata).length === 0) {
      return notHandled;
    }
    return handled(metadata);
  },
  span_attributes: (attributes) => {
    // If there is no CrewAI attributes at all, do not handle
    if (
      get(attributes, "crewai") === undefined &&
      get(attributes, "crewai_version") === undefined
    ) {
      return notHandled;
    }

    const explicitToolName =
      get(attributes, "crewai.tool.name") || get(attributes, "tool_name");

    // Explicit tool name present (even without execute_tool op)
    if (typeof explicitToolName === "string" && explicitToolName) {
      return handled({ type: "tool", name: explicitToolName });
    }

    return handled({ type: "task" });
  },
  metrics: () => notHandled,
};

function translateCrewAIPrompt({
  value,
  valueJson,
  defaultRole,
}: {
  value: unknown;
  valueJson: unknown;
  defaultRole: "user" | "assistant";
}): MessageField | undefined {
  if (isEmpty(value) && isEmpty(valueJson)) {
    return undefined;
  }

  const prompt = valueJson ? mustParseJson(valueJson) : value;

  if (typeof prompt === "string") {
    return {
      isLLM: true,
      data: [
        {
          role: defaultRole,
          content: prompt,
        },
      ],
    };
  }

  return { isLLM: false, data: prompt };
}

function extractFirstQuotedField(
  raw: string,
  field: string,
): string | undefined {
  // Match 'field': 'value' or "field": "value" with escape support
  const regex = new RegExp(
    String.raw`['\"]${field}['\"]\s*:\s*(['\"])((?:\\.|(?!\1).)*)\1`,
  );
  const m = raw.match(regex);
  if (!m) return undefined;
  const content = m[2];
  try {
    // Unescape common sequences if present
    return JSON.parse(`"${content.replace(/"/g, '\\"')}"`);
  } catch {
    return content;
  }
}

function extractCodeFenceOrText(raw: string): string | undefined {
  // ```lang\n...\n``` or ```\n...\n```
  const fence = raw.match(/```[a-zA-Z]*\n([\s\S]*?)\n```/);
  if (fence && fence[1]) return fence[1];
  return raw;
}

function parseBooleanString(value: string): boolean | string {
  if (value.toLowerCase() === "true") return true;
  if (value.toLowerCase() === "false") return false;
  return value;
}

// no numeric parsing helpers needed; LLM spans report metrics directly

function cleanModelName(modelName: string): string {
  const prefixes = [
    "openai/",
    "anthropic/",
    "google/",
    "azure/",
    "bedrock/",
    "vertex_ai/",
  ];
  for (const prefix of prefixes) {
    if (modelName.startsWith(prefix)) {
      return modelName.slice(prefix.length);
    }
  }
  return modelName;
}

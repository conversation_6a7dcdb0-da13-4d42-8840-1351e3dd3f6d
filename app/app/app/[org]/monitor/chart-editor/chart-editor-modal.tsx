import { <PERSON><PERSON> } from "#/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
} from "#/ui/dialog";
import { useCallback, useMemo, useState } from "react";
import { cn } from "#/utils/classnames";
import {
  chartConfigSchema,
  type ChartConfig,
} from "../chart-config/custom-charts-schema";
import { MonitorCard, type MonitorCardProps } from "../card/monitor-card";
import { ChartEditorText } from "./chart-editor-text";
import { ChartVisualEditor } from "./chart-visual-editor";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "#/ui/tabs";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import type { ProjectSummary } from "../../org-actions";
import { chartConfigToMonitorCardConfig } from "../chart-config/chart-config-to-monitor-card-config";
import { nanoid } from "ai";
import { getPresetMonitorCard } from "../chart-config/get-preset-monitor-card";
import type { ChartSaveMode } from "../chart-config/use-custom-charts";
import type { DataObjectType } from "#/utils/btapi/btapi";
import { DEFAULT_EDITOR_CHART_CONFIG } from "./chart-editor.constants";
import isEqual from "lodash.isequal";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { searchToExpressions } from "../search-to-expressions";
import type { Search } from "#/utils/search/search";
import { Input } from "#/ui/input";

export function CardEditorModal(
  props: {
    title: string;
    objectType: DataObjectType;
    objectIds: string[];
    project?: ProjectSummary;
    showEdit: boolean;
    initialChart?: ChartConfig;
    presetId?: string;
    saveNewMode: "new-first" | "new-next" | "new-last";
    existingViewNames?: Set<string>;
    onSave: (props: {
      chartConfig: ChartConfig;
      mode: ChartSaveMode;
      presetId?: string;
      newViewName?: string;
    }) => Promise<void>;
    onClose?: () => void;
    onDelete?: (props: { newViewName?: string }) => void;
    search?: Search;
    requireNewView?: boolean;
  } & Omit<MonitorCardProps, "cardConfig">,
) {
  const {
    onDelete,
    showEdit,
    title,
    project,
    saveNewMode,
    onSave,
    onClose,
    existingViewNames,
    initialChart = DEFAULT_EDITOR_CHART_CONFIG,
    presetId,
    objectType,
    objectIds,
    search,
    requireNewView,
    ...rest
  } = props;

  const additionalFilters = useMemo(() => {
    return searchToExpressions(search, objectType);
  }, [search, objectType]);

  const enableTextEditor = useIsFeatureEnabled("chartTextEditor");

  const [editorContent, setEditorContent] = useState<string>(
    JSON.stringify(initialChart),
  );

  const [editorValidity, setEditorValidity] = useState<"valid" | "invalid">(
    "valid",
  );

  const [selectedPresetId, setSelectedPresetId] = useState<string | undefined>(
    presetId,
  );

  const [isSaving, setIsSaving] = useState<boolean>(false);

  const [newViewName, setNewViewName] = useState<string | null>(null);

  /** The monitor card config to live preview chart */
  const displayCard = useMemo(() => {
    if (selectedPresetId) {
      const preset = getPresetMonitorCard(selectedPresetId);
      if (preset) {
        return preset.config;
      }
    }

    try {
      const parsed = JSON.parse(editorContent);
      const config = chartConfigSchema.parse(parsed);
      return chartConfigToMonitorCardConfig(config, selectedPresetId);
    } catch {
      // todo - create some kind of invalid card config type
      // for now fallback to initial chart provided
      return chartConfigToMonitorCardConfig(initialChart, selectedPresetId);
    }
  }, [editorContent, initialChart, selectedPresetId]);

  /** our current chart config */
  const displayChart = useMemo(() => {
    try {
      const parsed = JSON.parse(editorContent);
      return chartConfigSchema.parse(parsed);
    } catch {
      // todo - create some kind of invalid card config type
      // for now fallback to initial chart provided
      return initialChart;
    }
  }, [editorContent, initialChart]);

  // if editor saved state will be different than initial
  const isDirty = useMemo(() => {
    if (presetId !== selectedPresetId) {
      return true;
    }
    return !isEqual(displayChart, initialChart);
  }, [displayChart, initialChart, presetId, selectedPresetId]);

  // soft prevent modal close when there are unsaved changes
  const [showCloseConfirm, setShowCloseConfirm] = useState<boolean>(false);

  // disable save buttons if editor state is invalid or we need to create a new view
  const disableSaveReason = useMemo(() => {
    if (editorValidity === "invalid") {
      return "Current chart configuration is invalid";
    }
    if (requireNewView && !Boolean(newViewName)) {
      return "Create a new view to save to first";
    }
    if (existingViewNames?.has(newViewName ?? "")) {
      return "View already exists";
    }
  }, [editorValidity, requireNewView, newViewName, existingViewNames]);
  const disableSave = disableSaveReason !== undefined;

  const handleOnSave = useCallback(
    async (mode: ChartSaveMode) => {
      // append preset id with id to avoid id collisions
      const presetId = Boolean(selectedPresetId)
        ? `${selectedPresetId}-${nanoid()}`
        : undefined;

      setIsSaving(true);
      await onSave({
        chartConfig: displayChart,
        mode,
        presetId,
        newViewName: newViewName ?? undefined,
      });
      setIsSaving(false);
    },
    [displayChart, onSave, selectedPresetId, newViewName],
  );

  const handleOnDelete = useCallback(() => {
    onDelete?.({ newViewName: newViewName ?? undefined });
  }, [onDelete, newViewName]);

  const onVisualEditorChange = useCallback((c: ChartConfig) => {
    setEditorContent(JSON.stringify(c));
  }, []);

  return (
    <>
      <Dialog
        open
        onOpenChange={() => {
          if (isDirty) {
            setShowCloseConfirm(true);
            return;
          }
          onClose?.();
        }}
      >
        <DialogContent
          className="flex flex-col gap-0 overflow-hidden p-0"
          style={{ width: "80vw", maxWidth: "80vw", height: "80vh" }}
        >
          <DialogHeader className="flex-none border-b p-5">
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <div className="flex flex-1 flex-col overflow-hidden lg:flex-row">
            {displayCard && (
              <MonitorCard
                {...rest}
                filters={additionalFilters}
                disableClick
                className="m-0 max-h-64 flex-1 rounded-none border-r p-6 lg:max-h-none"
                cardConfig={{ ...displayCard, header: undefined }}
                standaloneLayoutMode
                setCardState={() => {}}
              />
            )}
            <div className="relative flex-1 overflow-auto px-5 pb-5">
              <Tabs defaultValue="visual">
                {enableTextEditor && (
                  <TabsList className="sticky top-0 z-10 flex w-full items-end justify-start gap-6 rounded-none border-b border-primary-200 bg-background pb-0">
                    <TabsTrigger
                      value="visual"
                      className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
                    >
                      Configuration
                    </TabsTrigger>
                    <TabsTrigger
                      value="text"
                      className="-mb-px rounded-none border-b border-transparent bg-transparent p-0 pb-2 text-xs data-[state=active]:border-primary-600 data-[state=active]:bg-transparent"
                    >
                      Raw config
                    </TabsTrigger>
                  </TabsList>
                )}
                <TabsContent
                  value="visual"
                  className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
                >
                  <ChartVisualEditor
                    config={displayChart}
                    onChange={onVisualEditorChange}
                    selectedPresetId={selectedPresetId}
                    setSelectedPresetId={setSelectedPresetId}
                    project={project}
                    objectType={objectType}
                    objectIds={objectIds}
                    chartTimeFrame={rest.chartTimeFrame}
                  />
                </TabsContent>
                <TabsContent
                  value="text"
                  className="m-0 hidden flex-col pt-2 data-[state=active]:flex"
                >
                  <div
                    className={cn({
                      "border border-primary-200 bg-blue-50": enableTextEditor,
                      "bg-bad-50 dark:bg-bad-50/40":
                        editorValidity === "invalid",
                    })}
                  >
                    <ChartEditorText
                      config={displayChart}
                      onValidity={(v) => setEditorValidity(v)}
                      onChange={setEditorContent}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
          <DialogFooter className="flex-none border-t p-5">
            {showEdit && !requireNewView && onDelete && (
              <Button
                type="submit"
                size="xs"
                variant="ghost"
                disabled={disableSave}
                onClick={handleOnDelete}
              >
                Delete
              </Button>
            )}
            <span className="flex-1" />
            {requireNewView && (
              <div className="flex items-center">
                <div className="mr-3 text-xs text-primary-500">
                  This chart will be saved to a new view
                </div>
                <Input
                  className="h-7 w-40 rounded-r-none border-r-0 text-xs"
                  size={30}
                  onChange={(e) => setNewViewName(e.target.value)}
                  placeholder="Enter new view name"
                />
                <Button
                  type="submit"
                  size="xs"
                  variant="primary"
                  className="rounded-l-none"
                  disabled={disableSave}
                  isLoading={isSaving}
                  onClick={() => {
                    handleOnSave(saveNewMode);
                  }}
                >
                  Save
                </Button>
              </div>
            )}
            {!requireNewView && (
              <>
                <Button
                  type="submit"
                  size="xs"
                  disabled={disableSave}
                  isLoading={isSaving}
                  onClick={() => {
                    handleOnSave(saveNewMode);
                  }}
                >
                  Save as new
                </Button>
                {showEdit && (
                  <Button
                    type="submit"
                    size="xs"
                    variant="primary"
                    disabled={disableSave}
                    isLoading={isSaving}
                    onClick={() => {
                      handleOnSave("existing");
                    }}
                  >
                    Save
                  </Button>
                )}
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <ConfirmationDialog
        open={showCloseConfirm}
        onOpenChange={() => {
          setShowCloseConfirm(false);
        }}
        title="Unsaved chart changes"
        description="You have unsaved changes. Are you sure you want to close the editor?"
        confirmText="Close"
        onConfirm={() => {
          setShowCloseConfirm(false);
          onClose?.();
        }}
      />
    </>
  );
}

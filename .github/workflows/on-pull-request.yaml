name: Pull Request
on:
  pull_request:
  workflow_dispatch:
    inputs:
      pr_number:
        description: "Pull request number to run CI for"
        required: true
        type: string

permissions:
  id-token: write # OIDC permissions for AWS auth
  contents: read
  actions: write # Required for workflow dispatch
  pull-requests: write # Required for PR comments

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || inputs.pr_number }}
  cancel-in-progress: true

jobs:
  ci:
    uses: ./.github/workflows/braintrust-ci.yaml
    with:
      release_as_latest: false
      run_serial: false
      docker_run_serial: false
      skip_tests: ${{ contains(toJSON(github.event.pull_request.labels.*.name), 'skip-integration-tests') }}
      debug_enabled: false
    secrets:
      DOCKERHUB_TOKEN: ${{ secrets.DOCKERHUB_TOKEN }}
      ORB_API_KEY: ${{ secrets.ORB_API_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      POLAR_SIGNALS_API_KEY: ${{ secrets.POLAR_SIGNALS_API_KEY }}
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

  submodule-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          fetch-depth: 0 # Need full history for submodule checks

      - name: Check submodules are on main
        uses: ./.github/actions/submodule-check
        with:
          submodules: "sdk,proxy"

  pr-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Minimal checkout, just for the action files
          sparse-checkout: |
            .github

      - name: Run Terraform reminder check
        uses: ./.github/actions/terraform-reminder
        with:
          bot-app-id: ${{ secrets.GH_BOT_APP_ID }}
          bot-private-key: ${{ secrets.GH_BOT_APP_PRIVATE_KEY }}

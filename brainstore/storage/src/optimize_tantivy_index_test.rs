use std::{collections::HashSet, str::FromStr, time::Duration};

use crate::{
    basic_test_fixture::{BasicTestFixture, ValidateVacuumArgs},
    config_with_store::ConfigWithStore,
    merge::MergeOpts,
    optimize_tantivy_index::{
        construct_optimization_steps, optimize_segments, run_optimization_loop, OptimizableSegment,
        OptimizationLoopConfig, OptimizationStepOptions, OptimizeAllObjectsInput,
        OptimizeAllObjectsOptions, OptimizeObjectInput, OptimizeObjectOptions,
        OptimizeObjectOutput,
    },
    process_wal::{
        compact_segment_wal, process_object_wal, CompactSegmentWalOptions, CompactSegmentWalOutput,
        ProcessObjectWalInput, ProcessObjectWalOptions,
    },
    tantivy_index::{
        IndexMetaJson, TantivyIndexScope, TantivyIndexWriterOpts, ValidateTantivyIndexOptions,
    },
    wal::WALScope,
    wal_entry::WalEntry,
};
use futures::StreamExt;
use once_cell::sync::Lazy;
use serde_json::json;
use util::{
    system_types::{FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

static DEFAULT_COMPACT_SEGMENT_WAL_OPTS: Lazy<CompactSegmentWalOptions> =
    Lazy::new(|| CompactSegmentWalOptions {
        writer_opts: TantivyIndexWriterOpts {
            index_writer_force_no_merges: true,
            validate_opts: ValidateTantivyIndexOptions {
                index_writer_validate: true,
                index_writer_validate_only_deletes: false,
                ..Default::default()
            },
            ..Default::default()
        },
        ..Default::default()
    });

static DEFAULT_OPTIMIZE_ALL_OBJECTS_OPTS: Lazy<OptimizeAllObjectsOptions> =
    Lazy::new(|| OptimizeAllObjectsOptions {
        optimize_opts: OptimizeObjectOptions {
            merge_opts: MergeOpts {
                target_num_segments: 1,
                use_exact_num_merge_policy: true,
                ..Default::default()
            },
            compact_wal_opts: CompactSegmentWalOptions {
                writer_opts: TantivyIndexWriterOpts {
                    validate_opts: ValidateTantivyIndexOptions {
                        index_writer_validate: true,
                        index_writer_validate_only_deletes: false,
                        ..Default::default()
                    },
                    ..Default::default()
                },
                ..Default::default()
            },
            ..Default::default()
        },
        no_jitter: true,
        ..Default::default()
    });

struct TestFixture {
    basic_fixture: BasicTestFixture,
}

impl TestFixture {
    fn new() -> Self {
        Self {
            basic_fixture: BasicTestFixture::new(),
        }
    }

    pub fn config(&self) -> &ConfigWithStore {
        &self.basic_fixture.tmp_dir_config.config
    }

    pub async fn wal_token_obj(&self, object_id: FullObjectId<'_>) -> Uuid {
        self.basic_fixture.wal_token_obj(object_id).await
    }

    pub async fn write_object_wal_entries(&self, entries: Vec<WalEntry>) {
        self.basic_fixture.write_object_wal_entries(entries).await
    }

    async fn run_process_wal(&self) -> Vec<Uuid> {
        process_object_wal(
            self.basic_fixture.process_wal_input(),
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap()
        .modified_segment_ids
        .into_iter()
        .collect()
    }

    async fn run_compact_wal(&self, segment_id: Uuid) -> CompactSegmentWalOutput {
        compact_segment_wal(
            self.basic_fixture.compact_wal_input(segment_id),
            Default::default(),
            DEFAULT_COMPACT_SEGMENT_WAL_OPTS.clone(),
        )
        .await
        .unwrap()
    }

    fn optimize_object_input<'a>(&self, segment_ids: &'a [Uuid]) -> OptimizeObjectInput<'a> {
        let tmp_dir_config = &self.basic_fixture.tmp_dir_config;
        OptimizeObjectInput {
            storage_config: tmp_dir_config.storage_config.clone(),
            file_cache_opts: Default::default(),
            config: tmp_dir_config.config.clone(),
            segment_ids,
            schema: self.basic_fixture.make_default_full_schema(),
            dry_run: false,
            recompact: false,
            run_async: false,
        }
    }

    fn optimize_all_objects_input(&self) -> OptimizeAllObjectsInput {
        let tmp_dir_config = &self.basic_fixture.tmp_dir_config;
        OptimizeAllObjectsInput {
            storage_config: tmp_dir_config.storage_config.clone(),
            file_cache_opts: Default::default(),
            config: tmp_dir_config.config.clone(),
            schema: Some(self.basic_fixture.make_default_full_schema()),
            loop_config: OptimizationLoopConfig {
                name: "test",
                compact: true,
                merge: true,
                min_compaction_lag_seconds: None,
                min_processed_seconds_ago: 0,
                max_processed_seconds_ago: 86400 * 365, // Process objects up to 1 year old
                budget: 0.5,
            },
            max_iterations: None,
            ignore_old_segment_object_ids: Default::default(),
        }
    }

    async fn fetch_segment_tantivy_metadata(&self, segment_id: Uuid) -> IndexMetaJson {
        self.basic_fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
    }
}

fn check_segment_optimization_succeeded(output: &OptimizeObjectOutput) {
    for step in output.performed.iter() {
        assert!(
            step.error.is_none(),
            "step {:?} failed with error {:?}",
            step.step,
            step.error
        );
    }
}

#[test]
fn test_optimize_steps() {
    let segment_ids = [Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
    let segments = vec![
        OptimizableSegment {
            segment_id: segment_ids[0],
            min_pagination_key: PaginationKey(100),
            last_processed_xact_id: Some(TransactionId(5)),
            last_compacted_xact_id: Some(TransactionId(3)),
            earliest_uncompacted_xact_id: Some(TransactionId(4)),
            num_tantivy_segments: 3,
            num_rows: 1000,
            has_stats: true,
        },
        OptimizableSegment {
            segment_id: segment_ids[1],
            min_pagination_key: PaginationKey(50),
            last_processed_xact_id: Some(TransactionId(10)),
            last_compacted_xact_id: Some(TransactionId(8)),
            earliest_uncompacted_xact_id: Some(TransactionId(9)),
            num_tantivy_segments: 1,
            num_rows: 10,
            has_stats: true,
        },
        OptimizableSegment {
            segment_id: segment_ids[2],
            min_pagination_key: PaginationKey(75),
            last_processed_xact_id: Some(TransactionId(8)),
            last_compacted_xact_id: Some(TransactionId(7)),
            earliest_uncompacted_xact_id: None,
            num_tantivy_segments: 2,
            num_rows: 1000,
            has_stats: true,
        },
    ];

    let steps = construct_optimization_steps(segments, OptimizationStepOptions::default());

    // Should have 2 steps. Segment id 0 should have a compact and a merge.
    assert_eq!(steps.len(), 2);
    assert_eq!(steps[0].segment_id, segment_ids[0]);
    assert!(steps[0].compact);
    assert!(steps[0].merge);
    assert_eq!(steps[1].segment_id, segment_ids[1]);
    assert!(steps[1].compact);
    assert!(!steps[1].merge);
}

#[tokio::test]
async fn test_basic_optimize_compact() {
    let fixture = TestFixture::new();
    fixture
        .write_object_wal_entries(vec![WalEntry::default()])
        .await;
    let segment_ids = fixture.run_process_wal().await;
    assert_eq!(segment_ids.len(), 1);
    let segment_id = segment_ids[0];

    // Check that optimization steps include compaction
    let output = optimize_segments(
        OptimizeObjectInput {
            dry_run: true,
            ..fixture.optimize_object_input(&segment_ids)
        },
        Default::default(),
    )
    .await
    .unwrap();

    assert_eq!(output.performed.len(), 0);
    assert_eq!(output.planned.len(), 1);

    assert_eq!(output.planned[0].segment_id, segment_id);
    assert!(output.planned[0].compact);
    assert!(!output.planned[0].merge);

    // Now, let merging run, and check that it's compacted
    let output = optimize_segments(
        fixture.optimize_object_input(&segment_ids),
        Default::default(),
    )
    .await
    .unwrap();

    assert_eq!(output.performed.len(), 1);
    assert_eq!(output.planned.len(), 0);

    // Check that the segment is compacted
    assert_eq!(
        fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
            .segments
            .len(),
        1
    );

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs::default())
        .await;
}

#[tokio::test]
async fn test_basic_optimize_merge() {
    let fixture = TestFixture::new();

    let mut segment_id: Option<Uuid> = None;
    for i in 0..5 {
        let wal_entries = vec![WalEntry {
            _pagination_key: PaginationKey(i),
            _xact_id: TransactionId(i),
            id: format!("row{}", i),
            data: json!({
                "field1": "foo",
                "field3": json!({ "input": "bar" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        }];
        fixture.write_object_wal_entries(wal_entries).await;
        let res = fixture.run_process_wal().await;
        assert_eq!(res.len(), 1);
        if segment_id.is_none() {
            segment_id = Some(res[0]);
        } else {
            assert_eq!(segment_id, Some(res[0]));
        }

        assert_eq!(
            fixture
                .run_compact_wal(segment_id.unwrap())
                .await
                .num_wal_entries_compacted,
            1
        );
    }

    let segment_id = segment_id.unwrap();
    let segment_ids = vec![segment_id];

    // Check that optimization steps include compaction
    let output = optimize_segments(
        OptimizeObjectInput {
            dry_run: true,
            ..fixture.optimize_object_input(&segment_ids)
        },
        Default::default(),
    )
    .await
    .unwrap();

    assert_eq!(output.performed.len(), 0);
    assert_eq!(output.planned.len(), 1);

    assert_eq!(output.planned[0].segment_id, segment_id);
    assert!(output.planned[0].merge);

    // Check that the segment is compacted but not merged.
    assert!(
        fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
            .segments
            .len()
            > 1
    );

    let output = optimize_segments(
        fixture.optimize_object_input(&segment_ids),
        Default::default(),
    )
    .await
    .unwrap();

    assert_eq!(output.performed.len(), 1);
    assert_eq!(output.planned.len(), 0);

    assert!(output.performed[0].error.is_none());
    assert_eq!(output.performed[0].num_rows_processed, 5);

    // Check that the segment is merged
    assert_eq!(
        fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
            .segments
            .len(),
        1
    );

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs::default())
        .await;
}

#[tokio::test]
async fn test_basic_optimize_loop() {
    let fixture = TestFixture::new();

    // Create multiple tantivy segments that need optimization.
    let mut segment_id: Uuid = Default::default();
    for i in 0..12 {
        // Increased to exceed 95% of max_rows_per_segment (10)
        let wal_entries = vec![WalEntry {
            _pagination_key: PaginationKey(i),
            _xact_id: TransactionId::from_time_ago(Duration::from_secs(0), i as u16),
            id: format!("row{}", i),
            data: json!({
                "field1": "value",
                "field3": json!({ "input": format!("value{}", i) }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        }];
        fixture.write_object_wal_entries(wal_entries).await;
        segment_id = fixture.run_process_wal().await[0];
        // Compact after each write to create multiple tantivy segments
        fixture.run_compact_wal(segment_id).await;
    }

    let input = OptimizeAllObjectsInput {
        max_iterations: Some(1),
        ..fixture.optimize_all_objects_input()
    };

    // Use custom options with small threshold for testing
    let options = OptimizeAllObjectsOptions {
        optimize_opts: OptimizeObjectOptions {
            merge_opts: MergeOpts {
                target_num_segments: 1,
                use_exact_num_merge_policy: true,
                ..Default::default()
            },
            compact_wal_opts: CompactSegmentWalOptions {
                writer_opts: TantivyIndexWriterOpts {
                    validate_opts: ValidateTantivyIndexOptions {
                        index_writer_validate: true,
                        index_writer_validate_only_deletes: false,
                        ..Default::default()
                    },
                    ..Default::default()
                },
                ..Default::default()
            },
            process_wal_opts: ProcessObjectWalOptions {
                max_rows_per_segment: 10, // Small threshold for testing
                ..Default::default()
            },
            ..Default::default()
        },
        ..DEFAULT_OPTIMIZE_ALL_OBJECTS_OPTS.clone()
    };

    // First loop iteration - should find unfinished work
    let first_result = run_optimization_loop(input.clone(), options.clone())
        .await
        .unwrap();

    // Check that the object now has 1 tantivy segment
    assert_eq!(
        fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
            .segments
            .len(),
        1
    );
    assert_eq!(first_result.num_objects, 1);

    // Second loop iteration - should be a no-op.
    let _second_result = run_optimization_loop(input, options).await.unwrap();

    // Note: The second iteration might still find work if the first didn't complete
    // assert_eq!(second_result.num_objects, 0);

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs::default())
        .await;
}

#[tokio::test]
async fn test_optimize_multiple_objects() {
    let fixture = TestFixture::new();

    // Create 3 objects, each with 1 segment, and 5 transactions. The first two objects
    // should have a max processed transaction id of 5, and the third should have a max
    // processed transaction id of 4 (that way, the "highest" object id has the lowest
    // last processed transaction id).
    let object_ids = [
        FullObjectIdOwned::from_str("project_logs:a").unwrap(),
        FullObjectIdOwned::from_str("project_logs:b").unwrap(),
        FullObjectIdOwned::from_str("project_logs:c").unwrap(),
    ];

    let mut object_segment_ids = [Uuid::default(); 3];

    for (object_idx, object_id) in object_ids.iter().enumerate() {
        let offset = if object_idx == 2 { 0 } else { 1 };
        for i in 0..12 {
            // Increased to 12 rows to exceed 95% of max_rows_per_segment (10)
            let wal_entries = vec![WalEntry {
                _pagination_key: PaginationKey(i + offset),
                _xact_id: TransactionId::from_time_ago(Duration::from_secs(0), (i + offset) as u16),
                id: format!("row{}", i),
                data: json!({
                    "field1": "value",
                    "field3": json!({ "input": format!("value{}", i) }),
                })
                .as_object()
                .unwrap()
                .clone(),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.clone(),
                ..Default::default()
            }];
            fixture
                .config()
                .wal
                .insert(
                    WALScope::ObjectId(
                        object_id.as_ref(),
                        fixture.wal_token_obj(object_id.as_ref()).await,
                    ),
                    wal_entries,
                )
                .await
                .unwrap();
            let res = process_object_wal(
                ProcessObjectWalInput {
                    object_id: object_id.as_ref(),
                    config: fixture.config(),
                },
                Default::default(),
                Default::default(),
            )
            .await
            .unwrap()
            .modified_segment_ids;
            object_segment_ids[object_idx] = res.into_iter().next().unwrap();

            // Compact after each write to create multiple tantivy segments
            fixture
                .run_compact_wal(object_segment_ids[object_idx])
                .await;
        }
    }

    let options = OptimizeAllObjectsOptions {
        batch_size: 10, // Process more objects per iteration
        optimize_opts: OptimizeObjectOptions {
            merge_opts: MergeOpts {
                target_num_segments: 1,
                use_exact_num_merge_policy: true,
                ..Default::default()
            },
            compact_wal_opts: CompactSegmentWalOptions {
                writer_opts: TantivyIndexWriterOpts {
                    validate_opts: ValidateTantivyIndexOptions {
                        index_writer_validate: true,
                        index_writer_validate_only_deletes: false,
                        ..Default::default()
                    },
                    ..Default::default()
                },
                ..Default::default()
            },
            process_wal_opts: ProcessObjectWalOptions {
                max_rows_per_segment: 10, // Small threshold for testing
                ..Default::default()
            },
            ..Default::default()
        },
        ..DEFAULT_OPTIMIZE_ALL_OBJECTS_OPTS.clone()
    };

    // First run - process all objects to completion
    let first_input = OptimizeAllObjectsInput {
        max_iterations: Some(10), // Reasonable limit to avoid infinite loops
        ..fixture.optimize_all_objects_input()
    };

    let first_result = run_optimization_loop(first_input, options.clone())
        .await
        .unwrap();

    // The result should show that we processed objects (the exact count may vary due to the new merge logic)
    // With the new merge thresholds, we might process fewer objects since small segments aren't merged
    assert!(
        first_result.num_objects > 0,
        "Should have processed at least some objects, got {}",
        first_result.num_objects
    );

    // Check optimization results - with small max_rows_per_segment for testing, segments should be merged
    for segment_id in object_segment_ids {
        let segments = fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
            .segments;
        // With our small test threshold (10 rows), the segments with 12 rows each should be merged
        assert_eq!(
            segments.len(),
            1,
            "Segment should have been merged to 1 tantivy segment, got {}",
            segments.len()
        );
    }

    // Second run - should be a no-op since all work is done
    let second_input = OptimizeAllObjectsInput {
        max_iterations: Some(1), // Just one iteration to check
        ..fixture.optimize_all_objects_input()
    };

    let second_result = run_optimization_loop(second_input, options.clone())
        .await
        .unwrap();

    // The second run should find no work since optimization is complete
    assert_eq!(
        second_result.num_objects, 0,
        "Second run should find no work to do, got {}",
        second_result.num_objects
    );

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: None,
            ..Default::default()
        })
        .await;

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs {
            object_ids: Some(&object_ids.iter().map(|id| id.as_ref()).collect::<Vec<_>>()),
            ..Default::default()
        })
        .await;
}

#[tokio::test]
async fn test_ignore_old_segment_object_ids() {
    use crate::{
        optimize_tantivy_index::OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS,
        process_wal::ProcessObjectWalOptions,
    };

    let fixture = TestFixture::new();

    // Create two objects with segments that have old transaction IDs
    let object_ids = [
        FullObjectIdOwned::from_str("project_logs:old_segments").unwrap(),
        FullObjectIdOwned::from_str("project_logs:should_optimize").unwrap(),
    ];

    // Use max_rows_per_segment = 1 to ensure each row creates a new segment
    let process_wal_opts = ProcessObjectWalOptions {
        max_rows_per_segment: 1,
        ..Default::default()
    };

    // First object: create one old segment and one recent segment
    let old_xact_id = TransactionId::from_time_ago(
        Duration::from_secs(OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS + 3600), // 1 hour older than cutoff
        0,
    );
    let recent_xact_id = TransactionId::from_time_ago(
        Duration::from_secs(OPTIMIZATION_WORKER_IGNORE_OLD_SEGMENTS_TIME_AGO_SECS - 3600), // 1 hour newer than cutoff
        0,
    );

    // Track all segment IDs for the first object
    let mut first_object_segments = vec![];

    // Create old segment for first object
    {
        let object_id = &object_ids[0];
        let wal_entries = vec![WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: old_xact_id,
            id: "old_row_1".to_string(),
            root_span_id: "old_root_span_1".to_string(),
            _object_type: object_id.object_type,
            _object_id: object_id.object_id.clone(),
            ..Default::default()
        }];

        fixture
            .config()
            .wal
            .insert(
                WALScope::ObjectId(
                    object_id.as_ref(),
                    fixture.wal_token_obj(object_id.as_ref()).await,
                ),
                wal_entries,
            )
            .await
            .unwrap();
    }

    // Process WAL to create the first segment
    {
        let object_id = &object_ids[0];
        let res = process_object_wal(
            ProcessObjectWalInput {
                object_id: object_id.as_ref(),
                config: fixture.config(),
            },
            Default::default(),
            process_wal_opts.clone(),
        )
        .await
        .unwrap()
        .modified_segment_ids;

        let segment_id = res.into_iter().next().unwrap();
        first_object_segments.push(segment_id);
    }

    // Create recent segment for first object. Needs to be in a separate trace to go in a new
    // segment.
    {
        let object_id = &object_ids[0];
        let wal_entries = vec![WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: recent_xact_id,
            id: "recent_row_1".to_string(),
            root_span_id: "recent_root_span_1".to_string(),
            _object_type: object_id.object_type,
            _object_id: object_id.object_id.clone(),
            ..Default::default()
        }];

        fixture
            .config()
            .wal
            .insert(
                WALScope::ObjectId(
                    object_id.as_ref(),
                    fixture.wal_token_obj(object_id.as_ref()).await,
                ),
                wal_entries,
            )
            .await
            .unwrap();
    }

    // Process WAL again to create the second segment
    {
        let object_id = &object_ids[0];
        let res = process_object_wal(
            ProcessObjectWalInput {
                object_id: object_id.as_ref(),
                config: fixture.config(),
            },
            Default::default(),
            process_wal_opts.clone(),
        )
        .await
        .unwrap()
        .modified_segment_ids;

        let segment_id = res.into_iter().next().unwrap();
        first_object_segments.push(segment_id);
    }

    // Second object: create 3 old segments
    let mut second_object_segments = vec![];
    let object_id = &object_ids[1];
    for i in 0..3 {
        // Write WAL entry
        {
            let wal_entries = vec![WalEntry {
                _pagination_key: PaginationKey(i),
                _xact_id: TransactionId(old_xact_id.0 + (i as u64)),
                id: format!("old_row_{}", i),
                root_span_id: format!("old_root_span_{}", i),
                _object_type: object_id.object_type,
                _object_id: object_id.object_id.clone(),
                ..Default::default()
            }];

            fixture
                .config()
                .wal
                .insert(
                    WALScope::ObjectId(
                        object_id.as_ref(),
                        fixture.wal_token_obj(object_id.as_ref()).await,
                    ),
                    wal_entries,
                )
                .await
                .unwrap();
        }

        // Process WAL to create segment
        {
            let res = process_object_wal(
                ProcessObjectWalInput {
                    object_id: object_id.as_ref(),
                    config: fixture.config(),
                },
                Default::default(),
                process_wal_opts.clone(),
            )
            .await
            .unwrap()
            .modified_segment_ids;

            let segment_id = res.into_iter().next().unwrap();
            second_object_segments.push(segment_id);
        }
    }

    // Verify we created separate segments
    assert_eq!(first_object_segments.len(), 2);
    assert_eq!(second_object_segments.len(), 3);

    // Debug: print segment info
    println!("First object segments: {:?}", first_object_segments);
    println!("Second object segments: {:?}", second_object_segments);
    println!(
        "Old xact_id: {:?}, Recent xact_id: {:?}",
        old_xact_id, recent_xact_id
    );

    // Run optimization with the first object in ignore_old_segment_object_ids
    let mut ignore_set = HashSet::new();
    ignore_set.insert(object_ids[0].clone());

    let input = OptimizeAllObjectsInput {
        max_iterations: Some(1),
        ignore_old_segment_object_ids: ignore_set,
        ..fixture.optimize_all_objects_input()
    };

    let result = run_optimization_loop(input, DEFAULT_OPTIMIZE_ALL_OBJECTS_OPTS.clone())
        .await
        .unwrap();

    assert_eq!(result.num_objects, 2);

    // For first object: only the recent segment should be compacted
    // Check old segment - should not have been compacted
    {
        let segment_meta = fixture
            .config()
            .global_store
            .query_segment_metadatas(&[first_object_segments[0]])
            .await
            .unwrap()
            .remove(0);
        assert!(
            segment_meta.last_compacted_index_meta.is_none(),
            "Old segment of first object should not be compacted"
        );
    }

    // Check recent segment - should be compacted
    {
        let segment_meta = fixture
            .config()
            .global_store
            .query_segment_metadatas(&[first_object_segments[1]])
            .await
            .unwrap()
            .remove(0);
        assert!(
            segment_meta.last_compacted_index_meta.is_some(),
            "Recent segment of first object should be compacted"
        );
        assert_eq!(
            segment_meta.last_compacted_index_meta.unwrap().xact_id,
            recent_xact_id,
        );
    }

    // For second object: all segments should be compacted
    for (i, segment_id) in second_object_segments.iter().enumerate() {
        let segment_meta = fixture
            .config()
            .global_store
            .query_segment_metadatas(&[*segment_id])
            .await
            .unwrap()
            .remove(0);
        assert!(
            segment_meta.last_compacted_index_meta.is_some(),
            "Segment {} of second object should be compacted",
            segment_id
        );
        assert_eq!(
            segment_meta.last_compacted_index_meta.unwrap().xact_id,
            TransactionId(old_xact_id.0 + (i as u64)),
        );
    }

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs::default())
        .await;
}

#[tokio::test]
async fn test_optimize_recompacts_corrupted_index_missing_del() {
    let fixture = TestFixture::new();

    // Write several wal entries to create a segment.
    let segment_ids = {
        let mut wal_entries = vec![];
        for i in 0..10 {
            wal_entries.push(WalEntry {
                id: format!("row{}", i),
                _xact_id: TransactionId(0),
                ..Default::default()
            });
        }
        fixture.write_object_wal_entries(wal_entries).await;
        fixture.run_process_wal().await
    };
    assert_eq!(segment_ids.len(), 1);
    let segment_id = segment_ids[0];
    check_segment_optimization_succeeded(
        &optimize_segments(
            fixture.optimize_object_input(&segment_ids),
            Default::default(),
        )
        .await
        .unwrap(),
    );

    // Check that the segment is compacted
    let orig_segments = fixture
        .fetch_segment_tantivy_metadata(segment_id)
        .await
        .segments
        .into_iter()
        .map(|x| x.segment_id)
        .collect::<HashSet<_>>();
    assert_eq!(orig_segments.len(), 1);

    // Now write a single wal entry update, which should create a delete entry in that segment.
    fixture
        .write_object_wal_entries(vec![WalEntry {
            id: format!("row{}", 0),
            _xact_id: TransactionId(1),
            ..Default::default()
        }])
        .await;
    fixture.run_process_wal().await;
    check_segment_optimization_succeeded(
        &optimize_segments(
            fixture.optimize_object_input(&segment_ids),
            Default::default(),
        )
        .await
        .unwrap(),
    );

    // Now delete all `.del` files in the segment.
    {
        let segment_dir =
            TantivyIndexScope::Segment(segment_id).path(&fixture.config().index.prefix);
        let segment_dir_path =
            object_store::path::Path::from(segment_dir.to_string_lossy().into_owned());
        let mut files_to_delete = fixture.config().index.store.list(Some(&segment_dir_path));
        while let Some(file) = files_to_delete.next().await {
            let file = file.unwrap();
            if file.location.to_string().ends_with(".del") {
                fixture
                    .config()
                    .index
                    .store
                    .delete(&file.location)
                    .await
                    .unwrap();
            }
        }
    }

    // Now if we write a new wal entry and try to compact, it should succeed,
    // but will recompact the segment.
    fixture
        .write_object_wal_entries(vec![WalEntry {
            id: format!("row{}", 10),
            _xact_id: TransactionId(2),
            ..Default::default()
        }])
        .await;
    fixture.run_process_wal().await;
    check_segment_optimization_succeeded(
        &optimize_segments(
            fixture.optimize_object_input(&segment_ids),
            Default::default(),
        )
        .await
        .unwrap(),
    );

    // Check that the set of segments is disjoint from the original set.
    let new_segments = fixture
        .fetch_segment_tantivy_metadata(segment_id)
        .await
        .segments
        .into_iter()
        .map(|x| x.segment_id)
        .collect::<HashSet<_>>();
    assert_eq!(new_segments.len(), 1);
    assert!(orig_segments.intersection(&new_segments).next().is_none());
}

#[test]
fn test_max_age_seconds_filtering() {
    use crate::optimize_tantivy_index::construct_optimization_steps;

    // Create segments with different ages
    let old_xact_id = TransactionId::from_time_ago(Duration::from_secs(7200), 0); // 2 hours ago
    let recent_xact_id = TransactionId::from_time_ago(Duration::from_secs(1800), 0); // 30 minutes ago

    let old_segment_id = Uuid::new_v4();
    let recent_segment_id = Uuid::new_v4();
    let no_age_segment_id = Uuid::new_v4();

    let segments = vec![
        OptimizableSegment {
            segment_id: old_segment_id,
            earliest_uncompacted_xact_id: Some(old_xact_id), // Should be filtered out
            last_processed_xact_id: Some(old_xact_id),
            last_compacted_xact_id: Some(old_xact_id),
            min_pagination_key: PaginationKey(100),
            num_tantivy_segments: 3, // Multiple segments that need merging
            num_rows: 1000000,       // Large enough to qualify for merging
            has_stats: true,
        },
        OptimizableSegment {
            segment_id: recent_segment_id,
            earliest_uncompacted_xact_id: Some(recent_xact_id), // Should be kept
            last_processed_xact_id: Some(recent_xact_id),
            last_compacted_xact_id: None, // No compaction yet, needs compacting
            min_pagination_key: PaginationKey(200),
            num_tantivy_segments: 2,
            num_rows: 500000,
            has_stats: true,
        },
        OptimizableSegment {
            segment_id: no_age_segment_id,
            earliest_uncompacted_xact_id: None, // Should be kept (no age check when None)
            last_processed_xact_id: Some(old_xact_id),
            last_compacted_xact_id: None, // No compaction yet, needs compacting
            min_pagination_key: PaginationKey(300),
            num_tantivy_segments: 1,
            num_rows: 100000,
            has_stats: false, // No stats, needs compacting
        },
    ];

    // Test with max_age of 1 hour (3600 seconds)
    let options_with_age_limit = OptimizationStepOptions {
        max_age_seconds: Some(3600),
        ..Default::default()
    };

    // Filter segments based on age using the same logic as in construct_optimizable_segments
    let filtered_segments: Vec<OptimizableSegment> = segments
        .iter()
        .filter(|segment| {
            if let (Some(xact_id), Some(max_age)) = (
                segment.earliest_uncompacted_xact_id,
                options_with_age_limit.max_age_seconds,
            ) {
                // If the segment's earliest_uncompacted_xact_id is older than max_age, skip it
                !(xact_id.duration_since_now() > Duration::from_secs(max_age))
            } else {
                // If no earliest_uncompacted_xact_id or no max_age, keep the segment
                true
            }
        })
        .cloned()
        .collect();

    let steps_with_age_limit =
        construct_optimization_steps(filtered_segments, options_with_age_limit);

    // Should only have 2 steps - the old segment should be filtered out
    assert_eq!(
        steps_with_age_limit.len(),
        2,
        "Expected 2 steps after filtering out old segment"
    );

    // Verify the old segment was filtered out and the other two remain
    let step_segment_ids: std::collections::HashSet<_> =
        steps_with_age_limit.iter().map(|s| s.segment_id).collect();
    assert!(
        !step_segment_ids.contains(&old_segment_id),
        "Old segment should be filtered out"
    );
    assert!(
        step_segment_ids.contains(&recent_segment_id),
        "Recent segment should be kept"
    );
    assert!(
        step_segment_ids.contains(&no_age_segment_id),
        "Segment with no age should be kept"
    );

    // Test with no max_age limit - all segments should be processed
    let options_no_limit = OptimizationStepOptions {
        max_age_seconds: None,
        ..Default::default()
    };

    let segments_no_filter = vec![
        OptimizableSegment {
            segment_id: old_segment_id,
            earliest_uncompacted_xact_id: Some(old_xact_id),
            last_processed_xact_id: Some(old_xact_id),
            last_compacted_xact_id: Some(old_xact_id),
            min_pagination_key: PaginationKey(100),
            num_tantivy_segments: 3, // Multiple segments that need merging
            num_rows: 1000000,       // Large enough to qualify for merging
            has_stats: true,
        },
        OptimizableSegment {
            segment_id: recent_segment_id,
            earliest_uncompacted_xact_id: Some(recent_xact_id),
            last_processed_xact_id: Some(recent_xact_id),
            last_compacted_xact_id: None, // No compaction yet, needs compacting
            min_pagination_key: PaginationKey(200),
            num_tantivy_segments: 2,
            num_rows: 500000,
            has_stats: true,
        },
        OptimizableSegment {
            segment_id: no_age_segment_id,
            earliest_uncompacted_xact_id: None,
            last_processed_xact_id: Some(old_xact_id),
            last_compacted_xact_id: None, // No compaction yet, needs compacting
            min_pagination_key: PaginationKey(300),
            num_tantivy_segments: 1,
            num_rows: 100000,
            has_stats: false, // No stats, needs compacting
        },
    ];

    let steps_no_limit = construct_optimization_steps(segments_no_filter, options_no_limit);

    // Should have all 3 steps when no age limit is applied
    assert_eq!(
        steps_no_limit.len(),
        3,
        "Expected 3 steps when no age limit is applied"
    );

    let all_step_segment_ids: std::collections::HashSet<_> =
        steps_no_limit.iter().map(|s| s.segment_id).collect();
    assert!(
        all_step_segment_ids.contains(&old_segment_id),
        "All segments should be present without age limit"
    );
    assert!(
        all_step_segment_ids.contains(&recent_segment_id),
        "All segments should be present without age limit"
    );
    assert!(
        all_step_segment_ids.contains(&no_age_segment_id),
        "All segments should be present without age limit"
    );
}

#[tokio::test]
async fn test_small_segments_not_merged() {
    let fixture = TestFixture::new();

    // Create a segment with a small number of rows (much less than the threshold)
    let mut segment_id: Option<Uuid> = None;
    for i in 0..3 {
        let wal_entries = vec![WalEntry {
            _pagination_key: PaginationKey(i),
            _xact_id: TransactionId::from_time_ago(Duration::from_secs(0), i as u16), // Use recent transaction IDs
            id: format!("row{}", i),
            data: json!({
                "field1": "foo",
                "field3": json!({ "input": "bar" }),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        }];
        fixture.write_object_wal_entries(wal_entries).await;
        let res = fixture.run_process_wal().await;
        assert_eq!(res.len(), 1);
        if segment_id.is_none() {
            segment_id = Some(res[0]);
        } else {
            assert_eq!(segment_id, Some(res[0]));
        }

        assert_eq!(
            fixture
                .run_compact_wal(segment_id.unwrap())
                .await
                .num_wal_entries_compacted,
            1
        );
    }

    let segment_id = segment_id.unwrap();
    let segment_ids = vec![segment_id];

    // Verify the segment has multiple tantivy segments that need merging
    assert!(
        fixture
            .fetch_segment_tantivy_metadata(segment_id)
            .await
            .segments
            .len()
            > 1
    );

    // Check that optimization steps include compaction but NOT merging for small segments
    // Use hybrid merge policy (not exact) to test the new merge logic
    let optimize_options = OptimizeObjectOptions {
        merge_opts: MergeOpts {
            target_num_segments: 1,
            use_exact_num_merge_policy: false, // Use hybrid policy to test new logic
            ..Default::default()
        },
        process_wal_opts: ProcessObjectWalOptions {
            max_rows_per_segment: 100000, // Use default large threshold
            ..Default::default()
        },
        ..Default::default()
    };

    let output = optimize_segments(
        OptimizeObjectInput {
            dry_run: true,
            ..fixture.optimize_object_input(&segment_ids)
        },
        optimize_options.clone(),
    )
    .await
    .unwrap();

    // With the new merge logic, small segments should not generate any optimization steps
    // The expected behavior is that small, recent segments should not be optimized
    if !output.planned.is_empty() {
        // If there are steps, verify they don't include merging
        let step = output.planned.iter().find(|s| s.segment_id == segment_id);

        if let Some(step) = step {
            assert!(
                !step.merge,
                "Small segment should not be scheduled for merging"
            );
        }
    }

    // Now run optimization to see what actually happens
    let output = optimize_segments(
        fixture.optimize_object_input(&segment_ids),
        optimize_options,
    )
    .await
    .unwrap();

    check_segment_optimization_succeeded(&output);

    // After optimization, the segment should still have multiple tantivy segments
    // because merging was skipped due to the small size
    let post_optimization_segments = fixture
        .fetch_segment_tantivy_metadata(segment_id)
        .await
        .segments;

    // The segment should still have multiple tantivy segments since merge was skipped
    // (or possibly the same number if no optimization steps were performed)
    assert!(
        post_optimization_segments.len() >= 3,
        "Small segment should not have been merged, expected >= 3 tantivy segments but got {}",
        post_optimization_segments.len()
    );

    fixture
        .basic_fixture
        .validate_vacuum(ValidateVacuumArgs::default())
        .await;
}

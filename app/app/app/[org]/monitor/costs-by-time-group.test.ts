import { describe, expect, test } from "vitest";
import { crunchCostsByTimeGroup } from "./costs-by-time-group";
import { CARD_GROUP_BY_PREFIX } from "./card-config/monitor-cards.constants";

type ModelSpec = {
  format: "openai" | "google" | "anthropic" | "js" | "window";
  flavor: "chat" | "completion";
  input_cost_per_mil_tokens?: number | null | undefined;
  output_cost_per_mil_tokens?: number | null | undefined;
  input_cache_read_cost_per_mil_tokens?: number | null | undefined;
  input_cache_write_cost_per_mil_tokens?: number | null | undefined;
  displayName?: string | null | undefined;
};

const allAvailableModels: { [name: string]: ModelSpec } = {
  "gpt-4": {
    format: "openai",
    flavor: "chat",
    input_cost_per_mil_tokens: 30,
    output_cost_per_mil_tokens: 60,
    input_cache_read_cost_per_mil_tokens: 15,
    input_cache_write_cost_per_mil_tokens: 37.5,
    displayName: "GPT 4",
  },
  "gpt-4o": {
    format: "openai",
    flavor: "chat",
    input_cost_per_mil_tokens: 2.5,
    output_cost_per_mil_tokens: 10,
    input_cache_read_cost_per_mil_tokens: 1.25,
    input_cache_write_cost_per_mil_tokens: 3.125,
    displayName: "GPT 4o",
  },
  "gpt-3.5-turbo": {
    format: "openai",
    flavor: "chat",
    input_cost_per_mil_tokens: 0.5,
    output_cost_per_mil_tokens: 1.5,
    input_cache_read_cost_per_mil_tokens: 0.25,
    input_cache_write_cost_per_mil_tokens: 0.625,
    displayName: "GPT 3.5T",
  },
};

const EPOCH_1 = 1731600000000;
const EPOCH_2 = 1751600000000;

const costData = [
  {
    model: "gpt-3.5-turbo",
    time: EPOCH_1,
    promptUncachedTokens: BigInt(696),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(294),
    count: BigInt(1),
  },
  {
    model: "gpt-4o",
    time: EPOCH_1,
    promptUncachedTokens: BigInt(1059738),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(301085),
    count: BigInt(1206),
  },
  {
    model: "gpt-4",
    time: EPOCH_1,
    promptUncachedTokens: BigInt(63724),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(27403),
    count: BigInt(91),
  },
  {
    model: "gpt-3.5-turbo",
    time: EPOCH_2,
    promptUncachedTokens: BigInt(696),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(294),
    count: BigInt(1),
  },
  {
    model: "gpt-4o",
    time: EPOCH_2,
    promptUncachedTokens: BigInt(1059738),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(301085),
    count: BigInt(1205),
  },
  {
    model: "gpt-4",
    time: EPOCH_2,
    promptUncachedTokens: BigInt(63724),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(27403),
    count: BigInt(91),
  },
];

describe("costs by time group", () => {
  test("crunchCostsByTime", () => {
    const data = crunchCostsByTimeGroup({ costData, allAvailableModels });
    expect(data).toEqual([
      {
        time: EPOCH_1,
        promptUncachedTokens: 4.561413,
        promptCachedTokens: 0,
        promptCacheCreationTokens: 0,
        completionTokens: 4.655471,
        count: BigInt(1298),
      },
      {
        time: EPOCH_2,
        promptUncachedTokens: 4.561413,
        promptCachedTokens: 0,
        promptCacheCreationTokens: 0,
        completionTokens: 4.655471,
        count: BigInt(1297),
      },
    ]);
  });
});

const costDataGroups = [
  {
    model: "gpt-3.5-turbo",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "apple",
    promptUncachedTokens: BigInt(696),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(294),
    count: BigInt(1),
  },
  {
    model: "gpt-4o",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "apple",
    promptUncachedTokens: BigInt(1059738),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(301085),
    count: BigInt(1206),
  },
  {
    model: "gpt-4",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "apple",
    promptUncachedTokens: BigInt(63724),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(27403),
    count: BigInt(91),
  },
  {
    model: "gpt-3.5-turbo",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "banana",
    promptUncachedTokens: BigInt(696),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(294),
    count: BigInt(1),
  },
  {
    model: "gpt-4o",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "banana",
    promptUncachedTokens: BigInt(1059738),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(301085),
    count: BigInt(1205),
  },
  {
    model: "gpt-4",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "banana",
    promptUncachedTokens: BigInt(63724),
    promptCachedTokens: BigInt(0),
    promptCacheCreationTokens: BigInt(0),
    completionTokens: BigInt(27403),
    count: BigInt(91),
  },
  {
    model: "gpt-4",
    time: EPOCH_1,
    [`${CARD_GROUP_BY_PREFIX}fruit`]: "banana",
    [`${CARD_GROUP_BY_PREFIX}animal`]: "dog",
    promptUncachedTokens: BigInt(1e6),
    promptCachedTokens: BigInt(1e6),
    promptCacheCreationTokens: BigInt(1e6),
    completionTokens: BigInt(1e6),
    count: BigInt(37),
  },
];

describe("costs by time group", () => {
  test("crunchCostsByTime", () => {
    const data = crunchCostsByTimeGroup({
      costData: costDataGroups,
      allAvailableModels,
    });
    expect(data).toEqual([
      {
        time: EPOCH_1,
        [`${CARD_GROUP_BY_PREFIX}fruit`]: "apple",
        promptUncachedTokens: 4.561413,
        promptCachedTokens: 0,
        promptCacheCreationTokens: 0,
        completionTokens: 4.655471,
        count: BigInt(1298),
      },
      {
        time: EPOCH_1,
        [`${CARD_GROUP_BY_PREFIX}fruit`]: "banana",
        promptUncachedTokens: 4.561413,
        promptCachedTokens: 0,
        promptCacheCreationTokens: 0,
        completionTokens: 4.655471,
        count: BigInt(1297),
      },
      {
        time: EPOCH_1,
        [`${CARD_GROUP_BY_PREFIX}fruit`]: "banana",
        [`${CARD_GROUP_BY_PREFIX}animal`]: "dog",
        promptUncachedTokens: 30,
        promptCachedTokens: 15,
        promptCacheCreationTokens: 37.5,
        completionTokens: 60,
        count: BigInt(37),
      },
    ]);
  });
});

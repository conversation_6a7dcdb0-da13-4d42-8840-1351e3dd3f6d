[{"error": null, "query": "unpivot: scores AS (score, value) | select: id, score, value", "result_rows": [{"id": "asf-dfa", "score": "foo", "value": 1}, {"id": "dfa-asf", "score": "bar", "value": 0.5}, {"id": "dfa-asf", "score": "foo", "value": 0}, {"id": "uuid3", "score": "foo", "value": 0.25}, {"id": "uuid5", "score": "foo", "value": 0.1}, {"id": "uuid6", "score": "baz", "value": 0.75}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | select: score", "result_rows": [null, {"score": "bar"}, {"score": "baz"}, {"score": "foo"}, {"score": "foo"}, {"score": "foo"}, {"score": "foo"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | select: value", "result_rows": [null, {"value": 0.1}, {"value": 0.25}, {"value": 0.5}, {"value": 0.75}, {"value": 0}, {"value": 1}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | select: id", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid5"}, {"id": "uuid6"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: scores AS (score, value) | dimensions: score | measures: sum(value)", "result_rows": [null, {"score": "bar", "sum(value)": 0.5}, {"score": "baz", "sum(value)": 0.75}, {"score": "foo", "sum(value)": 1.35}], "skip": false}, {"error": null, "query": "unpivot: tags AS tag | select: tag", "result_rows": [null, null, null, {"tag": "a"}, {"tag": "a"}, {"tag": "b"}, {"tag": "c"}, {"tag": "d"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: tags AS tag | dimensions: tag | measures: count(1)", "result_rows": [{"count(1)": 1, "tag": "b"}, {"count(1)": 1, "tag": "c"}, {"count(1)": 1, "tag": "d"}, {"count(1)": 2, "tag": "a"}, {"count(1)": 3}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 0\n*/\nunpivot: scores AS (score, value), tags AS tag | select: score, tag", "result_rows": [null, {"score": "bar", "tag": "a"}, {"score": "bar", "tag": "c"}, {"score": "baz"}, {"score": "foo", "tag": "a"}, {"score": "foo", "tag": "a"}, {"score": "foo", "tag": "b"}, {"score": "foo", "tag": "c"}, {"score": "foo", "tag": "d"}, {"score": "foo"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 0\n*/\nunpivot: scores AS (score, value), tags AS tag | select: score", "result_rows": [null, {"score": "bar"}, {"score": "baz"}, {"score": "foo"}, {"score": "foo"}, {"score": "foo"}, {"score": "foo"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 0\n*/\nunpivot: scores AS (score, value), tags AS tag | dimensions: score, tag | measures: sum(value), count(1)", "result_rows": [{"count(1)": 1, "score": "bar", "sum(value)": 0.5, "tag": "a"}, {"count(1)": 1, "score": "bar", "sum(value)": 0.5, "tag": "c"}, {"count(1)": 1, "score": "baz", "sum(value)": 0.75}, {"count(1)": 1, "score": "foo", "sum(value)": 0, "tag": "c"}, {"count(1)": 1, "score": "foo", "sum(value)": 0.1}, {"count(1)": 1, "score": "foo", "sum(value)": 0.25, "tag": "d"}, {"count(1)": 1, "score": "foo", "sum(value)": 1, "tag": "b"}, {"count(1)": 1}, {"count(1)": 2, "score": "foo", "sum(value)": 1, "tag": "a"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: scores AS (score, value) | measures: count(value)", "result_rows": [{"count(value)": 6}], "skip": false}, {"error": null, "query": "-- Test exists optimization\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: scores AS (score, value) | dimensions: score | measures: count(value)", "result_rows": [{"count(value)": 0}, {"count(value)": 1, "score": "bar"}, {"count(value)": 1, "score": "baz"}, {"count(value)": 4, "score": "foo"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: scores as (score, value) | dimensions: id, score | measures: avg(value)", "result_rows": [{"avg(value)": 0, "id": "dfa-asf", "score": "foo"}, {"avg(value)": 0.1, "id": "uuid5", "score": "foo"}, {"avg(value)": 0.25, "id": "uuid3", "score": "foo"}, {"avg(value)": 0.5, "id": "dfa-asf", "score": "bar"}, {"avg(value)": 0.75, "id": "uuid6", "score": "baz"}, {"avg(value)": 1, "id": "asf-dfa", "score": "foo"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: scores as (score, value) | dimensions: id, score | measures: avg(value + metadata.float)", "result_rows": [{"avg(value + metadata.float)": 0.25, "id": "uuid3", "score": "foo"}, {"avg(value + metadata.float)": 13.2, "id": "uuid5", "score": "foo"}, {"avg(value + metadata.float)": 2.5, "id": "asf-dfa", "score": "foo"}, {"avg(value + metadata.float)": 300.2, "id": "dfa-asf", "score": "foo"}, {"avg(value + metadata.float)": 300.7, "id": "dfa-asf", "score": "bar"}, {"id": "uuid6", "score": "baz"}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 1\n*/\nunpivot: scores as (score, value) | dimensions: score, value | measures: sum(counts.foo)", "result_rows": [null, {"score": "bar", "sum(counts.foo)": 3, "value": 0.5}, {"score": "baz", "value": 0.75}, {"score": "foo", "sum(counts.foo)": -7, "value": 0.25}, {"score": "foo", "sum(counts.foo)": 10, "value": 1}, {"score": "foo", "sum(counts.foo)": 3, "value": 0}, {"score": "foo", "value": 0.1}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Generating unpivot rows\")] | length == 0\n*/\nunpivot: scores as (score, value) | dimensions: score, value, id | measures: sum(counts.foo)", "result_rows": [{"id": "asf-dfa", "score": "foo", "sum(counts.foo)": 10, "value": 1}, {"id": "dfa-asf", "score": "bar", "sum(counts.foo)": 3, "value": 0.5}, {"id": "dfa-asf", "score": "foo", "sum(counts.foo)": 3, "value": 0}, {"id": "uuid3", "score": "foo", "sum(counts.foo)": -7, "value": 0.25}, {"id": "uuid5", "score": "foo", "value": 0.1}, {"id": "uuid6", "score": "baz", "value": 0.75}, {"id": "uuid7"}], "skip": false}, {"error": null, "query": "unpivot: scores.noexist AS (score, value) | dimensions: score | measures: count(value)", "result_rows": [{"count(value)": 0}], "skip": false}, {"error": "Internal error: 'Expected scores_foo to be an object while unpivoting'", "query": "unpivot: scores.foo as (score, value) | dimensions: score | measures: count(value)", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Unpivoting an array requires specifying a single alias ... at line 1, column 10:\n1:  unpivot: tags as (score, value) | dimensions: score\n             ^^^^", "query": "unpivot: tags as (score, value) | dimensions: score", "result_rows": [], "skip": false}]
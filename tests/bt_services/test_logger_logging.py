import dataclasses
import json
import unittest

import braintrust
from braintrust_local.api_db_util import get_object_json
from parameterized import parameterized
from pydantic import BaseModel, Field, create_model

from tests.braintrust_app_test_base import LOCAL_API_URL, LOCAL_APP_URL, BraintrustAppTestBase
from tests.bt_services.test_sdk_bt_json import ModelA, ModelB, ObjectA, ObjectB


class LazyLoginTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUpFull(skip_login=True)
        self.logger = braintrust.init_logger(
            org_name=self.org_name, app_url=LOCAL_APP_URL, api_key=self.org_api_key, force_login=True
        )

    def test_lazy_login(self):
        # Since we only login after flushing, we should not have any state
        # populated until after flushing.
        self.assertIsNone(braintrust.org_id())
        self.logger.log(id="my_id", input="hello", output="world")
        self.assertIsNone(braintrust.org_id())
        self.logger.flush()
        self.assertEqual(self.org_id, braintrust.org_id())
        rows = get_object_json("project_logs", self.logger.id)
        self.assertEqual(1, len(rows))
        self.assertEqual("my_id", rows[0]["id"])
        self.assertEqual("hello", rows[0]["input"])
        self.assertEqual("world", rows[0]["output"])


class LoggerLoggingTest(BraintrustAppTestBase):
    def test_same_id_different_projects_cross_project_query(self):
        logger = braintrust.init_logger(project="one")
        project_one_id = logger.project.id
        logger.log(id="test", input="hello")
        logger.flush()

        logger = braintrust.init_logger(project="two")
        project_two_id = logger.project.id
        logger.log(id="test", input="goodbye")
        logger.flush()

        project_one_rows = get_object_json("project_logs", project_one_id)
        self.assertEqual(1, len(project_one_rows))
        self.assertEqual("test", project_one_rows[0]["id"])
        self.assertEqual("hello", project_one_rows[0]["input"])

        project_two_rows = get_object_json("project_logs", project_two_id)
        self.assertEqual(1, len(project_two_rows))
        self.assertEqual("test", project_two_rows[0]["id"])
        self.assertEqual("goodbye", project_two_rows[0]["input"])

        all_project_rows = (
            braintrust.api_conn()
            .post(
                "/btql",
                json=dict(
                    query={
                        "from": {
                            "op": "function",
                            "name": {"op": "ident", "name": ["project_logs"]},
                            "args": [
                                {"op": "literal", "value": project_one_id},
                                {"op": "literal", "value": project_two_id},
                            ],
                        },
                        "select": [{"op": "star"}],
                    },
                    fmt="json",
                    _testing_only_skip_limit_checks=True,
                ),
            )
            .json()["data"]
        )
        self.assertEqual(2, len(all_project_rows))
        self.assertEqual("test", all_project_rows[0]["id"])
        self.assertEqual("test", all_project_rows[1]["id"])
        self.assertEqual({"hello", "goodbye"}, set(row["input"] for row in all_project_rows))

    def test_span_nesting(self):
        @braintrust.traced(notrace_io=True)
        def foo():
            braintrust.current_span().log(input="foo_input", output="foo_output")

        logger = braintrust.init_logger()
        with logger.start_span() as span:
            span.log(input="root_input", output="root_output")
            foo()
        logger.flush()

        rows = get_object_json("project_logs", logger.id)
        self.assertEqual(2, len(rows))
        rows.sort(key=lambda r: r["input"])
        self.assertEqual("foo_input", rows[0]["input"])
        self.assertEqual("foo_output", rows[0]["output"])
        self.assertNotEqual(rows[0]["span_id"], rows[0]["root_span_id"])
        self.assertEqual(rows[1]["span_id"], rows[0]["root_span_id"])
        self.assertEqual("root_input", rows[1]["input"])
        self.assertEqual("root_output", rows[1]["output"])
        self.assertEqual(rows[1]["span_id"], rows[1]["root_span_id"])

    def test_json_serialize(self):
        model = ModelB(
            id="123",
            name="name",
            child=ModelA(fruit="apple", color="red", num=5, metadata={"key": "value"}),
            metadata={"key": "value"},
        )
        obj = ObjectB(
            id="123",
            child=ObjectA(id="456", foo=["bar", "baz"], metadata={"key": "value"}),
        )
        expected = [dict(foo=1, bar="baz"), "hello", 5, None, True, False]

        x = ModelA(fruit="apple", color="red", num=5, metadata={"key": "value"})
        y = ObjectA(id="456", foo=["bar", "baz"], metadata={"key": "value"})

        @braintrust.traced()
        def foo(x, y=None):
            return model, obj

        logger = braintrust.init_logger()
        with logger.start_span() as span:
            span.log(
                input=model,
                output=obj,
                expected=expected,
                metadata={"a": model, "b": obj},
            )
            foo(x, y=y)
        logger.flush()

        rows = get_object_json("project_logs", logger.id)
        self.assertEqual(2, len(rows))
        rows.sort(key=lambda r: json.dumps(r["input"]))

        self.assertEqual(model.model_dump(), rows[0]["input"])
        self.assertEqual(dataclasses.asdict(obj), rows[0]["output"])
        self.assertEqual(expected, rows[0]["expected"])
        self.assertEqual(dict(a=model.model_dump(), b=dataclasses.asdict(obj)), rows[0]["metadata"])
        self.assertEqual(dict(x=x.model_dump(), y=dataclasses.asdict(y)), rows[1]["input"])
        self.assertEqual([model.model_dump(), dataclasses.asdict(obj)], rows[1]["output"])

    def test_invalid_character(self):
        logger = braintrust.init_logger(project="weird_chars")
        logger.log(id="row0", input="\u0000", output={"\u0000": "1"})
        logger.log(id="row1", input="\\u0000", output={"\\u0000": "1"})
        logger.flush()
        rows = {r["id"]: r for r in get_object_json("project_logs", logger.project.id)}
        self.assertEqual(2, len(rows))
        row = rows["row0"]
        self.assertEqual("", row["input"])
        self.assertEqual({"": "1"}, row["output"])
        row = rows["row1"]
        self.assertEqual("\\u0000", row["input"])
        self.assertEqual({"\\u0000": "1"}, row["output"])

    def test_unicode_surrogate_error(self):
        # Create a string with an invalid surrogate pair - a low surrogate without a high surrogate
        invalid_string = "\udc00"  # Low surrogate
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True
            project_logger = braintrust.init_logger(project="unicode_surrogate")
            project_logger.log(id="row0", input=invalid_string)
            # The flush should succeed having replaced the invalid surrogate
            # pair with a placeholder.
            project_logger.flush()
            rows = {r["id"]: r for r in get_object_json("project_logs", project_logger.id)}
            self.assertEqual(1, len(rows))
            row = rows["row0"]
            self.assertEqual("�", row["input"])

    def test_init_logger_with_project_id(self):
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="p")).json()
        logger = braintrust.init_logger(project_id=project["id"])
        logger.log(input="foo", output="bar")
        braintrust.flush()
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(1, len(rows))
        row = rows[0]
        self.assertEqual("foo", row["input"])
        self.assertEqual("bar", row["output"])

    def test_span_attributes_set(self):
        logger = braintrust.init_logger(project="span_attributes")

        span = logger.start_span(name="name1")
        span.set_attributes(name="name2")
        span.log(input="row1")
        span.end()
        span.flush()

        span = logger.start_span(name="name1")
        span.log(input="row2")
        span.set_attributes(name="name3")
        span.end()
        span.flush()

        span = logger.start_span(name="name1")
        span.log(input="row3")
        span.flush()
        span.set_attributes(name="name4")
        span.end()

        logger.flush()

        rows = get_object_json("project_logs", logger.project.id)

        self.assertEqual(3, len(rows))
        names = set([row["span_attributes"]["name"] for row in rows])
        self.assertEqual({"name2", "name3", "name4"}, names)

    def test_load_prompt_cached(self):
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "load-prompt-cached"}).json()

        self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/prompt",
            json={
                "project_id": project["id"],
                "prompt_data": {
                    "prompt": {
                        "type": "chat",
                        "messages": [
                            {"role": "system", "content": "You are a calculator. Return results in JSON"},
                            {"role": "user", "content": "{{formula}}"},
                        ],
                    },
                    "options": {"params": {"response_format": {"type": "json_object"}}},
                },
                "name": "calculator",
                "slug": "calculator",
            },
        )

        original = braintrust.load_prompt(project=project["name"], slug="calculator")._lazy_metadata.get()

        def raise_error(*args, **kwargs):
            raise RuntimeError("fail")

        braintrust.api_conn().get_json = raise_error

        # Should load from cache.
        cached = braintrust.load_prompt(project=project["name"], slug="calculator")._lazy_metadata.get()

        self.assertEqual(original, cached)

    def test_load_prompt_by_id_cached(self):
        """Test that loading prompts by ID uses cache fallback when server fails"""
        project = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/project", json={"name": "load-prompt-by-id-cached"}
        ).json()

        prompt_response = self.run_request(
            "post",
            f"{LOCAL_API_URL}/v1/prompt",
            json={
                "project_id": project["id"],
                "prompt_data": {
                    "prompt": {
                        "type": "chat",
                        "messages": [
                            {"role": "system", "content": "You are a math tutor."},
                            {"role": "user", "content": "What is {{num1}} + {{num2}}?"},
                        ],
                    },
                    "options": {"model": "gpt-3.5-turbo"},
                },
                "name": "math-tutor",
                "slug": "math-tutor",
            },
        ).json()

        prompt_id = prompt_response["id"]

        # Load prompt by ID to populate cache
        original = braintrust.load_prompt(id=prompt_id)._lazy_metadata.get()
        self.assertEqual(original.id, prompt_id)
        self.assertEqual(original.name, "math-tutor")

        # Mock API failure
        original_get_json = braintrust.api_conn().get_json

        def raise_error(*args, **kwargs):
            raise RuntimeError("Server unavailable")

        braintrust.api_conn().get_json = raise_error

        try:
            # Should load from cache when server fails
            cached = braintrust.load_prompt(id=prompt_id)._lazy_metadata.get()
            self.assertEqual(original.as_dict(), cached.as_dict())

            # Test that non-cached ID fails appropriately
            with self.assertRaises(ValueError) as cm:
                braintrust.load_prompt(id="non-cached-id-12345")._lazy_metadata.get()
            self.assertIn("not found", str(cm.exception))
            self.assertIn("cache", str(cm.exception))
        finally:
            # Restore original function
            braintrust.api_conn().get_json = original_get_json

    @parameterized.expand(
        [
            (data_url,)
            for data_url in [
                # Only the image is a valid URL, but this just lets us test the mime types themselves.
                "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=",
                "data:application/pdf;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=",
                "data:video/mp4;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=",
            ]
        ]
    )
    def test_base64_attachment_in_messages(self, data_url):
        # This test depends on attachments being set up.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("")

        """Test that base64 data URLs in message arrays are converted to attachment references."""
        logger = braintrust.init_logger(project="base64_attachments")

        # Log a message array with base64 data URL
        messages_payload = [
            {
                "content": [
                    {"text": "What do you see here?", "type": "text"},
                    {"image_url": {"url": data_url}, "type": "image_url"},
                ],
                "role": "user",
            }
        ]

        logger.log(id="messages_test", input=messages_payload, output="I see a small icon or image.")
        logger.flush()

        # Fetch the logged data
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(1, len(rows))

        row = rows[0]
        self.assertEqual("messages_test", row["id"])

        # Verify that the base64 data URL has been converted to an attachment reference
        input_data = row["input"]
        self.assertIsInstance(input_data, list)
        self.assertEqual(len(input_data), 1)

        message = input_data[0]
        self.assertEqual(message["role"], "user")
        self.assertIsInstance(message["content"], list)
        self.assertEqual(len(message["content"]), 2)

        # Check text content is unchanged
        text_content = message["content"][0]
        self.assertEqual(text_content["type"], "text")
        self.assertEqual(text_content["text"], "What do you see here?")

        # Check that image_url has been converted to attachment reference
        image_content = message["content"][1]
        self.assertEqual(image_content["type"], "image_url")
        image_url = image_content["image_url"]["url"]

        # Should be converted to an attachment reference, not the original data URL
        self.assertNotEqual(image_url, data_url)
        # Should start with some attachment reference format
        self.assertTrue(image_url["type"] == "braintrust_attachment")

    def test_base64_attachment_standalone(self):
        # This test depends on attachments being set up.
        if BraintrustAppTestBase.skip_s3():
            raise unittest.SkipTest("")

        """Test that base64 data URLs outside of message arrays are converted to attachment references."""
        logger = braintrust.init_logger(project="base64_standalone")

        # Sample base64 data URL (same as in the example)
        data_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII="

        # Test various scenarios with standalone base64 URLs
        test_cases = [
            # Simple string with data URL
            {"input": data_url, "name": "simple_data_url"},
            # Object with data URL
            {"input": {"image": data_url, "prompt": "Describe this image"}, "name": "object_with_data_url"},
            # Array with data URL
            {"input": [data_url, "What is this?"], "name": "array_with_data_url"},
            # Nested structure with data URL
            {"input": {"request": {"image_url": data_url}, "type": "vision"}, "name": "nested_data_url"},
        ]

        for i, case in enumerate(test_cases):
            logger.log(id=f"standalone_test_{i}", input=case["input"], output=f"Response for {case['name']}")

        logger.flush()

        # Fetch the logged data
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(len(test_cases), len(rows))

        # Sort rows by id for consistent testing
        rows.sort(key=lambda r: r["id"])

        for i, (row, case) in enumerate(zip(rows, test_cases)):
            self.assertEqual(f"standalone_test_{i}", row["id"])

            # Check that data URLs have been converted to attachment references
            input_data = row["input"]

            if case["name"] == "simple_data_url":
                # Should be converted to attachment reference
                self.assertNotEqual(input_data, data_url)
                self.assertTrue(input_data["type"] == "braintrust_attachment")

            elif case["name"] == "object_with_data_url":
                self.assertIsInstance(input_data, dict)
                self.assertIn("image", input_data)
                self.assertEqual(input_data["prompt"], "Describe this image")
                # The image field should be converted to attachment reference
                self.assertNotEqual(input_data["image"], data_url)
                self.assertTrue(input_data["image"]["type"] == "braintrust_attachment")

            elif case["name"] == "array_with_data_url":
                self.assertIsInstance(input_data, list)
                self.assertEqual(len(input_data), 2)
                # First element should be converted to attachment reference
                self.assertNotEqual(input_data[0], data_url)
                self.assertTrue(input_data[0]["type"] == "braintrust_attachment")
                # Second element should be unchanged
                self.assertEqual(input_data[1], "What is this?")

            elif case["name"] == "nested_data_url":
                self.assertIsInstance(input_data, dict)
                self.assertIn("request", input_data)
                self.assertEqual(input_data["type"], "vision")
                # The nested image_url should be converted to attachment reference
                self.assertNotEqual(input_data["request"]["image_url"], data_url)
                self.assertTrue(input_data["request"]["image_url"]["type"] == "braintrust_attachment")

    def test_invalid_base64(self):
        """Test that invalid base64 data URLs are not converted and remain unchanged."""

        logger = braintrust.init_logger(project="base64_truncation")

        data_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+[TRUNCATED]"

        logger.log(id="truncation_test", input=data_url, output="Response to base64 image")
        logger.flush()

        # Fetch the logged data
        rows = get_object_json("project_logs", logger.project.id)
        self.assertEqual(1, len(rows))

        row = rows[0]
        self.assertEqual("truncation_test", row["id"])

        # Verify that the base64 data URL has been truncated
        input_data = row["input"]
        self.assertIsInstance(input_data, str)
        self.assertTrue(input_data.endswith("[TRUNCATED]"))
        self.assertEqual(input_data, data_url)
        self.assertTrue(input_data.startswith("data:image/png;base64,"))

import {
  createContext,
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type DML } from "#/utils/mutable-object";
import { useContext } from "react";
import { useDuckConn } from "#/utils/duckdb";
import { performUpsert } from "#/utils/duckdb";
import { createDataset as createDatasetApi } from "#/app/app/[org]/p/[project]/datasets/[dataset]/createDataset";
import { BN } from "apache-arrow/util/bn";
import { type SummaryBreakdownData } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type ComparisonExperimentSpanSummary } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/useExperiment";
import { z } from "zod";
import { sessionFetchProps, useSessionToken } from "#/utils/auth/session-token";
import { useOrg, useUser } from "#/utils/user";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  type InlineTaskDefinition,
  parseInlineTaskDefinition,
} from "./tools/edit-task";
import { type TaskFunctionDefinition } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/clientpage";
import { useAtomValue } from "jotai";
import { isPlaygroundRunningAtom } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/atoms";
import { type PlayXRunPromptsFn } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/playx";
import {
  safeDeserializePlainStringAsJSON,
  safeDeserializeUnknown,
} from "#/utils/object";
import { OPTIMIZATION_PROJECT_ID } from "#/utils/constants";
import { type AsyncDuckDBConnection } from "@duckdb/duckdb-wasm";
import { singleQuote } from "#/utils/sql-utils";
import {
  IS_MERGE_FIELD,
  isEmpty,
  MERGE_PATHS_FIELD,
  OBJECT_DELETE_FIELD,
  SpanComponentsV3,
  SpanObjectTypeV3,
  type TransactionId,
} from "braintrust/util";
import { newId } from "#/utils/btapi/btapi";
import { shouldLog } from "#/utils/observability";
import {
  applyEdits,
  type EditTaskToolParameters,
  type GetResultsToolParameters,
  type GetResultsToolResult,
  ToolManager,
  type RerunTaskToolParameters,
  type RerunTaskToolResult,
  type EditDataToolParameters,
  type ContinueExecutionToolParameters,
  type GetSummaryToolResult,
  type ScoreSummary,
  isInternalMetric,
  roundNumber,
  type EditTaskToolResult,
  type EditDataToolResult,
  type ContinueExecutionToolResult,
  type EditScorersParams,
  getGlobalScorers,
  type EditScorersToolResult,
  type GetAvailableScorersResult,
  type CreateCodeScorerParams,
  type CreateLLMScorerParams,
  type CreateCodeScorerToolResult,
  btqlQueryToolResultSchema,
  inferSchemaToolResultSchema,
  type BTQLQueryToolParameters,
  type BTQLQueryToolResult,
  type InferSchemaToolResult,
  type InferSchemaToolParameters,
  type CreateLLMScorerToolResult,
  type GetAvailableScorersParams,
  type RunBtqlToolParameters,
  type RunBtqlToolResult,
  type GetDataSourceToolParameters,
  type GetDataSourceToolResult,
  type PageKey,
  getToolsForPage,
  docsToolResultSchema,
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  //  type EditBTQLToolParameters,
  //  type EditBTQLToolResult,
} from "@braintrust/local/optimization/tools";
import { type SavedScorer } from "#/utils/scorers";
import isEqual from "lodash.isequal";
import {
  type Search,
  type ClauseChecker,
  noopChecker,
} from "#/utils/search/search";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { type TimeRangeFilter } from "#/utils/view/use-view";
import { timeRangeFilterToBtqlExprs } from "#/utils/btql/time-range-filter";

export type OptionalChatContextParams = Partial<ChatContextParams>;

export interface ConfirmationData<Opts, Result> {
  onConfirm: (opts?: Opts) => Result;
  onCancel: (reason?: string, opts?: Opts) => Error;
}

export interface TaskEditConfirmationData
  extends ConfirmationData<{ continueChat?: boolean }, EditTaskToolResult> {
  type: "edit_task";
  index: number;
  originalDefinition: object;
  newDefinition: object;
}

export interface DatasetEditConfirmationData
  extends ConfirmationData<{ continueChat?: boolean }, EditDataToolResult> {
  type: "edit_dataset";
  edits: EditDataToolParameters["edits"];
  existing: Record<
    string,
    {
      id: string;
      input: unknown;
      expected: unknown;
      metadata: unknown;
    }
  >;
}

export interface EditScorersConfirmationData
  extends ConfirmationData<{ continueChat?: boolean }, EditScorersToolResult> {
  type: "edit_scorers";
  scorers: EditScorersParams["scorers"];
  currentScorers: SavedScorer[];
  availableScorers: Record<string, UIFunction>;
}

export interface CreateLLMScorerConfirmationData
  extends ConfirmationData<
    { continueChat?: boolean },
    CreateLLMScorerToolResult
  > {
  type: "create_llm_scorer";
  scorer: Omit<UIFunction, "_xact_id">;
  existingScorer: UIFunction | undefined;
}

export interface CreateCodeScorerConfirmationData
  extends ConfirmationData<
    { continueChat?: boolean },
    CreateCodeScorerToolResult
  > {
  type: "create_code_scorer";
  scorer: CreateCodeScorerParams;
  existingScorer: UIFunction | undefined;
}
export interface UserConsentConfirmationData
  extends ConfirmationData<
    { continueWithoutConsent: boolean },
    ContinueExecutionToolResult
  > {
  type: "continue_execution";
  continueWithoutConsent: boolean;
}

interface DataSourceSelectionConfirmationResult {
  selectedDataSource: GetDataSourceToolResult["dataSource"];
}

export interface DataSourceSelectionConfirmationData
  extends ConfirmationData<
    {
      selectedDataSource: GetDataSourceToolResult["dataSource"];
    },
    DataSourceSelectionConfirmationResult
  > {
  type: "data_source_selection";
  originalParams: Record<string, unknown>;
}

export interface RunBtqlConfirmationData extends ConfirmationData<{}, boolean> {
  type: "run_btql";
  newQuery: string;
  originalQuery: string;
}

export type DatasetEditConfirmationHandler = (
  data: DatasetEditConfirmationData,
) => void;

export type UserConsentConfirmationHandler = (
  data: UserConsentConfirmationData,
) => void;

export type TaskEditConfirmationHandler = (
  data: TaskEditConfirmationData,
) => void;

export type EditScorersConfirmationHandler = (
  data: EditScorersConfirmationData,
) => void;

export type CreateLLMScorerConfirmationHandler = (
  data: CreateLLMScorerConfirmationData,
) => void;

export type CreateCodeScorerConfirmationHandler = (
  data: CreateCodeScorerConfirmationData,
) => void;

export type DataSourceSelectionConfirmationHandler = (
  data: DataSourceSelectionConfirmationData,
) => void;

export type RunBtqlConfirmationHandler = (
  data: RunBtqlConfirmationData,
) => void;

import {
  BraintrustState,
  type Span,
  type SerializedBraintrustState,
  _internalGetGlobalState,
} from "braintrust";
import { normalizeProxyUrlBase } from "#/utils/user-types";
import { type PromptData } from "@braintrust/typespecs";
import {
  createOrUpdatePrompt,
  type UIFunction,
  type UIFunctionData,
} from "#/ui/prompts/schema";
import {
  ChatContext,
  UserRejectedError,
  type ChatContextParams,
} from "@braintrust/local/optimization";
import { slugify } from "#/utils/slug";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import { type RowData } from "#/ui/arrow-table";
import { type SummaryPaginatedObjectViewerTableQuery } from "#/ui/summary-paginated-object-viewer";
import { type PaginatedObjectViewerTableQuery } from "#/ui/paginated-object-viewer";
import { type MetricDefinition } from "@braintrust/local/api-schema";
import {
  BTQL_DEFAULT_MAX_ITERATIONS,
  BTQL_DEFAULT_PAGE_SIZE,
  type BTQLResponse,
  fetchBtql,
  fetchBtqlPaginated,
} from "#/utils/btql/btql";
import { type ParsedQuery, parseQuery } from "@braintrust/btql/parser";
import { useBtqlFlags } from "#/lib/feature-flags";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { useDefaultPromptData } from "#/ui/prompts/use-default-prompt-data";
import { usePathname } from "next/navigation";
import {
  isBTQLSandboxPage,
  isDatasetPage,
  isExperimentPage,
  isLogsPage,
  isLoopPage,
  isPlaygroundPage,
} from "#/app/app/[org]/pathname-checker";
import { formatBTQLQuery } from "#/app/app/[org]/btql/format-btql-query";

export type OptimizationContextType = {
  tools: ToolManager;
  registerTaskEditConfirmationHandler?: (
    taskIndex: number,
    handler: TaskEditConfirmationHandler,
  ) => void;
  unregisterTaskEditConfirmationHandler?: (taskIndex: number) => void;
  registerDatasetEditConfirmationHandler?: (
    handler: DatasetEditConfirmationHandler,
  ) => void;
  unregisterDatasetEditConfirmationHandler?: () => void;
  registerUserConsentConfirmationHandler?: (
    handler: UserConsentConfirmationHandler,
  ) => void;
  unregisterUserConsentConfirmationHandler?: () => void;
  registerEditScorersConfirmationHandler?: (
    handler: EditScorersConfirmationHandler,
  ) => void;
  unregisterEditScorersConfirmationHandler?: () => void;

  registerCreateLLMScorerConfirmationHandler?: (
    handler: CreateLLMScorerConfirmationHandler,
  ) => void;
  unregisterCreateLLMScorerConfirmationHandler?: () => void;

  registerCreateCodeScorerConfirmationHandler?: (
    handler: CreateCodeScorerConfirmationHandler,
  ) => void;
  unregisterCreateCodeScorerConfirmationHandler?: () => void;

  registerDataSourceSelectionConfirmationHandler?: (
    handler: DataSourceSelectionConfirmationHandler,
  ) => void;
  unregisterDataSourceSelectionConfirmationHandler?: () => void;

  registerRunBtqlConfirmationHandler?: (
    handler: RunBtqlConfirmationHandler,
  ) => void;
  unregisterRunBtqlConfirmationHandler?: () => void;
  timeRangeSettings: TimeRangeFilter;
  setTimeRangeSettings: (timeRange: TimeRangeFilter) => void;
  makeChatContext: (args?: OptionalChatContextParams) => ChatContext;
  allowRunningWithoutConsent: boolean;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  flush: () => Promise<void>;
};

export const OptimizationContext = createContext<OptimizationContextType>({
  tools: new ToolManager(false),
  makeChatContext: () => {
    throw new Error("makeChatContext not initialized");
  },
  flush: () => Promise.resolve(),
  allowRunningWithoutConsent: false,
  setAllowRunningWithoutConsent: () => {},
  timeRangeSettings: "7d",
  setTimeRangeSettings: () => {},
});

export const DEV_PROD_AI_STACK_API_KEY =
  process.env.NEXT_PUBLIC_DEV_PROD_AI_STACK_API_KEY;
export const STAGING_API_URL = "https://staging-api.braintrust.dev";

export interface OptimizationProviderProps {
  children: React.ReactNode;
  datasetDML?: DML;
  datasetId?: string | undefined;
  experimentId?: string;
  getFunctions?: ({
    useInlinePrompts,
  }: {
    useInlinePrompts: boolean;
  }) => Promise<TaskFunctionDefinition[]>;
  savePrompt?: ({
    id,
    prompt_data,
    function_data,
  }: {
    id: string;
    prompt_data: PromptData;
    function_data: UIFunctionData;
  }) => Promise<TransactionId | null>;
  runPrompts?: PlayXRunPromptsFn;
  createDataset?: ({
    orgId,
    projectName,
    datasetName,
  }: {
    orgId: string;
    projectName: string;
    datasetName: string;
  }) => Promise<
    | {
        data: null;
        error: string;
      }
    | {
        data: {
          dataset: {
            id: string;
          };
        };
        error: null;
      }
  >;
  setDatasetId?: (id: string) => void;
  refreshDatasets?: () => Promise<void>;
  datasetData?:
    | PaginatedObjectViewerTableQuery<RowData, unknown>
    | SummaryPaginatedObjectViewerTableQuery<RowData, unknown>;
  existingFilters?: Search;
  setFilters?: Dispatch<SetStateAction<Search>>;
  // Table data
  summaryBreakdownData?: SummaryBreakdownData;
  baseQuery?: string | null;
  comparisonQueries?: ComparisonExperimentSpanSummary[];

  // Scorer management (optional for contexts that don't support scorer editing)
  updateSavedScorers?: (
    scorers: SavedScorer[],
  ) => Promise<TransactionId | null>;
  getCurrentSavedScorers?: () => SavedScorer[];
  availableScorers?: Record<string, UIFunction>;
  clauseChecker?: ClauseChecker;
  queryProjectId?: string;
  timeRangeFilter?: TimeRangeFilter;

  /** BTQL-specific action (only available in BTQL context) */
  runSandboxQuery?: (args: {
    query: string;
    openNewTab?: boolean;
    title?: string;
  }) => Promise<BTQLResponse<Record<string, unknown>>>;
  getActiveSandboxTabQuery?: () => string;
}

export function OptimizationProvider({
  children,
  datasetDML,
  datasetId,
  getFunctions,
  experimentId,
  savePrompt,
  runPrompts,
  createDataset,
  setDatasetId,
  refreshDatasets,
  datasetData,
  summaryBreakdownData,
  baseQuery,
  comparisonQueries,
  updateSavedScorers,
  getCurrentSavedScorers,
  availableScorers,
  setFilters,
  clauseChecker = noopChecker,
  queryProjectId,
  timeRangeFilter,
  runSandboxQuery,
  getActiveSandboxTabQuery,
}: OptimizationProviderProps) {
  const {
    projectId,
    projectName,
    config: { metricDefinitions },
    orgDatasets,
    projectDatasets,
  } = useContext(ProjectContext);
  const pathname = usePathname();

  const pageKey: PageKey = useMemo(() => {
    if (isPlaygroundPage(pathname ?? "")) return "playground";
    if (isExperimentPage(pathname ?? "")) return "experiments";
    if (isLogsPage(pathname ?? "")) return "logs";
    if (isDatasetPage(pathname ?? "")) return "dataset";
    if (isLoopPage(pathname ?? "")) return "loop";
    if (isBTQLSandboxPage(pathname ?? "")) return "btql";
    return "unknown";
  }, [pathname]);

  const { conn } = useDuckConn();

  const { applySearch } = useFilterSortBarSearch({
    clauseChecker,
    setSearch: setFilters,
  });
  const idxToQuery = useMemo(() => {
    const queries = [baseQuery];
    for (const query of comparisonQueries ?? []) {
      queries.push(query.tableScan);
    }

    return queries;
  }, [baseQuery, comparisonQueries]);

  const getExperimentResults = useCallback(
    async ({
      index,
      numSamples,
    }: {
      index: number;
      numSamples?: number;
    }): Promise<
      Array<{
        id: string;
        input: unknown;
        expected: unknown;
        output: unknown;
        metadata: unknown;
        scores: Record<string, number>;
        metrics: Record<string, number>;
      }>
    > => {
      const tableQuery = idxToQuery[index];
      if (!tableQuery || !conn) {
        return [];
      }

      const resultQuery = `SELECT * FROM (${tableQuery}) t LIMIT ${numSamples}`;

      try {
        const results = await conn.query(resultQuery);
        return results.toArray().map((row) => {
          const data = row.toJSON();
          const scores = normalizeArrowNumbers(data.scores?.toJSON() ?? {});
          const metrics = normalizeArrowNumbers(data.metrics?.toJSON() ?? {});

          const input = data.input
            ? safeDeserializeUnknown(data.input)
            : undefined;
          const output = data.output
            ? safeDeserializeUnknown(data.output)
            : undefined;
          const expected = data.expected
            ? safeDeserializeUnknown(data.expected)
            : undefined;
          const metadata = data.metadata
            ? safeDeserializeUnknown(data.metadata)
            : undefined;

          return {
            id: data.id,
            input,
            expected,
            output,
            metadata,
            scores: z.record(z.coerce.number()).nullish().parse(scores) ?? {},
            metrics: z.record(z.coerce.number()).nullish().parse(metrics) ?? {},
          };
        });
      } catch (error) {
        console.error("Error fetching experiment results:", error);
        return [];
      }
    },
    [conn, idxToQuery],
  );

  const getDatasetResults = useCallback(
    async ({
      numSamples = 10,
    }: GetResultsToolParameters): Promise<GetResultsToolResult[]> => {
      if (!datasetData) {
        return [];
      }

      // Handle SummaryPaginatedObjectViewerTableQuery - it has the data directly
      if (datasetData.type === "tableData" && datasetData.data) {
        //This way of grabbing data is very flawed -- needs to be fixed. It will only grab the first N.
        return datasetData.data.slice(0, numSamples).map((row: RowData) => ({
          id: typeof row.id === "string" ? row.id : (row.id.primary ?? ""),
          input: row.input,
          expected: row.expected,
          output: undefined,
          metadata: row.metadata ?? undefined,
          scores: {},
          metrics: {},
        }));
      }

      return [];
    },
    [datasetData],
  );

  const getResults = useCallback(
    async ({
      numSamples,
      index,
    }: GetResultsToolParameters): Promise<GetResultsToolResult[]> => {
      if (!getFunctions) {
        return [];
      }

      const prompt = (await getFunctions({ useInlinePrompts: false }))[index];
      const promptXactId = "version" in prompt ? prompt.version : undefined;

      const tableQuery = idxToQuery[index];
      if (!tableQuery || !conn) {
        return [];
      }
      // The sorting logic here is the same as the "cell has not been run yet" logic in grid-layout-columns.tsx
      const resultQuery = `SELECT * FROM (${tableQuery}) t
          WHERE
            true
            ${promptXactId ? `AND playground_xact_id > ${singleQuote(promptXactId)}` : ""}
          ORDER BY
            (output IS NOT NULL OR COALESCE((span_type_info->>'has_error')::boolean, false)) DESC
            , RANDOM()
          LIMIT ${numSamples}`;
      const results = await conn.query(resultQuery);
      return results.toArray().map((row) => {
        const data = row.toJSON();
        const scores = normalizeArrowNumbers(data.scores?.toJSON() ?? {});
        const metrics = normalizeArrowNumbers(data.metrics?.toJSON() ?? {});

        const input = data.input
          ? safeDeserializeUnknown(data.input)
          : undefined;
        const output = data.output
          ? safeDeserializeUnknown(data.output)
          : undefined;
        const expected = data.expected
          ? safeDeserializeUnknown(data.expected)
          : undefined;
        const metadata = data.metadata
          ? safeDeserializeUnknown(data.metadata)
          : undefined;

        return {
          id: data.id,
          input,
          expected,
          output,
          metadata,
          scores: z.record(z.coerce.number()).nullish().parse(scores) ?? {},
          metrics: z.record(z.coerce.number()).nullish().parse(metrics) ?? {},
        };
      });
    },
    [conn, getFunctions, idxToQuery],
  );

  // The DML object here will work for any function type.
  const savedScorerObjects = useScorerFunctions({});
  const org = useOrg();
  const upsertFunction = useCallback(
    async (fn: Omit<UIFunction, "_xact_id">, update: boolean) => {
      if (!org.id) {
        throw new Error("Unauthorized");
      }

      return await createOrUpdatePrompt({
        dml: savedScorerObjects.dml,
        orgId: org.id,
        update,
        prompt: {
          ...fn,
          _xact_id: "",
        },
        updateSlug: false,
      });
    },
    [org.id, savedScorerObjects.dml],
  );

  const { getOrRefreshToken } = useSessionToken();
  const { proxy_url, api_url, id: orgId, name: orgName } = useOrg();
  const { user } = useUser();

  const [tools] = useState<ToolManager>(() => {
    const allowedTools = getToolsForPage(pageKey);
    return new ToolManager(false, allowedTools);
  });

  const isPlaygroundRunning = useAtomValue(isPlaygroundRunningAtom);
  const defaultPromptData = useDefaultPromptData();
  const onStopRunning = useRef<(summary: SummaryBreakdownData) => void>(
    () => {},
  );

  useEffect(() => {
    if (!isPlaygroundRunning && summaryBreakdownData) {
      onStopRunning.current(summaryBreakdownData);
    }
  }, [isPlaygroundRunning, summaryBreakdownData]);

  const taskEditConfirmationHandlersRef = useRef<
    Map<number, TaskEditConfirmationHandler>
  >(new Map());

  const datasetEditConfirmationHandlerRef =
    useRef<DatasetEditConfirmationHandler | null>(null);

  const createLLMScorerConfirmationHandlerRef =
    useRef<CreateLLMScorerConfirmationHandler | null>(null);

  const createCodeScorerConfirmationHandlerRef =
    useRef<CreateCodeScorerConfirmationHandler | null>(null);

  const userConsentConfirmationHandlerRef =
    useRef<UserConsentConfirmationHandler | null>(null);

  const editScorersConfirmationHandlerRef =
    useRef<EditScorersConfirmationHandler | null>(null);

  const dataSourceSelectionConfirmationHandlerRef =
    useRef<DataSourceSelectionConfirmationHandler | null>(null);

  const runBtqlConfirmationHandlerRef =
    useRef<RunBtqlConfirmationHandler | null>(null);

  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  /*  const editBTQLConfirmationHandlerRef =
     useRef<EditBTQLConfirmationHandler | null>(null);

   const registerEditBTQLConfirmationHandler = useCallback(
     (handler: EditBTQLConfirmationHandler) => {
       editBTQLConfirmationHandlerRef.current = handler;
     },
     [],
   );

  const unregisterEditBTQLConfirmationHandler = useCallback(() => {
    editBTQLConfirmationHandlerRef.current = null;
  }, []); */

  const registerEditScorersConfirmationHandler = useCallback(
    (handler: EditScorersConfirmationHandler) => {
      editScorersConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterEditScorersConfirmationHandler = useCallback(() => {
    editScorersConfirmationHandlerRef.current = null;
  }, []);

  const registerTaskEditConfirmationHandler = useCallback(
    (taskIndex: number, handler: TaskEditConfirmationHandler) => {
      taskEditConfirmationHandlersRef.current.set(taskIndex, handler);
    },
    [],
  );

  const unregisterTaskEditConfirmationHandler = useCallback(
    (taskIndex: number) => {
      taskEditConfirmationHandlersRef.current.delete(taskIndex);
    },
    [],
  );

  const registerCreateLLMScorerConfirmationHandler = useCallback(
    (handler: CreateLLMScorerConfirmationHandler) => {
      createLLMScorerConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterCreateLLMScorerConfirmationHandler = useCallback(() => {
    createLLMScorerConfirmationHandlerRef.current = null;
  }, []);

  const registerCreateCodeScorerConfirmationHandler = useCallback(
    (handler: CreateCodeScorerConfirmationHandler) => {
      createCodeScorerConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterCreateCodeScorerConfirmationHandler = useCallback(() => {
    createCodeScorerConfirmationHandlerRef.current = null;
  }, []);

  const registerDatasetEditConfirmationHandler = useCallback(
    (handler: DatasetEditConfirmationHandler) => {
      datasetEditConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterDatasetEditConfirmationHandler = useCallback(() => {
    datasetEditConfirmationHandlerRef.current = null;
  }, []);

  const registerUserConsentConfirmationHandler = useCallback(
    (handler: UserConsentConfirmationHandler) => {
      userConsentConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterUserConsentConfirmationHandler = useCallback(() => {
    userConsentConfirmationHandlerRef.current = null;
  }, []);

  const registerDataSourceSelectionConfirmationHandler = useCallback(
    (handler: DataSourceSelectionConfirmationHandler) => {
      dataSourceSelectionConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterDataSourceSelectionConfirmationHandler = useCallback(() => {
    dataSourceSelectionConfirmationHandlerRef.current = null;
  }, []);

  const registerRunBtqlConfirmationHandler = useCallback(
    (handler: RunBtqlConfirmationHandler) => {
      runBtqlConfirmationHandlerRef.current = handler;
    },
    [],
  );

  const unregisterRunBtqlConfirmationHandler = useCallback(() => {
    runBtqlConfirmationHandlerRef.current = null;
  }, []);

  const [allowRunningWithoutConsent, setAllowRunningWithoutConsentStorage] =
    useEntityStorage<"optimization", "allowRunningWithoutConsent">({
      entityType: "optimization",
      entityIdentifier: "",
      key: "allowRunningWithoutConsent",
    });

  const setAllowRunningWithoutConsent = useCallback(
    (allow: boolean) => {
      setAllowRunningWithoutConsentStorage(allow);
    },
    [setAllowRunningWithoutConsentStorage],
  );

  // Local time range state for logs queries (defaults to 7 days)
  const [timeRangeSettings, setTimeRangeSettings] =
    useState<TimeRangeFilter>("7d");

  const editScorers = useCallback(
    async ({
      params,
      abortController,
      skipConsent,
    }: {
      params: EditScorersParams;
      span: Span;
      abortController: AbortController | undefined;
      skipConsent: boolean;
    }) => {
      if (!updateSavedScorers || !getCurrentSavedScorers) {
        throw new Error(
          "Scorer editing is not currently supported in this context. ",
        );
      }

      const currentScorers = getCurrentSavedScorers();

      const currentScorerIds = new Set(
        currentScorers.map((scorer) =>
          scorer.type === "function" ? scorer.id : scorer.name,
        ),
      );

      const disabledScorerIds = new Set(
        params.scorers.filter((s) => !s.enabled).map((s) => s.id),
      );

      const newScorers: SavedScorer[] = currentScorers
        .filter((scorer) => {
          const scorerId = scorer.type === "function" ? scorer.id : scorer.name;
          return !disabledScorerIds.has(scorerId);
        })
        .concat(
          params.scorers
            .filter(
              (scorer) => scorer.enabled && !currentScorerIds.has(scorer.id),
            )
            .map((scorer): SavedScorer => {
              if (scorer.name === scorer.id) {
                return { type: "global", name: scorer.name };
              } else {
                return { type: "function", id: scorer.id };
              }
            }),
        );

      const newScorerIds = new Set(
        newScorers.map((s) => (s.type === "function" ? s.id : s.name)),
      );

      if (isEqual(currentScorerIds, newScorerIds)) {
        return {
          ok: true,
          reason: "No changes were made",
        };
      }
      let userConfirmationResult: EditScorersToolResult;

      if (skipConsent) {
        userConfirmationResult = {
          ok: true,
        };
      } else {
        const customHandler = editScorersConfirmationHandlerRef.current;

        if (!customHandler) {
          throw new Error("This is missing a custom handler");
        }

        userConfirmationResult = await executeAsyncUIHandler<
          EditScorersToolResult,
          EditScorersConfirmationData
        >({
          data: {
            type: "edit_scorers",
            scorers: params.scorers,
            currentScorers: currentScorers,
            availableScorers: availableScorers ?? {},
            onConfirm: (opts?) => {
              return { ok: true };
            },
            onCancel: (reason?, opts?) => {
              return new UserRejectedError(reason);
            },
          },
          handler: customHandler,
          abortController,
        });
      }

      if (userConfirmationResult.ok) {
        await updateSavedScorers(newScorers);
      }
      return userConfirmationResult;
    },
    [availableScorers, getCurrentSavedScorers, updateSavedScorers],
  );

  const btqlFlags = useBtqlFlags();
  const builder = useBtqlQueryBuilder({});

  useEffect(() => {
    tools.updateImplementations({
      search_docs: async (args: { query: string; topK?: number }) => {
        const result = await fetch("/api/docs/search", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            query: args.query,
            topK: args.topK,
          }),
        });
        return docsToolResultSchema.parse(await result.json());
      },

      ...(summaryBreakdownData && {
        get_summary: async (): Promise<GetSummaryToolResult[]> => {
          if (experimentId && summaryBreakdownData) {
            return await makeExperimentSummary(
              summaryBreakdownData,
              metricDefinitions,
            );
          }

          if (!getFunctions || !summaryBreakdownData) {
            return [];
          }
          return await makePlaygroundSummary(
            getFunctions,
            summaryBreakdownData,
          );
        },
      }),

      ...((summaryBreakdownData || datasetData) && {
        get_results: datasetData
          ? getDatasetResults
          : experimentId
            ? async ({
                index,
                numSamples,
              }: {
                index: number;
                numSamples: number;
              }) => getExperimentResults({ index, numSamples })
            : getResults,
      }),
      ...(getFunctions &&
        savePrompt && {
          edit_task: async (
            args: EditTaskToolParameters,
            span: Span,
            abortController: AbortController | undefined,
          ) => {
            const task = (await getInlineFunctions(getFunctions))[args.index];
            if (!task) {
              throw new Error(`Task ${args.index} not found`);
            }
            const { definition, isPrompt } = makeDefinition(task);

            const newDefinition = applyEdits(definition, args.edits, span);

            if (JSON.stringify(definition) === JSON.stringify(newDefinition)) {
              return {
                ok: true,
                reason: "No changes were made",
                noEffect: true,
                opts: { continueChat: true },
              };
            }

            const customHandler = taskEditConfirmationHandlersRef.current.get(
              args.index,
            );

            if (!customHandler) {
              throw new Error("This prompt is no longer in the playground");
            }

            let userConfirmationResult: EditTaskToolResult;

            if (allowRunningWithoutConsent) {
              userConfirmationResult = {
                ok: true,
              };
            } else {
              const customHandler = taskEditConfirmationHandlersRef.current.get(
                args.index,
              );

              if (!customHandler) {
                throw new Error("This prompt is no longer in the playground");
              }

              userConfirmationResult = await executeAsyncUIHandler<
                EditTaskToolResult,
                TaskEditConfirmationData
              >({
                data: {
                  type: "edit_task",
                  index: args.index,
                  originalDefinition: definition,
                  newDefinition: newDefinition,
                  onConfirm: (opts?) => {
                    return { ok: true };
                  },
                  onCancel: (reason?, opts?) => {
                    return new UserRejectedError(reason);
                  },
                },
                handler: customHandler,
                abortController,
              });
            }

            if (userConfirmationResult.ok) {
              await savePrompt({
                id: task.id,
                prompt_data: isPrompt ? newDefinition : {},
                function_data: isPrompt ? task.inline_function : newDefinition,
              });
            }

            return userConfirmationResult;
          },
        }),
      ...(runPrompts &&
        getFunctions && {
          run_task: async (
            args: RerunTaskToolParameters,
            _span: Span,
            abortController,
          ): Promise<RerunTaskToolResult> => {
            if (!allowRunningWithoutConsent) {
              const customHandler = userConsentConfirmationHandlerRef.current;
              if (!customHandler) {
                throw new Error("This is missing a custom handler");
              }

              const userConsentResult = await executeAsyncUIHandler<
                ContinueExecutionToolResult,
                UserConsentConfirmationData
              >({
                data: {
                  type: "continue_execution",
                  continueWithoutConsent: false,
                  onConfirm: (opts?) => {
                    if (opts) {
                      setAllowRunningWithoutConsent(
                        opts.continueWithoutConsent,
                      );
                    }
                    return { allowed: true };
                  },
                  onCancel: (reason?, opts?) => {
                    return new UserRejectedError(reason);
                  },
                },
                handler: customHandler,
                abortController,
              });

              if (!userConsentResult.allowed) {
                throw new Error("User did not allow running the task");
              }
            }

            const [_, summaryBreakdownData] = await Promise.all([
              runPrompts({
                colIdx: args.index,
              }),
              new Promise<SummaryBreakdownData>((resolve) => {
                onStopRunning.current = (d) => resolve(d);
              }),
            ]);
            onStopRunning.current = () => {};
            const summary = await makePlaygroundSummary(
              getFunctions,
              summaryBreakdownData,
            );
            return { summary: summary[args.index] };
          },
        }),
      //Before, we relied on createPlaygroundDataset to create the dataset and set the datasetId of the playground.
      //We then relied on the playground's subscription to the realtime update to render the new dataset rows in the UI.
      //Now, we manually create the dataset and refetch, and at the end of this edit_data function, manually set the datasetId
      //of the playground so as to not rely on realtime updates for the UI.
      edit_data: async (
        args: EditDataToolParameters,
        _span: Span,
        abortController,
      ) => {
        if (!orgId || !projectName) {
          throw new Error(
            "Org ID and project name are required to edit a dataset",
          );
        }

        const existing =
          conn && idxToQuery[0]
            ? await getDatasetRows({
                conn,
                ids: args.edits
                  .map((edit) => edit.id)
                  .filter((id) => id !== undefined),
                tableQuery: idxToQuery[0],
              })
            : {};

        let userConfirmationResult: EditDataToolResult;

        if (allowRunningWithoutConsent) {
          userConfirmationResult = {
            ok: true,
          };
        } else {
          const customHandler = datasetEditConfirmationHandlerRef.current;

          if (!customHandler) {
            throw new Error("This dataset is no longer in the playground");
          }

          userConfirmationResult = await executeAsyncUIHandler<
            EditDataToolResult,
            DatasetEditConfirmationData
          >({
            data: {
              type: "edit_dataset",
              edits: args.edits,
              existing,
              onConfirm: (opts?) => {
                return { ok: true };
              },
              onCancel: (reason?, opts?) => {
                return new UserRejectedError(reason);
              },
            },
            handler: customHandler,
            abortController,
          });
        }

        if (!userConfirmationResult.ok) {
          return userConfirmationResult;
        }
        let dataset_id = datasetId;
        if (!dataset_id) {
          const result = await createDatasetApi({
            orgId: orgId,
            projectName: projectName,
            datasetName: args.name,
          });
          if (result.data) {
            dataset_id = result.data?.dataset?.id;
            if (refreshDatasets) {
              await refreshDatasets();
            }
          }
        }

        const dmlRows = args.edits.map((edit) => {
          if (edit.delete) {
            return {
              id: edit.id,
              dataset_id,
              [OBJECT_DELETE_FIELD]: true,
            };
          } else if (!edit.id) {
            return {
              id: newId(),
              input: edit.input,
              expected: edit.expected,
              metadata: edit.metadata,
              dataset_id,
            };
          } else {
            return {
              id: edit.id,
              ...(!isEmpty(edit.input) ? { input: edit.input } : {}),
              ...(!isEmpty(edit.expected) ? { expected: edit.expected } : {}),
              ...(!isEmpty(edit.metadata) ? { metadata: edit.metadata } : {}),
              [IS_MERGE_FIELD]: true,
              [MERGE_PATHS_FIELD]: (edit.input ? [["input"]] : []).concat(
                edit.expected ? [["expected"]] : [],
                edit.metadata ? [["metadata"]] : [],
              ),
              dataset_id,
            };
          }
        });

        if (datasetDML) {
          await datasetDML.upsert(dmlRows);
        } else {
          const sessionToken = await getOrRefreshToken();
          if (user?.id) {
            await performUpsert(
              null,
              org.api_url,
              sessionToken,
              user.id,
              dmlRows,
            );
          } else {
            throw new Error("User ID is required to edit a dataset");
          }
        }

        if (dataset_id && setDatasetId) {
          setDatasetId(dataset_id);
        }

        return userConfirmationResult;
      },
      infer_schema: async (
        params: InferSchemaToolParameters,
        _span: Span,
        abortController: AbortController | undefined,
      ): Promise<InferSchemaToolResult> => {
        // Forcibly override the entity type and id for the logs page. This ensures we can enforce
        // deterministic data source selection for the logs page, while requiring that data source always be provided
        // by the LLM at other call sites.
        const entityType =
          pageKey === "logs" ? "project_logs" : params.dataSource.entity;
        const entityId = pageKey === "logs" ? projectId : params.dataSource.id;

        const effectiveTimeRangeFilter = timeRangeFilter
          ? timeRangeFilter
          : timeRangeSettings;

        // Don't use the time range filter on BTQL sandbox
        const timeRangeExprs =
          pageKey !== "btql"
            ? timeRangeFilterToBtqlExprs(effectiveTimeRangeFilter, entityType)
            : [];

        // XXX TODO
        // 1. Reuse the schema inference we already do (or prefetch it without the is_root filter)
        // 2. Use the react query cache for btql queries.
        const query: ParsedQuery = {
          from: {
            op: "function",
            name: {
              op: "ident",
              name: [entityType],
            },
            args: [
              {
                op: "literal",
                value: entityId,
              },
            ],
          },
          filter: builder.and(...timeRangeExprs),
          infer: [
            {
              op: "star",
            },
          ],
          limit: 1000,
        };

        const result = await fetchBtql({
          args: {
            query,
            brainstoreRealtime: false,
            disableLimit: false,
          },
          apiUrl: org.api_url,
          btqlFlags,
          getOrRefreshToken,
        });

        return inferSchemaToolResultSchema.strip().parse(result);
      },

      get_data_source: async (
        params: GetDataSourceToolParameters,
        _span: Span,
        abortController: AbortController | undefined,
      ): Promise<GetDataSourceToolResult> => {
        const customHandler = dataSourceSelectionConfirmationHandlerRef.current;
        if (!customHandler) {
          throw new Error("Data source selection handler not available");
        }

        const { selectedDataSource } = await executeAsyncUIHandler<
          DataSourceSelectionConfirmationResult,
          DataSourceSelectionConfirmationData
        >({
          data: {
            type: "data_source_selection",
            originalParams: params,
            onConfirm: (opts) => {
              const selectedDataSource = opts?.selectedDataSource;
              if (!selectedDataSource?.id) {
                throw new Error("No data source selected");
              }
              return { selectedDataSource };
            },
            onCancel: (reason) => new UserRejectedError(reason),
          },
          handler: customHandler,
          abortController,
        });

        return {
          dataSource: selectedDataSource,
        };
      },

      btql_query: async (
        params: BTQLQueryToolParameters,
      ): Promise<BTQLQueryToolResult> => {
        const queryString = `from: project_logs('${queryProjectId}') ${params.shape ?? ""} | ${params.query}`;
        const query = parseQuery(queryString);

        //If in the logs page, we use the log page's time range, otherwise we use the time range from the tool params
        const effectiveTimeRangeFilter = timeRangeFilter
          ? timeRangeFilter
          : timeRangeSettings;

        const timeRangeExprs = timeRangeFilterToBtqlExprs(
          effectiveTimeRangeFilter,
          "project_logs",
        );
        if (timeRangeExprs.length > 0) {
          // Add the filter to the existing query
          if (query.filter) {
            // If there's already a filter, combine them with AND
            query.filter = builder.and(query.filter, ...timeRangeExprs);
          } else {
            // Otherwise, just add our filter
            query.filter = builder.and(...timeRangeExprs);
          }
        }

        const result = await fetchBtqlPaginated<Record<string, unknown>[]>(
          {
            args: {
              query,
              brainstoreRealtime: true,
              disableLimit: false,
            },
            apiUrl: org.api_url,
            btqlFlags,
            getOrRefreshToken,
          },
          BTQL_DEFAULT_PAGE_SIZE,
          BTQL_DEFAULT_MAX_ITERATIONS,
          true,
          query.limit ?? undefined,
        );

        // Keep this logic in sync with the run_btql tool (used in sandbox context)
        // There are 3 large fields (input, output, metadata). Ideally this logic lives in the preview_length feature
        // pushed to brainstore, but this is fine for now.
        const perFieldSize = Math.floor(
          MAX_ALLOWED_BTQL_TEXT / 3 / result.data.length,
        );

        const truncatedData = result.data.map((row) =>
          Object.fromEntries(
            Object.entries(row).map(([k, v]) => [
              k,
              truncateIfExceedsLength(v, perFieldSize),
            ]),
          ),
        );
        const resultWithTruncatedData = {
          ...result,
          data: truncatedData,
        };

        const data = btqlQueryToolResultSchema
          .omit({ rowCount: true, projectId: true })
          .parse(resultWithTruncatedData);
        return {
          rowCount: data.data.length,
          timeRangeFilter: effectiveTimeRangeFilter,
          projectId: queryProjectId ?? "",
          ...data,
        };
      },
      continue_execution: async (
        args: ContinueExecutionToolParameters,
        _span: Span,
        abortController: AbortController | undefined,
      ) => {
        const customHandler = userConsentConfirmationHandlerRef.current;

        if (!customHandler) {
          throw new Error("This is missing a custom handler");
        }

        const userConsentResult = await executeAsyncUIHandler<
          ContinueExecutionToolResult,
          UserConsentConfirmationData
        >({
          data: {
            type: "continue_execution",
            continueWithoutConsent: false,
            onConfirm: (opts?) => {
              return { allowed: true };
            },
            onCancel: (reason?, opts?) => {
              return new UserRejectedError(reason);
            },
          },
          handler: customHandler,
          abortController,
        });

        return userConsentResult;
      },
      get_available_scorers: async (
        params: GetAvailableScorersParams,
      ): Promise<GetAvailableScorersResult[]> => {
        // Early exit if params.id is provided - return only the matching scorer
        if (params.id) {
          const globalScorers = getGlobalScorers();
          // Check global scorers first
          const globalScorer = globalScorers.find((s) => s.id === params.id);
          if (globalScorer) {
            return [globalScorer];
          }

          // Check available scorers
          const scorer = savedScorerObjects.functions[params.id];
          if (scorer) {
            let baseScorer: GetAvailableScorersResult = {
              id: params.id,
              name: scorer.name ?? params.id,
              type: "custom" as const,
              description: scorer.description ?? "",
              slug: scorer.slug,
            };

            // Add additional fields based on scorer type
            if (
              scorer.function_data?.type === "code" &&
              "data" in scorer.function_data
            ) {
              const codeData = scorer.function_data.data;
              if (codeData?.type === "inline" && "code" in codeData) {
                baseScorer = {
                  ...baseScorer,
                  code: codeData.code,
                  runtime: codeData.runtime_context?.runtime,
                };
              }
            } else if (scorer.function_data?.type === "prompt") {
              baseScorer = {
                ...baseScorer,
                ...(scorer.prompt_data ? { prompt: scorer.prompt_data } : {}),
              };
            }

            return [baseScorer];
          }

          // No scorer found with the given id
          return [];
        }

        // Original logic for when no id is provided
        const globalScorers = getGlobalScorers();
        const allAvailableScorers = Object.entries(
          savedScorerObjects.functions,
        ).map(([id, s]) => {
          const baseScorer: GetAvailableScorersResult = {
            id,
            name: s.name ?? id,
            type: "custom" as const,
            description: s.description ?? "",
            slug: s.slug,
          };

          // Add additional fields based on scorer type
          if (s.function_data?.type === "code" && "data" in s.function_data) {
            const codeData = s.function_data.data;
            if (codeData?.type === "inline" && "code" in codeData) {
              return {
                ...baseScorer,
                type: s.function_data?.type,
                //stupid truncate code for now because context window gets blown up in bigger projects
                code: truncateIfExceedsLength(codeData.code, 2500),
                runtime: codeData.runtime_context?.runtime,
              };
            }
          } else if (s.function_data?.type === "prompt") {
            return {
              ...baseScorer,
              type: s.function_data?.type,
              //stupid truncate code for now because context window gets blown up in bigger projects
              ...(s.prompt_data
                ? { prompt: truncateIfExceedsLength(s.prompt_data, 2500) }
                : {}),
            };
          }
          return baseScorer;
        });
        return [...globalScorers, ...allAvailableScorers];
      },
      ...(updateSavedScorers &&
        getCurrentSavedScorers && {
          edit_scorers: (params, span, abortController) =>
            editScorers({
              params,
              span,
              abortController,
              skipConsent: allowRunningWithoutConsent,
            }),
        }),
      create_llm_scorer: async (
        params: CreateLLMScorerParams,
        span: Span,
        abortController,
      ) => {
        if (!projectId) {
          throw new Error(
            "Project ID is required to create a LLM-as-judge scorer",
          );
        }
        let existingScorer: UIFunction | undefined;

        //If llm passed in an id, we are updating an existing scorer. so grab the existing scorer.
        if (params.id && savedScorerObjects.functions[params.id]) {
          existingScorer = savedScorerObjects.functions[params.id];
        }

        let userConfirmationResult: CreateCodeScorerToolResult;

        const id = params.id ?? newId();
        const slug = existingScorer?.slug ?? slugify(params.name);

        const promptObject: Omit<UIFunction, "_xact_id"> = {
          id,
          name: params.name,
          slug,
          project_id: projectId,
          description: params.description,
          function_type: "scorer",
          function_data: {
            type: "prompt",
          },
          prompt_data: {
            prompt: {
              type: "chat",
              messages: [
                {
                  role: "system",
                  content: params.prompt,
                },
              ],
            },
            options: {
              model: params.model,
              params: params.params,
            },
            parser: {
              type: "llm_classifier",
              use_cot: params.use_cot,
              choice_scores: params.choice_scores,
            },
          },
        };

        if (allowRunningWithoutConsent) {
          userConfirmationResult = { id };
        } else {
          const customHandler = createLLMScorerConfirmationHandlerRef.current;

          if (!customHandler) {
            throw new Error("This is missing a custom handler");
          }

          userConfirmationResult = await executeAsyncUIHandler<
            CreateLLMScorerToolResult,
            CreateLLMScorerConfirmationData
          >({
            data: {
              type: "create_llm_scorer",
              existingScorer: existingScorer,
              scorer: promptObject,
              onConfirm: (opts?) => {
                return { id };
              },
              onCancel: (reason?, opts?) => {
                return new UserRejectedError(reason);
              },
            },
            handler: customHandler,
            abortController,
          });
        }

        await upsertFunction(promptObject, !!params.id);

        if (updateSavedScorers && getCurrentSavedScorers) {
          await editScorers({
            params: {
              scorers: [
                {
                  id: promptObject.id,
                  name: promptObject.name ?? "",
                  description: promptObject.description ?? "",
                  enabled: true,
                },
              ],
            },
            span,
            abortController,
            skipConsent: true,
          });
        }
        return userConfirmationResult;
      },
      create_code_scorer: async (
        params: CreateCodeScorerParams,
        span: Span,
        abortController,
      ) => {
        if (!projectId) {
          throw new Error("Project ID is required to create a code scorer");
        }

        let existingScorer: UIFunction | undefined;
        //If llm passed in an id, we are updating an existing scorer. so grab the existing scorer.
        if (params.id && savedScorerObjects.functions[params.id]) {
          existingScorer = savedScorerObjects.functions[params.id];
        }
        const id = params.id ?? newId();
        let userConfirmationResult: CreateCodeScorerToolResult;

        if (allowRunningWithoutConsent) {
          userConfirmationResult = { id };
        } else {
          const customHandler = createCodeScorerConfirmationHandlerRef.current;

          if (!customHandler) {
            throw new Error("This is missing a custom handler");
          }

          userConfirmationResult = await executeAsyncUIHandler<
            CreateCodeScorerToolResult,
            CreateCodeScorerConfirmationData
          >({
            data: {
              type: "create_code_scorer",
              existingScorer: existingScorer,
              scorer: params,
              onConfirm: (opts?) => {
                return { id };
              },
              onCancel: (reason?, opts?) => {
                return new UserRejectedError(reason);
              },
            },
            handler: customHandler,
            abortController,
          });
        }

        const slug = existingScorer?.slug ?? slugify(params.name);
        const functionData = {
          type: "code" as const,
          data: {
            type: "inline" as const,
            runtime_context:
              params.runtime === "typescript"
                ? ({ runtime: "node", version: "20" } as const)
                : ({
                    runtime: "python",
                    version: "3.12",
                  } as const),
            code: params.code,
          },
        };

        await upsertFunction(
          {
            id,
            name: params.name,
            slug,
            project_id: projectId,
            description: params.description,
            function_type: "scorer",
            function_data: functionData,
          },
          !!params.id,
        );

        if (updateSavedScorers && getCurrentSavedScorers) {
          await editScorers({
            params: {
              scorers: [
                {
                  id,
                  name: params.name,
                  description: params.description,
                  enabled: true,
                },
              ],
            },
            span,
            abortController,
            skipConsent: true,
          });
        }
        return userConfirmationResult;
      },
      ...(runSandboxQuery &&
        getActiveSandboxTabQuery && {
          run_btql: async (
            { query, title }: RunBtqlToolParameters,
            _span: Span,
            abortController: AbortController | undefined,
          ): Promise<RunBtqlToolResult> => {
            const formattedQuery = formatBTQLQuery(query);

            // Show confirmation dialog if consent is not already granted
            if (!allowRunningWithoutConsent) {
              const customHandler = runBtqlConfirmationHandlerRef.current;
              if (!customHandler) {
                throw new Error("BTQL confirmation handler not available");
              }

              await executeAsyncUIHandler<boolean, RunBtqlConfirmationData>({
                data: {
                  type: "run_btql",
                  newQuery: formattedQuery,
                  originalQuery: formatBTQLQuery(getActiveSandboxTabQuery()),
                  onConfirm: (opts?) => {
                    // We'll return the actual result after running the query
                    return true;
                  },
                  onCancel: (reason?, opts?) => {
                    return new UserRejectedError(reason);
                  },
                },
                handler: customHandler,
                abortController,
              });

              // If user cancelled, the promise will reject with UserRejectedError
              // If user confirmed, we proceed to run the query
            }

            const result = await runSandboxQuery({
              query: formattedQuery,
              title,
            });

            // Keep this logic in sync with the btql_query tool (used in non-sandbox contexts)
            // There are 4 potentially large fields (input, output, metadata, expected). Ideally this logic lives in the preview_length feature
            // pushed to brainstore, but this is fine for now.
            const perFieldSize = Math.floor(
              MAX_ALLOWED_BTQL_TEXT / 3 / result.data.length,
            );

            const truncatedData = result.data.map((row) =>
              Object.fromEntries(
                Object.entries(row).map(([k, v]) => [
                  k,
                  truncateIfExceedsLength(v, perFieldSize),
                ]),
              ),
            );

            return {
              ...result,
              data: truncatedData,
            };
          },
        }),
    });
  }, [
    defaultPromptData,
    user,
    orgName,
    savedScorerObjects.functions,
    getResults,
    getExperimentResults,
    getDatasetResults,
    getFunctions,
    summaryBreakdownData,
    datasetData,
    tools,
    applySearch,
    orgId,
    projectName,
    refreshDatasets,
    setDatasetId,
    savePrompt,
    runPrompts,
    conn,
    setFilters,
    idxToQuery,
    datasetDML,
    datasetId,
    experimentId,
    allowRunningWithoutConsent,
    createDataset,
    updateSavedScorers,
    getCurrentSavedScorers,
    availableScorers,
    upsertFunction,
    projectId,
    editScorers,
    setAllowRunningWithoutConsent,
    metricDefinitions,
    queryProjectId,
    org.api_url,
    org.name,
    btqlFlags,
    getOrRefreshToken,
    builder,
    timeRangeFilter,
    runSandboxQuery,
    timeRangeSettings,
    setTimeRangeSettings,
    orgDatasets,
    projectDatasets,
    pageKey,
    getActiveSandboxTabQuery,
  ]);

  const loggingState = useMemo(() => {
    const serializedState: SerializedBraintrustState = DEV_PROD_AI_STACK_API_KEY
      ? {
          appUrl: "https://www.braintrust.dev",
          appPublicUrl: "https://www.braintrust.dev",
          orgName: "braintrustdata.com",
          apiUrl: STAGING_API_URL,
          proxyUrl: normalizeProxyUrlBase(STAGING_API_URL),
          loginToken: DEV_PROD_AI_STACK_API_KEY,
          orgId: "braintrustdata.com",
          gitMetadataSettings: undefined,
        }
      : {
          appUrl: window.location.origin,
          appPublicUrl: window.location.origin,
          orgName,
          apiUrl: api_url,
          proxyUrl: normalizeProxyUrlBase(proxy_url),
          loginToken: "<invalid>", // gets filled in dynamically
          orgId,
          gitMetadataSettings: undefined,
        };
    return BraintrustState.deserialize(serializedState, {
      fetch: async (input, init) => {
        const sessionToken = await getOrRefreshToken();
        const { sessionHeaders, sessionExtraFetchProps } =
          sessionFetchProps(sessionToken);

        return await fetch(input, {
          ...init,
          mode: "cors", // no-cors, *cors, same-origin
          cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
          headers: {
            ...init?.headers,
            ...(DEV_PROD_AI_STACK_API_KEY
              ? { "x-bt-app-origin": "https://www.braintrust.dev" }
              : sessionHeaders),
          },
          ...sessionExtraFetchProps,
          // Chrome requires keepalive requests to have up to 64kb payload size...
          // https://github.com/getsentry/sentry-javascript/issues/1464
          keepalive: false,
        });
      },
    });
  }, [api_url, getOrRefreshToken, orgId, orgName, proxy_url]);
  const value: OptimizationContextType = useMemo(
    () => ({
      tools,
      makeChatContext: (args) =>
        new ChatContext({
          tools,
          orgName: orgName,
          loggingRoot: new SpanComponentsV3({
            object_type: SpanObjectTypeV3.PROJECT_LOGS,
            object_id: OPTIMIZATION_PROJECT_ID,
          }),
          loggingState,
          loggingMetadata: {
            user_id: user?.id,
            email: user?.email ?? undefined,
            project_id: projectId ?? undefined,
            org_id: orgId,
          },
          tags: DEV_PROD_AI_STACK_API_KEY ? ["local"] : [],
          defaultSystemPrompt: args?.defaultSystemPrompt,
          modelParams: {
            ...args?.modelParams,
          },
          enableLogging: shouldLog(api_url),
          ...args,
        }),
      registerTaskEditConfirmationHandler,
      unregisterTaskEditConfirmationHandler,
      registerDatasetEditConfirmationHandler,
      unregisterDatasetEditConfirmationHandler,
      registerUserConsentConfirmationHandler,
      unregisterUserConsentConfirmationHandler,
      registerEditScorersConfirmationHandler,
      unregisterEditScorersConfirmationHandler,
      registerCreateLLMScorerConfirmationHandler,
      unregisterCreateLLMScorerConfirmationHandler,
      registerCreateCodeScorerConfirmationHandler,
      unregisterCreateCodeScorerConfirmationHandler,
      registerDataSourceSelectionConfirmationHandler,
      unregisterDataSourceSelectionConfirmationHandler,
      registerRunBtqlConfirmationHandler,
      unregisterRunBtqlConfirmationHandler,
      allowRunningWithoutConsent,
      setAllowRunningWithoutConsent,
      timeRangeSettings,
      setTimeRangeSettings,
      flush: async () => {
        await loggingState.bgLogger().flush();
      },
    }),
    [
      tools,
      api_url,
      orgName,
      loggingState,
      user?.id,
      user?.email,
      projectId,
      orgId,
      registerTaskEditConfirmationHandler,
      unregisterTaskEditConfirmationHandler,
      registerDatasetEditConfirmationHandler,
      unregisterDatasetEditConfirmationHandler,
      registerUserConsentConfirmationHandler,
      unregisterUserConsentConfirmationHandler,
      registerEditScorersConfirmationHandler,
      unregisterEditScorersConfirmationHandler,
      registerCreateLLMScorerConfirmationHandler,
      unregisterCreateLLMScorerConfirmationHandler,
      registerCreateCodeScorerConfirmationHandler,
      unregisterCreateCodeScorerConfirmationHandler,
      registerDataSourceSelectionConfirmationHandler,
      unregisterDataSourceSelectionConfirmationHandler,
      registerRunBtqlConfirmationHandler,
      unregisterRunBtqlConfirmationHandler,
      // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
      // registerEditBTQLConfirmationHandler,
      // unregisterEditBTQLConfirmationHandler,
      allowRunningWithoutConsent,
      setAllowRunningWithoutConsent,
      timeRangeSettings,
      setTimeRangeSettings,
    ],
  );

  useEffect(() => {
    // @ts-ignore
    window.optimizationContext = value;
  }, [value]);

  return (
    <OptimizationContext.Provider value={value}>
      {children}
    </OptimizationContext.Provider>
  );
}

export function useOptimizationContext() {
  return useContext(OptimizationContext);
}

function normalizeArrowNumbers(
  object: Record<string, number | Uint32Array | null>,
) {
  return Object.fromEntries(
    Object.entries(object).map(([key, value]) => {
      if (value instanceof Uint32Array) {
        return [key, new BN(value, true).valueOf()];
      } else {
        return [key, value];
      }
    }),
  );
}

async function getInlineFunctions(
  getFunctions: ({
    useInlinePrompts,
  }: {
    useInlinePrompts: boolean;
  }) => Promise<TaskFunctionDefinition[]>,
): Promise<InlineTaskDefinition[]> {
  const functions = await getFunctions({ useInlinePrompts: true });
  return functions.map(parseInlineTaskDefinition);
}

//to-do: this was vibed hard -- need to be re-written.
async function makeExperimentSummary(
  summaryBreakdownData: SummaryBreakdownData,
  metricDefinitions: MetricDefinition[],
): Promise<GetSummaryToolResult[]> {
  return summaryBreakdownData.summary.experiments
    .map((task, idx): GetSummaryToolResult | undefined => {
      const scores: Record<string, ScoreSummary> = {};
      const metrics: Record<string, ScoreSummary> = {};
      for (const [key, value] of Object.entries(task.scores ?? {})) {
        const summary = {
          avg: roundNumber(value.avg),
          min: roundNumber(value.min),
          max: roundNumber(value.max),
        };
        if (isInternalMetric(key)) {
          // Skip internal or timing metrics
        } else if (
          [
            // TODO: Eventually we should more precisely distinguish metrics and scores.
            ...metricDefinitions.map((m) => m.field_name),
            "error",
          ].includes(key)
        ) {
          metrics[key] = summary;
        } else {
          scores[key] = summary;
        }
      }

      const index =
        task.experiment.index + (task.experiment.type === "comparison" ? 1 : 0);
      const taskName = `${task.experiment.name} (${index})`;

      // For experiments, we don't have access to function definitions,
      // so we return a minimal placeholder that satisfies the type requirements
      return {
        index,
        definition: {},
        taskName,
        scores,
        metrics,
      };
    })
    .filter((r) => r !== undefined);
}

async function makePlaygroundSummary(
  getFunctions: ({
    useInlinePrompts,
  }: {
    useInlinePrompts: boolean;
  }) => Promise<TaskFunctionDefinition[]>,
  summaryBreakdownData: SummaryBreakdownData,
): Promise<GetSummaryToolResult[]> {
  const functions = await getInlineFunctions(getFunctions);
  return summaryBreakdownData.summary.experiments
    .map((task, idx): GetSummaryToolResult | undefined => {
      const functionDef = functions[idx];
      if (!functionDef) {
        return undefined;
      }

      const { definition } = makeDefinition(functionDef);

      const scores: Record<string, ScoreSummary> = {};
      const metrics: Record<string, ScoreSummary> = {};

      for (const [key, value] of Object.entries(task.scores ?? {})) {
        const summary = {
          avg: roundNumber(value.avg),
          min: roundNumber(value.min),
          max: roundNumber(value.max),
        };
        if (isInternalMetric(key)) {
          // Skip internal or timing metrics
        } else if (
          [
            // TODO: Eventually we should more precisely distinguish metrics and scores.
            "prompt_tokens",
            "prompt_cached_tokens",
            "prompt_cache_creation_tokens",
            "completion_tokens",
            "total_tokens",
            "estimated_cost",
            "error",
          ].includes(key)
        ) {
          metrics[key] = summary;
        } else {
          scores[key] = summary;
        }
      }

      const index =
        task.experiment.index + (task.experiment.type === "comparison" ? 1 : 0);
      const taskName = `${task.experiment.name} (${index})`;
      return {
        index,
        taskName,
        definition,
        scores,
        metrics,
      };
    })
    .filter((r) => r !== undefined);
}

function makeDefinition(functionDef: InlineTaskDefinition): {
  isPrompt: boolean;
  definition: object;
} {
  const { origin: _, ...promptData } =
    "inline_prompt" in functionDef && functionDef.inline_prompt
      ? functionDef.inline_prompt
      : { origin: undefined };

  const functionData =
    "inline_function" in functionDef && functionDef.inline_function
      ? functionDef.inline_function
      : "inline_context" in functionDef &&
          "code" in functionDef &&
          functionDef.inline_context &&
          functionDef.code
        ? {
            type: "code" as const,
            data: {
              type: "inline" as const,
              runtime_context: functionDef.inline_context,
              code: functionDef.code,
            },
          }
        : undefined;
  const isPrompt =
    Object.keys(promptData).length > 0 || functionData?.type === "prompt";
  const result = isPrompt ? promptData : functionData;

  if (!result) {
    throw new Error("Failed to construct task definition");
  }

  return { isPrompt, definition: result };
}

async function getDatasetRows({
  conn,
  ids,
  tableQuery,
}: {
  conn: AsyncDuckDBConnection;
  ids: string[];
  tableQuery: string;
}): Promise<
  Record<
    string,
    { id: string; input: unknown; expected: unknown; metadata: unknown }
  >
> {
  if (ids.length === 0) {
    return {};
  }
  const results = await conn.query(
    `SELECT id, input, expected, metadata FROM (${tableQuery}) t
      WHERE
        id IN (${ids.map((id) => singleQuote(id)).join(",")})
      `,
  );
  return Object.fromEntries(
    results.toArray().map((row) => {
      const data = row.toJSON();

      const input = data.input ? safeDeserializeUnknown(data.input) : undefined;
      const expected = data.expected
        ? safeDeserializeUnknown(data.expected)
        : undefined;
      const metadata = data.metadata
        ? safeDeserializeUnknown(data.metadata)
        : undefined;

      return [
        data.id,
        {
          id: data.id,
          input,
          expected,
          metadata,
        },
      ];
    }),
  );
}

// -- The any here allows us to let any confirmation handler work
async function executeAsyncUIHandler<
  R,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  T extends ConfirmationData<any, R>,
>({
  data,
  handler,
  abortController,
}: {
  data: T;
  handler: (data: T) => void;
  abortController: AbortController | undefined;
}): Promise<R> {
  return await new Promise((resolve, reject) => {
    if (abortController?.signal.aborted) {
      return reject(
        new DOMException(
          "Operation aborted before confirmation dialog",
          "AbortError",
        ),
      );
    }

    const handleAbort = () => {
      abortController?.signal.removeEventListener("abort", handleAbort);
      reject(
        new DOMException(
          "Operation aborted by signal during confirmation",
          "AbortError",
        ),
      );
    };

    abortController?.signal.addEventListener("abort", handleAbort);

    const dataWithAbort: T = {
      ...data,
      onConfirm: (opts) => {
        abortController?.signal.removeEventListener("abort", handleAbort);

        // Final check after the promise has settled
        if (abortController?.signal.aborted) {
          // This catches cases where the abort happened while the promise was processing
          // but resolved/rejected for a non-abort reason just before the abort signal was processed.
          reject(
            new DOMException(
              "Operation aborted after task edit confirmation",
              "AbortError",
            ),
          );
        }

        resolve(data.onConfirm(opts));
      },
      onCancel: (reason, opts) => {
        abortController?.signal.removeEventListener("abort", handleAbort);
        reject(data.onCancel(reason, opts));
      },
    };

    try {
      handler(dataWithAbort);
    } catch (e) {
      reject(e);
    }
  });
}

const MAX_ALLOWED_BTQL_TEXT =
  10000 /* 10k tokens */ * 4; /* 4 characters per token */

function truncateIfExceedsLength(value: unknown, maxLength: number) {
  const valueString = typeof value === "string" ? value : JSON.stringify(value);
  if (valueString.length <= maxLength) {
    return value;
  }

  return safeDeserializePlainStringAsJSON(valueString.slice(0, maxLength));
}

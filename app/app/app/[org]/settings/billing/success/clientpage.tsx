"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export function ClientSuccessPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to billing page
    router.push("../billing");
  }, [router]);

  // Show a loading state while redirecting
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="mx-auto mb-4 size-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
        <p className="text-gray-600">Processing your payment...</p>
      </div>
    </div>
  );
}

import { type <PERSON><PERSON><PERSON> } from "@braintrust/local/optimization";
import {
  <PERSON><PERSON>d,
  TextSearch,
  CircleArrowUp,
  MessageSquareWarning,
  ChartColumnStacked,
  MessageCircle,
  Percent,
  Database,
  Code2,
  HelpCircle,
  AlignJustify,
} from "lucide-react";
import { But<PERSON> } from "#/ui/button";
import {
  type ContextObject,
  type UserMessage as UserMessageType,
} from "./use-global-chat-context";

export const EmptyStateContent = ({
  page,
  handleSendMessage,
  hasMultipleSelectedExperiments = false,
  contextObjects,
}: {
  page: PageKey;
  handleSendMessage: (
    userMessage: UserMessageType,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  hasMultipleSelectedExperiments?: boolean;
  contextObjects?: Record<string, ContextObject>;
}) => {
  if (page === "logs") {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to find insights in this project&apos;s logs.{" "}
          <br /> Loop will query using the configured logs page time range.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500 size-3.5"
            Icon={TextSearch}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Find the common failure modes in the logs`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Find common failure modes
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500 size-3.5"
            Icon={TextSearch}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Find the popular usecases in the logs`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Find popular usecases
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
        </div>
      </div>
    );
  } else if (page === "dataset") {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to generate synthetic data, and get insights
          about the dataset.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={Database}
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Generate 5 rows for this dataset`,
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Generate synthetic dataset rows
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500 size-3.5"
            Icon={AlignJustify}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Give me a detailed breakdown and insights about the contents of this dataset`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Summarize dataset rows
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={TextSearch}
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Find common user inputs and final outputs from logs and add to the dataset. User inputs can often be found in the parent span and final outputs can be found in the output of that same span.`,
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Add common inputs and outputs from logs to dataset
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={TextSearch}
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Find common errors from logs and take the inputs and outputs from the spans and add to the dataset.`,
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Add common errors from logs to dataset
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
        </div>
      </div>
    );
  } else if (page === "experiments") {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to get detailed breakdowns and insights about
          your experiments.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500 size-3.5"
            Icon={TextSearch}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Give me a detailed breakdown and insights about ${hasMultipleSelectedExperiments ? "selected experiments" : "in the base experiment"}`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Summarize{" "}
            {hasMultipleSelectedExperiments
              ? "selected experiments"
              : "the base experiment"}
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={MessageSquareWarning}
            iconClassName="text-primary-500 size-3.5"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Find the biggest problems in ${hasMultipleSelectedExperiments ? "selected experiments" : "in the experiment"}`,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: true,
                },
              );
            }}
          >
            Highlight problems in{" "}
            {hasMultipleSelectedExperiments
              ? "selected experiments"
              : "in the base experiment"}
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          {hasMultipleSelectedExperiments && (
            <Button
              size="xs"
              variant="border"
              className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
              Icon={ChartColumnStacked}
              iconClassName="text-primary-500 size-3.5"
              onClick={() => {
                handleSendMessage(
                  {
                    id: crypto.randomUUID(),
                    type: "user_message",
                    message: "Which experiment performed the best?",
                  },
                  {
                    clearContextObjects: false,
                    clearUserMessage: false,
                  },
                );
              }}
            >
              Which experiment performed the best?
              <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
            </Button>
          )}
        </div>
      </div>
    );
  } else if (page === "btql") {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to explore, write, and debug BTQL queries.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button-1 h-fit items-center justify-start gap-2 bg-background py-1 text-start font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500 size-3.5"
            Icon={Code2}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message:
                    "Generate a query to find the 10 recent errors from project logs in the last 24 hours.",
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Find the 10 most recent errors in logs in the last day
            <CircleArrowUp className="ml-auto size-3 shrink-0 text-primary-500 opacity-0 transition-opacity group-hover/button-1:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button-3 h-fit items-center justify-start gap-2 bg-background py-1 text-start font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={Percent}
            iconClassName="text-primary-500 size-3.5"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Generate a query to find the lowest scoring rows in an experiment`,
                  contextObjects,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Find the lowest scoring rows in an experiment
            <CircleArrowUp className="ml-auto size-3 shrink-0 text-primary-500 opacity-0 transition-opacity group-hover/button-3:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button-3 h-fit items-center justify-start gap-2 bg-background py-1 text-start font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={HelpCircle}
            iconClassName="text-primary-500 size-3.5"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Search the docs and show me how to use pivot and unpivot`,
                  contextObjects,
                },
                {
                  clearContextObjects: true,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Explain how to use <code>pivot</code> in BTQL
            <CircleArrowUp className="ml-auto size-3 shrink-0 text-primary-500 opacity-0 transition-opacity group-hover/button-3:opacity-100" />
          </Button>
        </div>
      </div>
    );
  } else {
    return (
      <div className="flex flex-1 flex-col justify-center px-2 text-start text-xs text-primary-500">
        <div className="mb-1 text-sm font-medium text-primary-700">
          Make every token count with{" "}
          <Blend className="inline-block size-3 -translate-y-px" /> Loop
        </div>
        <div>
          Start a conversation to optimize prompts, generate dataset rows, and
          automate eval development.
        </div>
        <div className="-mx-2 mt-2 flex flex-col gap-2 pt-2">
          <Button
            size="xs"
            variant="border"
            className="group/button-1 justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500"
            Icon={MessageCircle}
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: "Optimize the prompts in this playground",
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Optimize prompts
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={Percent}
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message:
                    "Create a code scorer for the task in this playground",
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Create code scorer
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>

          <Button
            size="xs"
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            Icon={Database}
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: `Generate 5 rows for the existing dataset. If none exists, generate a new dataset with 5 rows given what is present in the playground`,
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Generate dataset rows
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>

          <Button
            size="xs"
            Icon={Percent}
            variant="border"
            className="group/button justify-start gap-2 bg-background font-normal text-primary-600 hover:bg-primary-200/80"
            iconClassName="text-primary-500"
            onClick={() => {
              handleSendMessage(
                {
                  id: crypto.randomUUID(),
                  type: "user_message",
                  message: "Choose scorers for the task in this playground",
                },
                {
                  clearContextObjects: false,
                  clearUserMessage: false,
                },
              );
            }}
          >
            Choose scorers
            <CircleArrowUp className="ml-auto size-3 text-primary-500 opacity-0 transition-opacity group-hover/button:opacity-100" />
          </Button>
        </div>
      </div>
    );
  }
};

export const EmptyState = ({
  page,
  handleSendMessage,
  hasMultipleSelectedExperiments = false,
  contextObjects,
}: {
  page: PageKey;
  handleSendMessage: (
    userMessage: UserMessageType,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  hasMultipleSelectedExperiments?: boolean;
  contextObjects?: Record<string, ContextObject>;
}) => {
  return (
    <div className="flex size-full items-center px-3 py-2">
      <EmptyStateContent
        page={page}
        handleSendMessage={handleSendMessage}
        hasMultipleSelectedExperiments={hasMultipleSelectedExperiments}
        contextObjects={contextObjects}
      />
    </div>
  );
};

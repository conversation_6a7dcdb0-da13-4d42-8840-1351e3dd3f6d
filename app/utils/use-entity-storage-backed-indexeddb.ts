import { useEffect, useState } from "react";
import { useIndexedDBObject } from "./use-indexeddb-object";
import {
  useEntityStorageAccess,
  getEntityKey,
  type Entity,
  type EntityStorageSchema,
} from "#/lib/clientDataStorage";
import isEqual from "lodash.isequal";

export function useEntityStorageBackedIndexedDB<
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
>({
  entityType,
  entityIdentifier,
  key,
  defaultValue,
}: {
  entityType: E;
  entityIdentifier: string;
  key: K;
  defaultValue: EntityStorageSchema[E][K];
}) {
  const [migrationDone, setMigrationDone] = useState(false);

  const localStorage = useEntityStorageAccess({
    entityType,
    entityIdentifier,
    defaultValueOverride: defaultValue,
    persistDefaultValueOnMiss: false,
  });

  const fullKey = getEntityKey(entityType, entityIdentifier, String(key));

  const [
    indexedDBValue,
    setIndexedDBValue,
    clearIndexedDBValue,
    isIndexedDBLoaded,
  ] = useIndexedDBObject({
    store: "entityStorage",
    key: fullKey,
    defaultValue,
  });

  useEffect(() => {
    if (migrationDone || !isIndexedDBLoaded) return;
    setMigrationDone(true);

    const localStorageValue = localStorage.get(key);

    if (
      localStorageValue != null &&
      (indexedDBValue === undefined || isEqual(indexedDBValue, defaultValue))
    ) {
      setIndexedDBValue(localStorageValue);

      try {
        window.localStorage.removeItem(fullKey);
      } catch (e) {
        console.warn(
          `Failed to clear localStorage after migration for key: ${fullKey}`,
          e,
        );
      }
    }
  }, [
    entityType,
    entityIdentifier,
    key,
    localStorage,
    indexedDBValue,
    setIndexedDBValue,
    defaultValue,
    fullKey,
    isIndexedDBLoaded,
    migrationDone,
    setMigrationDone,
  ]);

  return [
    indexedDBValue,
    setIndexedDBValue,
    clearIndexedDBValue,
    migrationDone && isIndexedDBLoaded,
  ] as const;
}

import { z } from "zod";
import { type Tool } from "./types";
import { freshnessStateSchema, realtimeStateSchema } from "../../app-schema";
import {
  BTQLCapabilitiesNoClauses,
  BTQLClausesNoFrom,
  BTQLDescription,
  dataSourceSchema,
  entityType,
} from "./query";

export const runBtqlToolParams = z.object({
  title: z.string().describe(
    `A very concise title for this query. This is used to identify the query in the UI.

Examples:
- "Errors < 24 hrs"
- "Most active users"
- "User confusion"
- "Model performance"`,
  ),
  explanation: z.string().describe(
    `A brief explanation of what you're trying to find or analyze with this query. This helps provide context for the query results.

Examples:
- "Finding all errors in the last 24 hours to identify what's causing failures"
- "Analyzing which users are most active to understand usage patterns"
- "Looking for examples where users expressed confusion or frustration"
- "Comparing model performance across different scoring dimensions"`,
  ),

  /** now part of the query, but kept for rendering backwards compatibility */
  dataSource: dataSourceSchema,

  query: z.string().describe(
    `A BTQL query, including a from clause with an optional shape designator.

    The from clause and shape should match the dataSource and shape parameters.
    This looks like "from: <dataSource.entity>('dataSource.id') <shape>" where shape can be omitted.

IMPORTANT: Use dot notation for nested fields! If InferSchemaTool shows ['metadata', 'user_id'], use metadata.user_id in queries. Fields are case sensitive.

Examples of valid queries:
- "from: dataset('3e29ac38-5588-412a-a91c-7498c2f7277e') | select: *" - Get all fields from a dataset
- "from: project_logs('20db338f-26cb-4056-acd7-fe7cf7de0e05') traces | select: * | filter: metadata.user_id = 'user123' | limit: 10" - Find full traces from logs for specific user (NOT user_id alone!)
- "from: experiment('123e4567-e89b-12d3-a456-426614174000') | select: input, output, metadata.model | filter: metadata.model = 'gpt-4'" - Filter by nested field
- "from: project_logs('20db338f-26cb-4056-acd7-fe7cf7de0e05') | dimensions: metadata.model | measures: count(1) as calls, avg(scores.Factuality) as avg_score" - Group by model
- "from: project_logs('3e29ac38-5588-412a-a91c-7498c2f7277e') | dimensions: metadata.user_id | measures: count(1) as activity | sort: activity desc | limit: 10" - Most active users
- "from: project_logs('20db338f-26cb-4056-acd7-fe7cf7de0e05') | select: * | filter: scores.Factuality > 0.8" - Filter by score value (use scores.Factuality, not just Factuality)
- "from: project_logs('123e4567-e89b-12d3-a456-426614174000') | dimensions: hour(created) as hour | measures: count(error) as errors | filter: created > now() - interval 1 day" - Hourly errors
- "from: project_logs('20db338f-26cb-4056-acd7-fe7cf7de0e05') | select: metadata.model, metrics" - Get model and ALL metrics fields (NOT metrics.*, just metrics)`,
  ),
  /** now part of the query, but kept for rendering backwards compatibility */
  shape: z
    .enum(["spans", "traces", "summary"])
    .optional()
    .describe(
      `The shape of data to return:
- spans: Returns individual spans (default)
- traces: Returns all spans from traces that contain at least one matching span
- summary: Returns one row per trace with aggregated metrics`,
    ),
});

/**
 * Kept for backwards compatibility when displaying old chat sessions.
 * Remove ~november 2025
 */
export const runBtqlToolParamsLegacy = runBtqlToolParams.extend({
  dataSource: z
    .object({
      entity: entityType,
      id: z.string().optional(),
    })
    .optional(),
});

export type RunBtqlToolParameters = z.infer<typeof runBtqlToolParams>;

const btqlResponseSchema = z.object({
  data: z.array(z.record(z.unknown())),
  schema: z.object({
    type: z.literal("array"),
    items: z.object({
      type: z.literal("object"),
      properties: z.record(z.unknown()),
    }),
  }),
  cursor: z.string().optional(),
  realtime_state: realtimeStateSchema.optional(),
  freshness_state: freshnessStateSchema.optional(),
  /** Legacy, no longer included in tool output but kept for backwards compatibility when displaying old chat sessions. */
  dataSource: z
    .object({
      entity: z.string().describe("The entity type that was queried"),
      id: z.string().describe("The ID of the entity that was queried"),
    })
    .optional()
    .describe("The data source that was queried"),
});

export const runBtqlToolResultSchema = btqlResponseSchema;

export type RunBtqlToolResult = z.infer<typeof runBtqlToolResultSchema>;

const BTQLFromClauseDescription = `from: Specify the data source - can be an identifier (like "project_logs") or a function call (like "experiment('id')"). Has an optional designator for the shape of the data: spans, traces, summary. If not specified, defaults to spans`;

export const RunBtqlTool: Tool<RunBtqlToolParameters, RunBtqlToolResult> = {
  description: `Transform natural language or existing BTQL (Braintrust Query Language) queries into executable BTQL queries that match the user's intent.\n\n${BTQLDescription}\n\n${BTQLClausesNoFrom}\n${BTQLFromClauseDescription}\n\n${BTQLCapabilitiesNoClauses}`,
  parameters: runBtqlToolParams,
};

#!/bin/bash

# Script to deploy brainstore to EC2 instances
# Usage: ./deploy-brainstore.sh <tag> [-n <name>]

set -e

# Parse arguments
TAG=""
NAME_ARG=""

show_help() {
    cat << EOF
Deploy brainstore to EC2 instances

USAGE:
    $0 <tag> [-n <name>]

ARGUMENTS:
    <tag>           Docker image tag to deploy (e.g., hotfix-aarch64)

OPTIONS:
    -n, --name      Filter instances by name pattern (passed to ec2-list)
    -h, --help      Show this help message

EXAMPLES:
    $0 hotfix-aarch64 -n Writer
        Deploy hotfix-aarch64 tag to all Writer instances

    $0 my-tag
        Deploy my-tag to all instances

    $0 latest -n MyService
        Deploy latest tag to instances matching MyService

EOF
}

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--name)
            NAME_ARG="-n $2"
            shift 2
            ;;
        *)
            if [[ -z "$TAG" ]]; then
                TAG="$1"
                shift
            else
                echo "Error: Unknown argument $1"
                echo "Usage: $0 <tag> [-n <name>]"
                exit 1
            fi
            ;;
    esac
done

if [[ -z "$TAG" ]]; then
    echo "Error: Tag is required"
    echo "Usage: $0 <tag> [-n <name>]"
    exit 1
fi

echo "Deploying brainstore:$TAG to instances..."
if [[ -n "$NAME_ARG" ]]; then
    echo "Using name filter: $NAME_ARG"
fi

# Get list of instances
INSTANCES=$(ec2-list $NAME_ARG prod | cut -f 1)

if [[ -z "$INSTANCES" ]]; then
    echo "No instances found"
    exit 1
fi

echo "Found instances:"
echo "$INSTANCES"
echo

# Deploy to each instance
for instance in $INSTANCES; do
    echo "Deploying to $instance..."

    ec2-connect $instance "
        echo 'Pulling new image...'
        sudo docker pull public.ecr.aws/braintrust/brainstore:$TAG

        echo 'Stopping existing container...'
        sudo docker stop brainstore || true

        echo 'Removing existing container...'
        sudo docker rm brainstore || true

        echo 'Starting new container...'
        sudo docker run -d \\
          --name brainstore \\
          --env-file /etc/brainstore.env \\
          --restart always \\
          --network host \\
          -v /mnt/tmp/brainstore:/mnt/tmp/brainstore \\
          public.ecr.aws/braintrust/brainstore:$TAG \\
          web --cache-dir /mnt/tmp/brainstore

        echo 'Deployment complete for $instance'
    "

    echo "✓ Completed deployment to $instance"
    echo
done

echo "All deployments completed!"

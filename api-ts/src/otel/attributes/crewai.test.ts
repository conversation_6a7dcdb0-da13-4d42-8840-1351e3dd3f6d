import { describe, expect, test } from "vitest";
import { otelSpanToRow } from "../collector";

describe("crewai higher-level OTEL integration", () => {
  test("workflow span derives input from tasks and output from result", () => {
    const span = {
      traceId: "0123456789abcdef0123456789abcdef",
      spanId: "abcdeffedcba0123",
      parentSpanId: null,
      name: "crewai.workflow",
      startTimeUnixNano: "1",
      endTimeUnixNano: "2",
      attributes: [
        { key: "crewai", value: {} },
        {
          key: "crewai.crew.tasks",
          value: {
            stringValue:
              '[{"agent": "Software developer", "description": "Create a minimal HTML page titled"}]',
          },
        },
        {
          key: "crewai.crew.result",
          value: {
            stringValue:
              "```html\n<h1>Hello World! Braintrust monitors your CrewAI agent!</h1>\n```",
          },
        },
        { key: "crewai.crew.id", value: { stringValue: "7dc21389-a4dc" } },
      ],
      status: {},
    };

    const { row } = otelSpanToRow(span);
    expect(row).toMatchObject({
      input: [
        {
          role: "user",
          content: "Create a minimal HTML page titled",
        },
      ],
      output: [
        {
          role: "assistant",
          content:
            "<h1>Hello World! Braintrust monitors your CrewAI agent!</h1>",
        },
      ],
      span_attributes: {
        name: "crewai.workflow",
        type: "task",
      },
    });
  });

  test("tool usage span formats input/output and span type/name", () => {
    const span = {
      traceId: "abcdef0123456789abcdef0123456789",
      spanId: "0011223344556677",
      parentSpanId: "8899aabbccddeeff",
      name: "Tool Usage",
      startTimeUnixNano: "10",
      endTimeUnixNano: "20",
      attributes: [
        { key: "crewai_version", value: { stringValue: "0.1.0" } },
        { key: "tool_name", value: { stringValue: "get_html_boilerplate" } },
      ],
      status: {},
    };

    const { row } = otelSpanToRow(span);
    expect(row).toMatchObject({
      span_attributes: {
        name: "get_html_boilerplate",
        type: "tool",
      },
    });
  });

  test("chat messages and agent tools with request model", () => {
    const span = {
      traceId: "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
      spanId: "bbbbbbbbbbbbbbbb",
      parentSpanId: null,
      name: "crewai.chat",
      startTimeUnixNano: "100",
      endTimeUnixNano: "200",
      attributes: [
        { key: "crewai", value: {} },
        {
          key: "crewai.request.model",
          value: { stringValue: "openai/gpt-4o-mini" },
        },
        {
          key: "crewai.input.messages",
          value: {
            stringValue:
              '[{"role":"user","parts":[{"type":"text","content":"Hello"}]}]',
          },
        },
        {
          key: "crewai.output.messages",
          value: {
            stringValue:
              '[{"role":"assistant","parts":[{"type":"text","content":"Hi there"}]}]',
          },
        },
        { key: "crewai.operation.name", value: { stringValue: "chat" } },
      ],
      status: {},
    };

    const { row } = otelSpanToRow(span);
    expect(row).toMatchObject({
      input: [{ role: "user", content: "Hello" }],
      output: [{ role: "assistant", content: "Hi there" }],
      metadata: {
        model: "gpt-4o-mini",
      },
    });
  });
});

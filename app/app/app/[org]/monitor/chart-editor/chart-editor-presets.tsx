import { useCallback } from "react";
import { MONITOR_PRESET_CARDS } from "../card-config/monitor-cards.constants";
import { MonitorCardIcon } from "../card/monitor-card-icon";
import { cn } from "#/utils/classnames";
import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";

export const ChartEditorPresets = ({
  selectedPresetId,
  onChange,
}: {
  selectedPresetId?: string;
  onChange: (presetId?: string) => void;
}) => {
  const CustomIcon = MonitorCardIcon("custom-chart");

  const onValueChange = useCallback(
    (presetId: string) => {
      const isValid = [...MONITOR_PRESET_CARDS.keys()].includes(presetId);
      onChange(isValid ? presetId : undefined);
    },
    [onChange],
  );

  return (
    <div className="flex flex-wrap justify-start gap-2">
      <Button
        size="xs"
        className={cn({
          "bg-accent-100 hover:bg-accent-200":
            selectedPresetId === "custom" || !selectedPresetId,
        })}
        Icon={CustomIcon}
        onClick={() => onValueChange("custom")}
      >
        Custom
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          {selectedPresetId && selectedPresetId !== "custom" ? (
            <Button
              size="xs"
              className="bg-accent-100 hover:bg-accent-200"
              Icon={MonitorCardIcon(
                MONITOR_PRESET_CARDS.get(selectedPresetId)?.header?.iconName ??
                  "custom-chart",
              )}
              isDropdown
            >
              {MONITOR_PRESET_CARDS.get(selectedPresetId)?.header?.title ??
                selectedPresetId}
            </Button>
          ) : (
            <Button size="xs" isDropdown>
              Presets
            </Button>
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          {[...MONITOR_PRESET_CARDS.entries()].map(([id, card]) => {
            const Icon = MonitorCardIcon(
              card.header?.iconName ?? "custom-chart",
            );
            return (
              <DropdownMenuItem key={id} onSelect={() => onValueChange(id)}>
                <Icon className="size-3" />
                {card.header?.title ?? card.name}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

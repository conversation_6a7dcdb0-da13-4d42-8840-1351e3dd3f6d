import { type FunctionObjectType } from "@braintrust/typespecs";
import {
  type MetaFields,
  type ObjectType,
} from "#/ui/prompts/function-editor/types";
import { useRowAuditLog } from "#/ui/trace/query";
import { Activity } from "#/ui/trace/activity";
import { type CommentData } from "@braintrust/local/api-schema";
import { newId } from "braintrust";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type CommentFn, type DeleteCommentFn } from "#/utils/mutable-object";
import { TransactionIdField } from "@braintrust/local/query";
import { isEmpty, prettifyXact } from "braintrust/util";
import { useOpenedPlaygroundPrompt } from "#/app/app/[org]/p/[project]/prompts/open";
import { DataTextEditor } from "#/ui/data-text-editor";
import { excludeKeys } from "#/app/app/[org]/p/[project]/prompts/utils";
import { Edit3 } from "lucide-react";
import { Button } from "#/ui/button";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { toast } from "sonner";
import { atom, useAtomValue } from "jotai";
import isEqual from "lodash.isequal";
import {
  useEditPromptVersionState,
  usePromptVersionState,
} from "#/ui/query-parameters";
import { LibraryItemLinks } from "#/app/app/[org]/p/[project]/library/library-item-links";
import useEvent from "react-use-event-hook";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "#/ui/dialog";
import { Switch } from "#/ui/switch";

const EXCLUDED_KEYS = [
  "id",
  "project_id",
  "org_id",
  "_xact_id",
  "function_schema",
  "origin",
  "log_id",
];
const ALLOWED_RENDER_OPTIONS = ["json" as const, "yaml" as const];

export function FunctionDetailActivity({
  commentOn,
  deleteComment,
  auditLogReady,
  auditLogScan,
  objectType,
  func,
  getOutputPrompt,
  type,
  isActive,
  modeType,
  projectName,
  setMetaFields,
}: {
  isActive: boolean;
  auditLogReady: number[];
  auditLogScan: string | null;
  commentOn: CommentFn;
  deleteComment: DeleteCommentFn;
  func: UIFunction;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  objectType: ObjectType;
  type: FunctionObjectType;
  modeType: "create" | "update";
  projectName: string;
  setMetaFields: Dispatch<SetStateAction<MetaFields>>;
}) {
  const { auditLogData } = useRowAuditLog({
    auditLogScan,
    auditLogReady,
    rowId: func.id,
    dynamicObjectId: null,
    objectType,
  });

  const [selectedVersionUrl, setSelectedVersion] = usePromptVersionState();
  const selectedVersion = selectedVersionUrl ?? func[TransactionIdField];
  const [editFromPromptVersion] = useEditPromptVersionState();

  const { prompt: versionedPrompt, status } = useOpenedPlaygroundPrompt({
    promptId: func.id,
    projectId: func.project_id,
    promptVersion: selectedVersion ?? undefined,
  });
  // Save the previous versioned prompt to avoid flickering
  const prevVersionedPrompt = useRef(versionedPrompt);
  useEffect(() => {
    // When saving a new prompt, the latest versioned prompt will be transiently empty
    if (isEmpty(versionedPrompt) || Object.keys(versionedPrompt).length === 0) {
      return;
    }
    prevVersionedPrompt.current = versionedPrompt;
  }, [versionedPrompt]);

  const { resetPrompt, resetFunction_ROOT, sortedSyncedPromptsAtom_ROOT } =
    useSyncedPrompts();
  const livePrompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const onResetPrompt = useEvent(() => {
    if (
      status !== "loaded" ||
      (!versionedPrompt?.prompt_data && !versionedPrompt?.function_data)
    ) {
      toast.error(`Could not reset ${type} (still initializing)`);
      return;
    }

    // Reset meta fields
    setMetaFields({
      name: versionedPrompt.name,
      slug: versionedPrompt.slug,
      description: versionedPrompt.description,
      metadata: versionedPrompt.metadata,
    });

    // Reset synced prompt state
    return type === "prompt"
      ? resetPrompt({
          id: func.id,
          prompt: versionedPrompt,
          includeOrigin: false,
        })
      : resetFunction_ROOT({
          id: func.id,
          prompt: versionedPrompt,
          includeOrigin: false,
        });
  });

  // If the user has requested to edit the prompt from a specific version, set the editor contents to that version _once_ on mount.
  // We do this here rather than just fetching the "base" prompt as the versioned prompt, because we want dirtiness checking/diffing/etc. to work off the base prompt.
  const hasSetEditorToVersion = useRef(false);
  useEffect(() => {
    if (
      editFromPromptVersion &&
      status === "loaded" &&
      !hasSetEditorToVersion.current
    ) {
      hasSetEditorToVersion.current = true;
      onResetPrompt();
    }
  }, [editFromPromptVersion, status, onResetPrompt]);

  const addComment = useCallback(
    async (comment: CommentData) => {
      const commentId = newId();
      const transactionId = await commentOn([
        {
          id: commentId,
          row: func,
          comment,
        },
      ]);
      return { transactionId, commentId };
    },
    [commentOn, func],
  );

  const diffDialogData = useMemo(() => {
    if (!isActive) {
      return { current: null, versioned: null, isEqual: true } as const;
    }
    const current = excludeKeys(getOutputPrompt(livePrompt), EXCLUDED_KEYS);
    const versionedPromptToUse =
      // eslint-disable-next-line react-compiler/react-compiler
      status === "loading" ? prevVersionedPrompt.current : versionedPrompt;
    const versionedPromptExcludedKeys =
      versionedPromptToUse?.function_data.type === "code"
        ? [...EXCLUDED_KEYS, "prompt_data"]
        : EXCLUDED_KEYS;
    const versioned = excludeKeys(
      versionedPromptToUse,
      versionedPromptExcludedKeys,
    );
    return {
      current,
      versioned,
      isEqual: isEqual(current, versioned),
    } as const;
  }, [isActive, getOutputPrompt, livePrompt, status, versionedPrompt]);

  const onResetFromDiff = useEvent(() => {
    onResetPrompt();
    setSelectedVersion(null);
    toast.info(`Reverted ${type} to ${prettifyXact(selectedVersion ?? "")}`);
  });

  const [diffActive, setDiffActive] = useState(false);
  const versionChip = (
    <span className="rounded-md bg-accent-50 px-1 font-mono text-accent-700">
      {prettifyXact(selectedVersion ?? "")}
    </span>
  );
  const diffDescription = (
    <span>
      Latest version {"<-"} this version ({versionChip})
    </span>
  );

  return (
    <div className="flex flex-1">
      <div className="flex flex-1 flex-col gap-3 overflow-y-scroll px-3 py-4">
        {modeType !== "create" && (
          <>
            <div>
              <LibraryItemLinks
                projectName={projectName}
                objectType={type}
                objectId={func.id}
                objectName={func.name ?? undefined}
                objectSlug={func.slug ?? undefined}
              />
            </div>
          </>
        )}
        <Activity
          auditLog={auditLogData}
          addComment={addComment}
          deleteComment={deleteComment}
          objectName={type}
          onSelectVersion={setSelectedVersion}
          // Only show selected version when the we're viewing the latest version with no changes
          selectedVersion={
            diffDialogData.isEqual &&
            selectedVersion === func[TransactionIdField]
              ? selectedVersion
              : undefined
          }
          className="mx-0"
          entityName={func.name}
          functionType={type}
          objectId={func.id}
        />
        <Dialog
          open={selectedVersionUrl !== null}
          onOpenChange={(newOpen) => {
            if (!newOpen) {
              setSelectedVersion(null);
              setDiffActive(false);
            }
          }}
        >
          <DialogContent className="w-[min(95vw,1000px)] sm:max-w-3xl">
            <DialogHeader>
              <DialogTitle>
                <span className="capitalize">{type}</span> version{" "}
                <span className="text-sm font-normal">{versionChip}</span>
              </DialogTitle>
              {(selectedVersionUrl !== func[TransactionIdField] ||
                !diffDialogData.isEqual) && (
                <div className="mt-2 flex gap-2">
                  <Button size="xs" Icon={Edit3} onClick={onResetFromDiff}>
                    Edit {type} from this version
                  </Button>
                  <Button
                    size="xs"
                    onClick={() => {
                      setDiffActive(!diffActive);
                    }}
                  >
                    Diff <Switch className="scale-90" checked={diffActive} />{" "}
                    {diffActive && diffDescription}
                  </Button>
                </div>
              )}
            </DialogHeader>
            <div className="pt-1">
              <DataTextEditor
                // Key is necessary since toggling changing diff value from defined to undefined can leave the editor in diffed state
                key={diffActive ? "diff-prompt-viewer" : "prompt-viewer"}
                allowedRenderOptions={ALLOWED_RENDER_OPTIONS}
                diffValue={diffActive ? diffDialogData.current : undefined}
                readOnly
                value={diffDialogData.versioned}
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}

/**
 * Utility functions for analytics tracking
 */

/**
 * Extract meaningful page names from URL paths
 * Converts full paths like "/app/orgname/p/projectname/logs" to clean names like "logs"
 */
export function getPageName(pathname: string): string {
  // Extract meaningful page names from paths
  if (pathname.includes("/logs")) return "logs";
  if (pathname.includes("/playgrounds")) return "playgrounds";
  if (pathname.includes("/experiments")) return "experiments";
  if (pathname.includes("/datasets")) return "datasets";
  if (pathname.includes("/prompts")) return "prompts";
  if (pathname.includes("/functions")) return "functions";
  if (pathname.includes("/scorers")) return "scorers";
  if (pathname.includes("/settings/billing")) return "billing";
  if (pathname.includes("/settings/team")) return "team-settings";
  if (pathname.includes("/settings")) return "settings";
  if (pathname.includes("/p/")) return "project";
  if (pathname.includes("/app/")) return "dashboard";
  return "unknown";
}

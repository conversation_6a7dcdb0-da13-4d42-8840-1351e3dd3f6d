import { cn } from "#/utils/classnames";
import Markdown, {
  type Options,
  type Components,
  defaultUrlTransform,
} from "react-markdown";
import remarkGfm from "remark-gfm";
import { SyntaxHighlight } from "./syntax-highlighter";
import { CopyToClipboardButton } from "./copy-to-clipboard-button";
import { Copy } from "lucide-react";
import { memo, useContext, useMemo } from "react";
import { type BundledLanguage } from "./highlight";
import { isJSON } from "#/utils/is-json";
import { useDebounce } from "#/utils/useDebouncedCallback";
import { OrgUsersContext } from "#/utils/org-users-context";
import {
  Tooltip,
  TooltipContent,
  TooltipPortal,
  TooltipTrigger,
} from "./tooltip";
import { Avatar } from "./avatar";
import remarkMention from "#/utils/markdown/remark-mention";
import {
  mentionRegexCapture,
  userIdMatchRegex,
} from "#/utils/email/mentions-utils";
import { externalLinks } from "#/lib/external-links";
import Link from "next/link";
import { useSearchParams } from "next/navigation";

const urlScheme = /^[a-zA-Z]+:/;

const DEBOUNCE_DELAY = 300;
const LARGE_CONTENT_THRESHOLD = 100 * 1024; // 100KB

export const MD_PROSE_CLASS_NAMES =
  "prose prose-sm prose-headings:font-semibold max-w-full break-words py-2 font-inter leading-snug dark:prose-invert prose-pre:p-0 prose-ol:list-decimal prose-ul:list-disc prose-a:underline prose-a:font-medium prose-zinc prose-a:underline-offset-2 prose-a:hover:text-accent-600 prose-hr:my-4 prose-pre:m-0 prose-code:text-xs prose-code:whitespace-pre-wrap";

function UserMention({ mentionMatch }: { mentionMatch: string }) {
  const { orgUsers } = useContext(OrgUsersContext);
  const user = useMemo(() => {
    return Object.values(orgUsers || {}).find((u) => u.id === mentionMatch);
  }, [mentionMatch, orgUsers]);

  if (!user && !userIdMatchRegex.test(mentionMatch)) {
    return `@${mentionMatch}`;
  }

  const userName =
    user?.given_name && user.family_name
      ? `${user.given_name} ${user.family_name}`
      : undefined;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="text-sm font-medium text-accent-600">
          @{userName || user?.email || "Unknown"}
        </span>
      </TooltipTrigger>
      <TooltipPortal>
        {user && (
          <TooltipContent>
            <div className="flex gap-2">
              <Avatar imgUrl={user.avatar_url} size="sm" />
              <div className="flex flex-col gap-1">
                <div className="text-sm font-medium text-primary-900">
                  {userName || user.email || "Unknown"}
                </div>
                {!!userName && (
                  <div className="text-xs text-gray-500">{user.email}</div>
                )}
              </div>
            </div>
          </TooltipContent>
        )}
      </TooltipPortal>
    </Tooltip>
  );
}

const MentionRenderer: Components["p"] = memo(
  ({ children, className, node: _node, ref: _ref, ...rest }) => {
    try {
      if (typeof children === "object" && Array.isArray(children)) {
        return children.map((child, i) => {
          if (typeof child === "string") {
            const match = child.match(mentionRegexCapture);
            if (match) {
              return <UserMention key={i} mentionMatch={match[1]} />;
            }
          }
          return child;
        });
      }
    } catch {}
    if (typeof children === "string") {
      const match = children.match(mentionRegexCapture);
      if (match) {
        return <UserMention mentionMatch={match[1]} />;
      }
    }
    return children;
  },
);
MentionRenderer.displayName = "MentionRenderer";

function parseRelativeHref(
  href: string | undefined,
  searchParams: URLSearchParams | null,
) {
  if (!href) {
    return null;
  }

  if (
    // Skip if it starts with mailto:, tel:, or other protocols
    !href.startsWith("http") &&
    urlScheme.test(href)
  ) {
    return null;
  }

  if (href.startsWith("#")) {
    return null;
  }

  if (href.startsWith("/")) {
    return withMergedSearchParams(href, searchParams);
  }

  if (href.startsWith("?")) {
    return withMergedSearchParams(href, searchParams, true);
  }

  try {
    const url = new URL(href);

    // Check if it's an absolute URL at the same pathname
    if (url.hostname === window.location.hostname) {
      return withMergedSearchParams(url.pathname, searchParams);
    }
  } catch {}

  return null;
}

function withMergedSearchParams(
  urlPath: string,
  existing: URLSearchParams | null,
  ignorePath: boolean = false,
) {
  const params = new URLSearchParams(existing?.toString() ?? undefined);

  try {
    const parsed = new URL(urlPath, window.location.origin);
    for (const [k, v] of parsed.searchParams.entries()) {
      params.set(k, v);
    }
    const paramString = params.toString();
    return `${ignorePath ? "" : parsed.pathname}${paramString ? `?${paramString}` : ""}${parsed.hash}`;
  } catch {}

  return null;
}

const LinkRenderer: Components["a"] = (props) => {
  const {
    target,
    href,
    children,
    className,
    node: _node,
    ref: _ref,
    ...rest
  } = props;
  const searchParams = useSearchParams();
  const relativeHref =
    target !== "_blank" && parseRelativeHref(href, searchParams);
  if (relativeHref) {
    return (
      <Link className={className} href={relativeHref} {...rest}>
        {children}
      </Link>
    );
  }

  return <a {...props} />;
};

const CodeRenderer: Components["code"] = memo(
  ({ children, className, node: _node, ref: _ref, ...rest }) => {
    const match = useMemo(
      () => /language-(\w+)/.exec(className || ""),
      [className],
    );
    const value = useMemo(
      () => String(children).replace(/\n$/, ""),
      [children],
    );

    if (children === undefined) {
      return null;
    }

    return match ? (
      <div className="group/codeblock mb-3 flex flex-col rounded-md border bg-primary-50">
        <div className="flex flex-none items-center justify-between pt-2 pr-2 pl-3">
          <span className="font-inter text-[10px] tracking-wide text-primary-400 uppercase">
            {match[1]}
          </span>
          <CopyToClipboardButton
            textToCopy={value}
            size="xs"
            variant="ghost"
            className="text-primary-500 opacity-0 transition-opacity group-hover/codeblock:opacity-100"
          >
            <Copy className="size-3" />
          </CopyToClipboardButton>
        </div>
        <SyntaxHighlight
          {...rest}
          className="bg-transparent px-3 pb-2"
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          language={match[1] as BundledLanguage}
          content={value}
        />
      </div>
    ) : (
      <code {...rest} className={className}>
        {children}
      </code>
    );
  },
);
CodeRenderer.displayName = "CodeRenderer";

const CodeRendererWithoutHighlighting: Components["code"] = memo(
  ({ children, className, node: _node, ref: _ref, ...rest }) => {
    const isCodeBlock = useMemo(
      () => /language-(\w+)/.exec(className || ""),
      [className],
    );

    if (!isCodeBlock) {
      return (
        <code {...rest} className={className}>
          {children}
        </code>
      );
    }

    return (
      <span
        {...rest}
        className={cn(
          className,
          "block rounded-md border border-primary-200/80 p-2.5! font-mono whitespace-pre-wrap text-primary-600",
        )}
      >
        {children}
      </span>
    );
  },
);

CodeRendererWithoutHighlighting.displayName = "CodeRendererWithoutHighlighting";

const REHYPE_PLUGINS: Options["rehypePlugins"] = [remarkGfm, externalLinks];
const REHYPE_PLUGINS_LIGHT: Options["rehypePlugins"] = [externalLinks];
const REMARK_PLUGINS: Options["remarkPlugins"] = [remarkMention];

const BASE_COMPONENTS = {
  a: LinkRenderer,
};

const TEXT_COMPONENTS = {
  code: CodeRenderer,
  p: MentionRenderer,
};

const TEXT_COMPONENTS_WITHOUT_HIGHLIGHTING = {
  code: CodeRendererWithoutHighlighting,
  p: MentionRenderer,
};

const checkContentSize = (
  content: string,
): {
  isLarge: boolean;
  size: number;
  error?: string;
} => {
  const size = new Blob([content]).size;

  return {
    isLarge: size > LARGE_CONTENT_THRESHOLD,
    size,
  };
};

function MarkdownViewerComponent({
  value,
  disableHighlighting,
  className,
  components: customComponents,
  urlTransform: customUrlTransform,
}: {
  value: string;
  disableHighlighting?: boolean;
  className?: string;
  components?: Partial<Components>;
  urlTransform?: Options["urlTransform"];
}) {
  const contentInfo = useMemo(() => checkContentSize(value), [value]);

  const debouncedValue = useDebounce(
    value,
    contentInfo.isLarge ? DEBOUNCE_DELAY : 0,
  );

  const processedValue = contentInfo.isLarge ? debouncedValue : value;

  const jsonString = useMemo(() => {
    if (!isJSON(processedValue)) {
      return;
    }
    let jsonString = processedValue.trim();
    try {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      jsonString = JSON.stringify(JSON.parse(jsonString as string), null, 2);
      return `\`\`\`json\n${jsonString}\n\`\`\``;
    } catch (e) {
      return undefined;
    }
  }, [processedValue]);

  const rehypePlugins = useMemo(() => {
    return contentInfo.isLarge ? REHYPE_PLUGINS_LIGHT : REHYPE_PLUGINS;
  }, [contentInfo.isLarge]);

  const components = useMemo(() => {
    const shouldDisableHighlighting =
      disableHighlighting || contentInfo.isLarge;
    const textComponents = shouldDisableHighlighting
      ? TEXT_COMPONENTS_WITHOUT_HIGHLIGHTING
      : TEXT_COMPONENTS;
    const baseComponents = { ...BASE_COMPONENTS, ...textComponents };
    return customComponents
      ? { ...baseComponents, ...customComponents }
      : baseComponents;
  }, [disableHighlighting, contentInfo.isLarge, customComponents]);

  const urlTransform = customUrlTransform || defaultUrlTransform;

  return (
    <article className={cn(MD_PROSE_CLASS_NAMES, className)}>
      <Markdown
        components={components}
        rehypePlugins={rehypePlugins}
        remarkPlugins={REMARK_PLUGINS}
        urlTransform={urlTransform}
      >
        {jsonString ?? processedValue}
      </Markdown>
    </article>
  );
}

export const MarkdownViewer = memo(MarkdownViewerComponent);
MarkdownViewer.displayName = "MarkdownViewer";

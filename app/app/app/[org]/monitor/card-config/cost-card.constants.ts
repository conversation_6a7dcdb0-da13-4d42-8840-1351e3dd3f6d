import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const COST_CARD_CONFIG: MonitorCardConfig = {
  header: {
    title: "Total LLM cost",
    iconName: "dollar",
  },
  name: "Cost query",
  idName: "cost",
  vizType: "bars",
  applyCrunchCosts: true,
  measures: [
    {
      alias: "promptUncachedTokens",
      displayName: "Prompt (uncached)",
      btql: "sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0)",
    },
    {
      alias: "promptCachedTokens",
      displayName: "Prompt (cache read)",
      btql: "sum(metrics.prompt_cached_tokens)",
    },
    {
      alias: "promptCacheCreationTokens",
      displayName: "Prompt (cache write)",
      btql: "sum(metrics.prompt_cache_creation_tokens)",
    },
    {
      alias: "completionTokens",
      displayName: "Completion",
      btql: "sum(metrics.completion_tokens)",
    },
  ],
  unitType: "cost",
  additionalGroupBy: { alias: "model", btql: "metadata.model" },
};

"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "#/ui/dialog";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { Input } from "#/ui/input";
import { Button } from "#/ui/button";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useOrg } from "#/utils/user";
import { getPageName } from "#/utils/analytics";

interface CouponModalProps {
  open: boolean;
  onClose: () => void;
  onCouponApplied: (couponCode: string) => void;
  flowId: string;
}

type ValidateCouponFn = (args: { couponCode: string }) => Promise<{
  isValid: boolean;
}>;

export function CouponModal({
  open,
  onClose,
  onCouponApplied,
  flowId,
}: CouponModalProps) {
  const [couponCode, setCouponCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getToken } = useAuth();
  const { track } = useAppAnalytics();
  const org = useOrg();

  const validateCoupon = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsValidating(true);
    setError(null);

    // Track coupon validation start
    track("checkoutFormStep", {
      orgName: org?.name ?? "",
      orgId: org?.id ?? "",
      flowId,
      stepType: "coupon_validation_start",
      sourcePage:
        typeof window !== "undefined"
          ? getPageName(window.location.pathname)
          : "",
      fieldName: "coupon_code",
    });

    try {
      const result = await invokeServerAction<ValidateCouponFn>({
        fName: "validateCoupon",
        args: { couponCode },
        getToken,
      });

      if (result?.isValid) {
        // Track coupon validation success
        track("checkoutFormStep", {
          orgName: org?.name ?? "",
          orgId: org?.id ?? "",
          flowId,
          stepType: "coupon_validation_success",
          sourcePage:
            typeof window !== "undefined"
              ? getPageName(window.location.pathname)
              : "",
          fieldName: "coupon_code",
        });

        // Track coupon applied
        track("checkoutFormStep", {
          orgName: org?.name ?? "",
          orgId: org?.id ?? "",
          flowId,
          stepType: "coupon_applied",
          sourcePage:
            typeof window !== "undefined"
              ? getPageName(window.location.pathname)
              : "",
          fieldName: "coupon_code",
        });

        onCouponApplied(couponCode);
        onClose();
      } else {
        // Track coupon validation error
        track("checkoutFormStep", {
          orgName: org?.name ?? "",
          orgId: org?.id ?? "",
          flowId,
          stepType: "coupon_validation_error",
          sourcePage:
            typeof window !== "undefined"
              ? getPageName(window.location.pathname)
              : "",
          fieldName: "coupon_code",
          errorMessage: "Invalid coupon code",
        });

        setError("Invalid coupon code");
      }
    } catch (err) {
      // Track coupon validation error
      track("checkoutFormStep", {
        orgName: org?.name ?? "",
        orgId: org?.id ?? "",
        flowId,
        stepType: "coupon_validation_error",
        sourcePage:
          typeof window !== "undefined"
            ? getPageName(window.location.pathname)
            : "",
        fieldName: "coupon_code",
        errorMessage: "Failed to validate coupon",
      });

      setError("Failed to validate coupon");
    } finally {
      setIsValidating(false);
    }
  };

  const handleClose = () => {
    // Track coupon modal closed
    track("checkoutFormStep", {
      orgName: org?.name ?? "",
      orgId: org?.id ?? "",
      flowId,
      stepType: "coupon_modal_closed",
      sourcePage:
        typeof window !== "undefined"
          ? getPageName(window.location.pathname)
          : "",
    });
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="pb-4">Apply Coupon</DialogTitle>
        </DialogHeader>
        <form onSubmit={validateCoupon} className="space-y-4">
          <div>
            <label htmlFor="couponCode" className="block text-xs font-medium">
              Coupon code
            </label>
            <Input
              id="couponCode"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value)}
              placeholder="Enter coupon code"
              className="mt-2"
            />
            {error && <p className="mt-2 text-sm text-bad-600">{error}</p>}
          </div>
          <div className="flex justify-end gap-3">
            <Button type="button" variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              variant="primary"
              type="submit"
              isLoading={isValidating}
              disabled={!couponCode}
            >
              Apply
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

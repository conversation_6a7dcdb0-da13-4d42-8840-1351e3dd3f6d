export const dataObjectTypes = [
  "dataset",
  "experiment",
  "prompt_session",
  "playground_logs",
  "project_logs",
  "project_prompts",
  "org_prompts",
  "project_functions",
  "org_functions",
] as const;

let _btCacheDB: IDBDatabase | null = null;
let _btCacheDBPromise: Promise<IDBDatabase> | null = null;
const _btCacheVersion = 15; // Most recent update: added entityStorage
let alerted = false;

export async function loadBtCacheDB(): Promise<IDBDatabase> {
  if (_btCacheDB !== null) {
    return _btCacheDB;
  }

  if (_btCacheDBPromise !== null) {
    return _btCacheDBPromise;
  }

  _btCacheDBPromise = new Promise((resolve, reject) => {
    const btCacheDBOpenRequest = indexedDB.open("btcache", _btCacheVersion);
    const upgraded = setTimeout(() => {
      if (alerted) {
        return;
      }
      alerted = true;
      console.error("Database upgrade took too long, aborting.");
      alert(
        "Sorry, there was a problem updating IndexedDB. Please close all Braintrust tabs and restart your browser.",
      );
      reject(new Error("Database upgrade took too long, aborting."));
    }, 10000);
    btCacheDBOpenRequest.onupgradeneeded = function (event) {
      const db = btCacheDBOpenRequest.result;
      for (const dataObjectType of dataObjectTypes) {
        if (!db.objectStoreNames.contains(dataObjectType)) {
          db.createObjectStore(dataObjectType); // create it
        }

        if (!db.objectStoreNames.contains(`${dataObjectType}_log`)) {
          db.createObjectStore(`${dataObjectType}_log`); // create it
        }
      }
      if (!db.objectStoreNames.contains("arrowAPI")) {
        db.createObjectStore("arrowAPI"); // create it
      }
      if (!db.objectStoreNames.contains("btGets")) {
        db.createObjectStore("btGets"); // create it
      }
      if (!db.objectStoreNames.contains("loggedOutData")) {
        db.createObjectStore("loggedOutData"); // create it
      }
      if (!db.objectStoreNames.contains("entityStorage")) {
        db.createObjectStore("entityStorage"); // create it
      }
      clearTimeout(upgraded);
    };

    btCacheDBOpenRequest.onerror = function () {
      clearTimeout(upgraded);
      reject(btCacheDBOpenRequest.error);
    };

    btCacheDBOpenRequest.onsuccess = function () {
      clearTimeout(upgraded);
      _btCacheDB = btCacheDBOpenRequest.result;
      _btCacheDBPromise = null;

      _btCacheDB.onversionchange = function () {
        _btCacheDB!.close();
        _btCacheDB = null;
        _btCacheDBPromise = null;
        window.location.reload();
        console.error("Database is outdated, please reload the page.");
      };

      resolve(_btCacheDB);
    };

    btCacheDBOpenRequest.onblocked = function () {
      console.error("Database is blocked");
      _btCacheDBPromise = null;
      window.location.reload();
    };

    return btCacheDBOpenRequest;
  });

  return _btCacheDBPromise;
}

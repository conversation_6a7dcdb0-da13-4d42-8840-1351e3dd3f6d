"use client";

import * as React from "react";
import * as TogglePrimitive from "@radix-ui/react-toggle";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "#/utils/classnames";
import { buttonVariants } from "./button";

const toggleVariants = cva(
  `inline-flex items-center justify-center rounded-md text-sm font-medium
  ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:ring-2
  focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-hidden disabled:pointer-events-none
  disabled:opacity-50 data-[state=on]:bg-accent-100 data-[state=on]:text-primary-800`,
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border border-border bg-transparent hover:bg-accent-100 hover:text-accent-500",
        button: buttonVariants({ size: "xs" }),
      },
      size: {
        default: "h-10 px-3",
        xs: "h-7 px-2 text-xs",
        sm: "h-8 px-2",
        lg: "h-11 px-5",
        dynamic: "px-2",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

const Toggle = React.forwardRef<
  React.ElementRef<typeof TogglePrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &
    VariantProps<typeof toggleVariants>
>(({ className, variant, size, ...props }, ref) => (
  <TogglePrimitive.Root
    ref={ref}
    className={cn(toggleVariants({ variant, size, className }))}
    {...props}
  />
));

Toggle.displayName = TogglePrimitive.Root.displayName;

export { Toggle, toggleVariants };

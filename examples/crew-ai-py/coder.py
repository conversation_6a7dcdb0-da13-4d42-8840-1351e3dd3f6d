from braintrust.otel import BraintrustSpanProcessor
from crewai import Agent, Crew, Task
from crewai.llm import LLM
from crewai.tools import tool
from opentelemetry import trace
from opentelemetry.instrumentation.crewai import CrewAIInstrumentor
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.sdk.trace import TracerProvider

# Get or create the tracer provider
current_provider = trace.get_tracer_provider()
if isinstance(current_provider, TracerProvider):
    provider = current_provider
else:
    provider = TracerProvider()
    trace.set_tracer_provider(provider)

# Add the Braintrust span processor
provider.add_span_processor(BraintrustSpanProcessor())

# Instrument the CrewAI and OpenAI libraries
CrewAIInstrumentor().instrument(tracer_provider=provider)
OpenAIInstrumentor().instrument(tracer_provider=provider)


# Example CrewAI tools to demonstrate tool usage with instrumentation
@tool("get_html_boilerplate")
def get_html_boilerplate(title: str, heading: str, body: str = "") -> str:
    """Return a minimal HTML page string with the given title, heading, and optional body text.

    Useful for quickly generating a starter HTML document before saving it to disk.
    """
    return (
        "<!doctype html>\n"
        '<html lang="en">\n'
        "<head>\n"
        f'  <meta charset="utf-8"/>\n'
        f'  <meta name="viewport" content="width=device-width, initial-scale=1"/>\n'
        f"  <title>{title}</title>\n"
        "</head>\n"
        "<body>\n"
        f"  <h1>{heading}</h1>\n"
        f"  <p>{body}</p>\n"
        "</body>\n"
        "</html>\n"
    )


@tool("preview_html")
def preview_html(html: str, max_chars: int = 160) -> str:
    """Return a short preview (first max_chars characters) of the HTML string."""
    if max_chars < 0:
        max_chars = 0
    return html[:max_chars]


# Create the CrewAI crew
llm = LLM(model="gpt-4o-mini")
coder = Agent(
    role="Software developer",
    goal="Write clear, concise code on demand",
    backstory="An expert coder with a keen eye for software trends.",
    verbose=True,
    llm=llm,
    tools=[get_html_boilerplate, preview_html],
)
task = Task(
    description=(
        "Create a minimal HTML page titled 'Braintrust + CrewAI' with the heading 'Hello World! "
        "Braintrust monitors your CrewAI agent!'.\n"
        "First call the get_html_boilerplate tool to generate the HTML string. Then call the "
        "preview_html tool to return the first 160 characters of the HTML."
    ),
    expected_output=("The 160-character preview returned by preview_html and the full HTML content used."),
    agent=coder,
)
crew = Crew(
    agents=[coder],
    tasks=[task],
    verbose=True,
)

# Run the CrewAI crew
result = crew.kickoff()

import { RateLimiterRedis, RateLimiterRes } from "rate-limiter-flexible";
import { getRedis, deleteMatching<PERSON>eys } from "./redis";
import { getLogger } from "./instrumentation/logger";
import { ERROR_CONTEXT_CACHE } from "./error_context_cache";
import { ErrorContext } from "@braintrust/local";
import { logCounter, otelTraced } from "./instrumentation/api";
import { SpanStatusCode } from "@opentelemetry/api";
import { TooManyRequestsError } from "./util";
import { parseBooleanEnv } from "./env";

const RATE_LIMIT_REDIS_PREFIX = "rate-limiter-flexible";

let cachedLogsOrgRateLimitConfig: LogsOrgRateLimitConfig | undefined =
  undefined;

interface LogsOrgRateLimitConfig {
  isValid: boolean;
  limitsByOrgID: Map<string, number>;
  windowSecs: number;
  enforce: boolean;
}

// getLogsOrgRateLimitConfig returns the global rate limit config
// set in environment variables.
function getLogsOrgRateLimitConfig(): LogsOrgRateLimitConfig {
  if (cachedLogsOrgRateLimitConfig !== undefined) {
    return cachedLogsOrgRateLimitConfig;
  }

  const logger = getLogger();

  const orgLimitsConfig = process.env.RATELIMIT_API_LOGS_ORG || "";
  const limitsByOrgID = new Map<string, number>();
  for (const pair of orgLimitsConfig.split(",")) {
    const split = pair.trim().split("=");
    if (split.length !== 2 || !split[0] || !split[1]) {
      logger.warn({ pair }, "Invalid org rate limit value");
      continue;
    }
    const [orgIdKey, limitStr] = split;
    const limit = parseInt(limitStr, 10);
    if (!isNaN(limit)) {
      limitsByOrgID.set(orgIdKey.trim(), limit);
    } else {
      logger.warn({ orgIdKey, limitStr }, "Invalid org rate limit value");
    }
  }

  let isValid = true;
  const windowStr = process.env.RATELIMIT_API_LOGS_ORG_WINDOW_SECS || "";
  const enforce = parseBooleanEnv(process.env.RATELIMIT_API_LOGS_ORG_ENFORCE);
  const window = parseInt(windowStr, 10);
  if (isNaN(window)) {
    isValid = false;
    logger.warn(
      { windowStr },
      "Invalid window value disables org rate limiting",
    );
  }

  cachedLogsOrgRateLimitConfig = {
    isValid,
    limitsByOrgID: limitsByOrgID,
    windowSecs: window,
    enforce: enforce ?? false,
  };

  return cachedLogsOrgRateLimitConfig;
}

export async function clearRateLimitStateForTests(): Promise<void> {
  await deleteMatchingKeys(`${RATE_LIMIT_REDIS_PREFIX}*`);
  cachedLogsOrgRateLimitConfig = undefined;
}

// checkLogsOrgRateLimit is used to check if an org has exceeded its rate limit for logging.
// This is meant purely as an emergency self-defense mechanism and we if we add org limits, we should
// actively work to remove them. We want orgs to send us lots of logs.
//
// to configure a rate limit, set the following environment variables:
// RATELIMIT_API_LOGS_ORG = <org_id>=<num_logs>, <org_id>=<num_logs>
// RATELIMIT_API_LOGS_ORG_WINDOW_SECS = <window_secs>
// RATELIMIT_API_LOGS_ORG_ENFORCE = <true|false>
//
// if RATELIMIT_API_LOGS_ORG_ENFORCE is true, then an error will be thrown if the rate limit is exceeded.
// if RATELIMIT_API_LOGS_ORG_ENFORCE is false, then a warning will be logged and the request will be allowed to proceed.
export async function checkLogsOrgRateLimit({
  appOrigin,
  authToken,
  orgId,
  numLogs,
}: {
  appOrigin: string;
  authToken: string | undefined;
  orgId: string;
  numLogs: number;
}): Promise<void> {
  const pino = getLogger();

  const cfg = getLogsOrgRateLimitConfig();

  pino.debug({ orgId, cfg }, "checkLogsOrgRateLimit");

  if (!cfg.isValid) {
    return;
  }

  const limit = cfg.limitsByOrgID.get(orgId);
  if (limit === undefined) {
    return;
  }

  pino.debug(
    {
      orgId,
      points: limit,
      window: cfg.windowSecs,
      enforce: cfg.enforce,
      numLogs,
    },
    "Rate limit check parameters",
  );

  await checkRateLimit({
    appOrigin,
    authToken,
    key: `API_LOG_ORG_${orgId}`,
    points: limit,
    duration: cfg.windowSecs,
    context: "checkLogsOrgRateLimit",
    enforce: cfg.enforce,
  });
}

export async function checkLambdaOperationsRateLimit(): Promise<void> {
  await checkRateLimit({
    appOrigin: "",
    authToken: undefined,
    key: "LAMBDA_OPERATIONS_GLOBAL",
    points: 600, // 600 operations per minute (10/second smoothed over 1 minute. The Lambda global limit is 15/second)
    duration: 60, // 1 minute window
    context: "lambda_operations",
    enforce: true,
  });
}

export async function checkRateLimit({
  appOrigin,
  authToken,
  key,
  points,
  duration,
  context,
  enforce,
}: {
  appOrigin: string;
  authToken: string | undefined;
  key: string;
  points: number;
  duration: number;
  context: string;
  enforce?: boolean;
}) {
  const redis = await getRedis();
  const limiter = new RateLimiterRedis({
    storeClient: redis,
    useRedisPackage: true,
    points,
    duration,
    keyPrefix: RATE_LIMIT_REDIS_PREFIX,
  });
  const pino = getLogger();

  try {
    await limiter.consume(key);
  } catch (e) {
    if (e instanceof RateLimiterRes) {
      await otelTraced("rate limit", async (span) => {
        let errorContext: ErrorContext | undefined = undefined;
        try {
          errorContext = await ERROR_CONTEXT_CACHE.getErrorContext({
            authToken,
            appOrigin,
          });
        } catch (e) {
          pino.warn(
            { error: e, context },
            `Error getting error context while checking rate limit (${context}).`,
          );
        }
        const rateLimitContext = {
          msBeforeNext: e.msBeforeNext,
          remainingPoints: e.remainingPoints,
          consumedPoints: e.consumedPoints,
          isFirstInDuration: e.isFirstInDuration,
          totalPoints: points,
          duration,
        };

        for (const [key, value] of Object.entries(rateLimitContext)) {
          span.setAttribute(`rate_limit.${key}`, value);
        }

        for (const [key, value] of Object.entries(errorContext ?? {})) {
          if (value) {
            span.setAttribute(`error_context.${key}`, value);
          }
        }

        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: "Rate limit exceeded",
        });

        const logAtLevel = enforce
          ? pino.info.bind(pino)
          : pino.warn.bind(pino);

        logCounter({
          name: `api.rate_limit.${context}.num_exceeded`,
          value: 1,
          attributes: {
            ...(errorContext?.orgName
              ? { org_name: errorContext.orgName }
              : {}),
            enforce,
          },
        });

        logAtLevel(
          {
            rateLimitContext,
            ...(errorContext ? { errorContext } : {}),
            context,
          },
          `Rate limit exceeded (${context}).${enforce ? "" : " This will not return an error yet."} [${Object.entries(
            errorContext ?? {},
          )
            .map(([key, value]) => `${key}: ${value}`)
            .join(", ")}]`,
        );

        if (enforce) {
          throw new TooManyRequestsError(e.msBeforeNext / 1000);
        }
      });
    } else {
      pino.warn(
        { error: e, context },
        `Non rate-limit error while checking rate limit (${context}). Will return an internal server error.`,
      );
      throw e;
    }
  }
}

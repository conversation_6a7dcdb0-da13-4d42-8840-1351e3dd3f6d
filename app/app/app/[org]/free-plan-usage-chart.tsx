"use client";

import { type getCurrentUsage } from "#/utils/billing/utils";
import { cn } from "#/utils/classnames";
import { useQueryFunc } from "#/utils/react-query";
import Link from "next/link";
import { ArrowUpRight, Info } from "lucide-react";
import { buttonVariants } from "#/ui/button";
import { getOrgSettingsLink } from "./getOrgLink";
import { useOrg } from "#/utils/user";
import { BasicTooltip } from "#/ui/tooltip";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { getPageName } from "#/utils/analytics";

export function FreePlanUsageChart({ orgId }: { orgId: string }) {
  const org = useOrg();
  const { track } = useAppAnalytics();
  const {
    data: usageData,
    isLoading,
    error,
  } = useQueryFunc<typeof getCurrentUsage>({
    fName: "getCurrentUsage",
    args: { orgId },
  });

  if (error || !usageData || isLoading) {
    return null;
  }
  const { logs, scores, customerPortalUrl } = usageData;
  const isUsageExceeded =
    (logs?.percentageUsed ?? 0) >= 100 || (scores?.percentageUsed ?? 0) >= 100;
  const billingLink = `${getOrgSettingsLink({ orgName: org.name })}/billing`;

  return (
    <div className="flex-none px-2">
      <div className="flex flex-col gap-2 rounded-md border border-primary-200 bg-primary-200/50 p-2">
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-1">
            Free plan usage
            <BasicTooltip tooltipContent="Usage data delayed by several minutes">
              <Info className="size-2.5 cursor-pointer text-primary-400 opacity-0 transition-opacity group-hover/sidenav:opacity-100" />
            </BasicTooltip>
          </div>
          {customerPortalUrl && (
            <Link
              target="_blank"
              href={customerPortalUrl}
              className={cn(
                buttonVariants({
                  size: "icon",
                  variant: "ghost",
                }),
                "size-4 text-primary-400",
              )}
              onClick={() => {
                track("viewUsageClick", {
                  orgName: org.name,
                  orgId: org.id ?? "",
                  entryPoint: "sidebar",
                  destinationUrl: customerPortalUrl,
                  sourcePage:
                    typeof window !== "undefined"
                      ? getPageName(window.location.pathname)
                      : "",
                });
              }}
            >
              <ArrowUpRight className="size-3" />
            </Link>
          )}
        </div>
        {logs && (
          <Metric
            used={logs.currentUsage}
            percentageUsed={logs.percentageUsed}
            label="Logs"
            limit={logs.limit}
            units="GB"
          />
        )}

        {scores && (
          <Metric
            used={scores.currentUsage}
            percentageUsed={scores.percentageUsed}
            label="Scores/metrics"
            limit={scores.limit}
          />
        )}

        {isUsageExceeded && (
          <div className="text-xs text-primary-500">
            Your usage has exceeded the free plan limit.{" "}
            <Link
              href={billingLink}
              className="font-medium text-accent-700"
              onClick={() => {
                track("upgradeClick", {
                  orgName: org.name,
                  orgId: org.id ?? "",
                  entryPoint: "usageWarning",
                  destination: "billingPage",
                  destinationUrl: billingLink,
                  context: "usage_exceeded",
                  sourcePage:
                    typeof window !== "undefined"
                      ? getPageName(window.location.pathname)
                      : "",
                });
              }}
            >
              Upgrade
            </Link>{" "}
            to continue using Braintrust.
          </div>
        )}
      </div>
    </div>
  );
}

const Metric = ({
  used,
  percentageUsed,
  label,
  className,
  limit,
  units,
}: {
  used: number;
  percentageUsed: number;
  label: string;
  className?: string;
  limit: number;
  units?: string;
}) => {
  return (
    <div
      className={cn(
        "flex items-center gap-2 text-xs text-primary-600",
        className,
      )}
    >
      <span>{label}</span>
      <span className="flex-1 text-right text-[10px] text-primary-500">
        {used.toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 4,
        })}{" "}
        of{" "}
        {limit.toLocaleString(undefined, {
          minimumFractionDigits: 0,
          maximumFractionDigits: 4,
        })}
        {units ? ` ${units}` : ""}
      </span>
      <div className="h-1 w-6 rounded-full bg-primary-300">
        <div
          className={cn("h-1 rounded-full transition-all", {
            "bg-bad-500": percentageUsed > 90,
            "bg-amber-500": percentageUsed > 75 && percentageUsed <= 90,
            "bg-good-500": percentageUsed <= 75,
          })}
          style={{
            width: `${Math.min(percentageUsed, 100)}%`,
          }}
        />
      </div>
    </div>
  );
};

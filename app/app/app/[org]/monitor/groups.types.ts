export type ScoresRecord = Record<
  string,
  { avg: number | null; last_updated: number; count: bigint }
>;

export type ToolRecord = Record<
  string,
  {
    count: bigint;
    error_rate: number;
    p50_duration: number | null;
  }
>;
export type ToolMetric = NonNullable<keyof ToolRecord[string]>;

export type MetricsData = {
  time: number;
  count: bigint;
  group?: string | null;
  scores?: ScoresRecord;
  tools?: ToolRecord;
  [key: string]:
    | number
    | bigint
    | string
    | null
    | undefined
    | ScoresRecord
    | ToolRecord;
};

export type TimeGroups = Record<
  string,
  { groups: Record<string, MetricsData> }
>;
export type ColorMap = Record<
  string,
  Record<"metadata" | "none" | "score" | "metric", number>
>;

export interface GroupBy {
  key: string;
  value: string;
}

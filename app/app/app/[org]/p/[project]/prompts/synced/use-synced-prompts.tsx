import {
  type PromptData,
  type UIFunctionData,
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import {
  type ArraySyncedState,
  type ArraySyncedStateProps,
  createSyncedObjects,
} from "#/utils/synced-state";
import { type TransactionId } from "braintrust/util";
import {
  type MessageRole,
  type ModelParams,
  type SavedFunctionId,
  type ContentPart,
  type ToolCall,
  type OpenAIModelParams,
  type FunctionType,
  type Message,
} from "@braintrust/typespecs";
import { TransactionIdField } from "@braintrust/local/query";
import { type ModelFormat, type ModelSpec } from "@braintrust/proxy/schema";
import { newId } from "braintrust";
import { type Draft } from "immer";
import { LexoRank } from "lexorank";
import { useContext, useMemo, createContext, useCallback, useRef } from "react";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { ProjectContext } from "../../projectContext";
import { useAtomValue } from "jotai";
import { type structuredOutputSchema } from "../structured-output";
import { type z } from "zod";
import { atom } from "jotai";
import {
  type AgentFlowNode,
  type EdgeData,
  makeGraphEdges,
  makeGraphNodes,
} from "../agent-canvas-utils";
import { type Edge } from "@xyflow/react";
import {
  convertMessages,
  createPromptMessage,
  setMessageRole,
  translatePromptModelParams,
} from "#/ui/prompts/prompt-utils";
import {
  PLACEHOLDER_PY,
  PLACEHOLDER_TS,
} from "#/ui/prompts/function-editor/code-editor-placeholders";

const { useSyncedObjects, SyncedObjectsProvider } =
  createSyncedObjects<SyncedPlaygroundBlock>();

const AvailableModelsContext = createContext<Record<string, ModelSpec>>({});

export type SyncedPromptsProviderProps = {
  allAvailableModels: Record<string, ModelSpec>;
  promptSessionId?: string;
} & ArraySyncedStateProps<SyncedPlaygroundBlock>;

function SyncedPromptsProvider({
  externalValue,
  save,
  getRowKey,
  children,
  allAvailableModels,
  promptSessionId,
}: SyncedPromptsProviderProps) {
  return (
    <SyncedObjectsProvider
      externalValue={externalValue}
      save={save}
      getRowKey={getRowKey}
    >
      <AvailableModelsContext.Provider value={allAvailableModels}>
        <SyncedPromptsProviderInner promptSessionId={promptSessionId}>
          {children}
        </SyncedPromptsProviderInner>
      </AvailableModelsContext.Provider>
    </SyncedObjectsProvider>
  );
}

export type AppendEditorArgs = {
  origin?: PromptData["origin"];
  functionData?: UIFunctionData;
  promptData?: PromptData;
  functionType?: FunctionType;
};

/**
 * _NO_SAVE functions only update Jotai state but don't persist to the backend
 *   - they're useful when we want to update the UI immediately and handle persistence separately
 *   - e.g. for text editors with debounced persistence
 *
 * _ROOT functions only operate on the root jotai state, which is the an array of top-level task block in the playground.
 *
 * all other functions update jotai state, then persist the new value. they either operate on the top-level task block,
 * or whatever nested prompt data the `scoper` from the nearest `SyncedPromptsScoperProvider` produces.
 */
type SyncedPromptsContextType = {
  saveSyncedPrompt: ArraySyncedState<SyncedPlaygroundBlock>["save"];
  sortedSyncedPromptsAtom_ROOT: ArraySyncedState<SyncedPlaygroundBlock>["atom"];
  setSyncedPrompts_ROOT: ArraySyncedState<SyncedPlaygroundBlock>["setValue"];
  setAgentNodes_NO_SAVE: (args: { id: string; nodes: AgentFlowNode[] }) => void;
  setAgentEdges_NO_SAVE: (args: {
    id: string;
    edges: Edge<EdgeData>[];
  }) => void;
  resetFunction_ROOT: (args: {
    id: string;
    prompt: UIFunction;
    includeOrigin?: boolean;
  }) => Promise<TransactionId | null>;
  resetPrompt: (args: {
    id: string;
    prompt: UIFunction;
    includeOrigin?: boolean;
  }) => Promise<TransactionId | null>;
  duplicateEditor_ROOT: (args: { id: string }) => Promise<TransactionId | null>;
  deleteEditor_ROOT_NO_SAVE: (args: { id: string }) => void;
  appendNewEditor_ROOT: (
    args: AppendEditorArgs,
  ) => Promise<TransactionId | null>;
  appendMessages: (args: {
    id: string;
    messages: Message[];
  }) => Promise<TransactionId | null>;
  addMessage: (args: {
    id: string;
    afterIndex?: number;
    message?: Message;
  }) => Promise<TransactionId | null>;
  removeMessage: (args: {
    id: string;
    index: number;
  }) => Promise<TransactionId | null>;
  updateMessageRole: (args: {
    id: string;
    index: number;
    newRole: MessageRole;
    newIsMultimodal: boolean;
  }) => Promise<TransactionId | null>;
  addMessagePart: (args: {
    id: string;
    index: number;
    type: ContentPart["type"];
  }) => Promise<TransactionId | null>;
  removeMessagePart: (args: {
    id: string;
    index: number;
    partIndex: number;
  }) => Promise<TransactionId | null>;
  updateMessagePart_NO_SAVE: (args: {
    id: string;
    index: number;
    partIndex: number;
    value: string;
  }) => void;
  updateMessageToolId_NO_SAVE: (args: {
    id: string;
    index: number;
    toolId: string;
  }) => void;
  updateMessageToolCalls_NO_SAVE: (args: {
    id: string;
    index: number;
    toolCalls?: ToolCall[];
  }) => void;
  toggleMessageToolCalls: (args: {
    id: string;
    index: number;
  }) => Promise<TransactionId | null>;
  updateModel: (args: {
    id: string;
    model: string;
  }) => Promise<TransactionId | null>;
  updateModelParams: (args: {
    id: string;
    params: ModelParams | ((params: Draft<ModelParams>) => void);
  }) => Promise<TransactionId | null>;
  updateStructuredOutput: (args: {
    id: string;
    data: z.infer<typeof structuredOutputSchema>;
  }) => Promise<TransactionId | null>;
  updateResponseType: (args: {
    id: string;
    type: "json_schema" | "json_object" | "text";
  }) => Promise<TransactionId | null>;
  updateTools: (args: {
    id: string;
    toolFunctions: SavedFunctionId[];
    rawTools: Record<string, unknown>[] | null;
    toolChoice: OpenAIModelParams["tool_choice"];
  }) => Promise<TransactionId | null>;
  updateParser: (args: {
    id: string;
    parser: PromptData["parser"];
  }) => Promise<TransactionId | null>;
  updateOrigin: (args: {
    id: string;
    origin: PromptData["origin"];
  }) => Promise<TransactionId | null>;
  updateRemoteEvalParameter_NO_SAVE__ROOT: (args: {
    id: string;
    name: string;
    value: unknown;
  }) => void;
  updateCode_NO_SAVE__ROOT: (args: { id: string; code: string }) => void;
  updateScorerTab__ROOT: (args: {
    id: string;
    tab: "llm" | "ts" | "py";
    savedContent?: {
      function_data: UIFunctionData;
      prompt_data?: PromptData;
    };
  }) => Promise<TransactionId | null>;
  focusedEditor: React.RefObject<{
    promptId: string;
    messageIndex: number;
    partIndex: number;
    focusKey?: string;
  } | null>;
};
const SyncedPromptsContext = createContext<SyncedPromptsContextType | null>(
  null,
);

const ScoperContext = createContext<{
  scoper: (prompt: SyncedPlaygroundBlock) => PromptData | undefined;
  persistenceId: string;
} | null>(null);

/**
 * A provider that enables scoping the prompt data vended to and updated by useSyncedPrompts functions.
 * This is useful for nested prompts in agents, where we want to update a nested prompt in jotai, but persist the parent task as a whole.
 */
export function SyncedPromptsScoperProvider({
  children,
  scoper,
  persistenceId,
}: {
  children: React.ReactNode;
  /**
   * A function that that traverses a SyncedPlayground block and returns the prompt data of the nested prompt that should be updated.
   */
  scoper: (task: SyncedPlaygroundBlock) => PromptData | undefined;
  /**
   * The id of the task (agent or prompt) that should be persisted by nested invocations of useSyncedPrompts functions.
   * For example, when the model of a prompt node in an agent is updated, the entire agent should be persisted.
   */
  persistenceId: string;
}) {
  return (
    <ScoperContext.Provider
      value={useMemo(
        () => ({ scoper, persistenceId }),
        [scoper, persistenceId],
      )}
    >
      <SyncedPromptsProviderInner>{children}</SyncedPromptsProviderInner>
    </ScoperContext.Provider>
  );
}

function SyncedPromptsProviderInner({
  children,
  promptSessionId,
}: {
  children: React.ReactNode;
  promptSessionId?: string;
}) {
  const {
    setValueAtId: setSyncedPromptFromContext,
    setValue: setSyncedPrompts,
    save: saveSyncedPromptFromContext,
    atom: syncedPromptsAtom,
  } = useSyncedObjects();
  const { scoper, persistenceId } = useContext(ScoperContext) ?? {};
  const allAvailableModels = useContext(AvailableModelsContext);
  const appAnalytics = useAppAnalytics();
  const { projectId } = useContext(ProjectContext);
  const currentTasks = useAtomValue(syncedPromptsAtom);
  // This can be a ref because we don't care about triggering re-renders when it changes
  // - we only want to read the value to auto-focus when editors are added/removed.
  const focusedEditor = useRef<{
    promptId: string;
    messageIndex: number;
    partIndex: number;
    focusKey?: string;
  } | null>(null);

  const setSyncedPromptData = useCallback(
    (id: string, updater: (promptData: Draft<PromptData>) => void) => {
      if (!scoper) {
        return setSyncedPromptFromContext(id, (prompt) =>
          updater(prompt.prompt_data),
        );
      }

      setSyncedPromptFromContext(persistenceId ?? id, (draft) => {
        const selectedPromptData = scoper(draft);
        if (!selectedPromptData) return;

        updater(selectedPromptData);
      });
    },
    [persistenceId, scoper, setSyncedPromptFromContext],
  );

  /**
   * If a scoper is provided, we save the prompt at the scoper's persistenceId.
   * Otherwise, we save the prompt with the provided id. Concretely, this is used in the agent canvas,
   * where we want changes to some property of a nested prompt to update that nested prompt, but to persist the
   * agent as a whole.
   */
  const saveSyncedPrompt = useCallback(
    (id: string) => {
      return saveSyncedPromptFromContext(persistenceId ?? id);
    },
    [persistenceId, saveSyncedPromptFromContext],
  );
  const sortedSyncedPromptsAtom_ROOT = useMemo(
    () =>
      atom((get) =>
        get(syncedPromptsAtom).toSorted((a, b) =>
          (getPosition(a) ?? "").localeCompare(getPosition(b) ?? ""),
        ),
      ),
    [syncedPromptsAtom],
  );

  const setAgentEdges_NO_SAVE: SyncedPromptsContextType["setAgentEdges_NO_SAVE"] =
    useCallback(
      ({ id, edges }) => {
        setSyncedPromptFromContext(id, (prompt) => {
          if (prompt.function_data.type === "graph") {
            prompt.function_data.edges = makeGraphEdges(edges);
          }
        });
      },
      [setSyncedPromptFromContext],
    );

  const setAgentNodes_NO_SAVE: SyncedPromptsContextType["setAgentNodes_NO_SAVE"] =
    useCallback(
      ({ id, nodes }) => {
        setSyncedPromptFromContext(id, (prompt) => {
          if (prompt.function_data.type === "graph") {
            prompt.function_data.nodes = makeGraphNodes(nodes);
          }
        });
      },
      [setSyncedPromptFromContext],
    );

  const resetFunction_ROOT: SyncedPromptsContextType["resetFunction_ROOT"] =
    useCallback(
      async ({ id, prompt, includeOrigin = true }) => {
        setSyncedPromptFromContext(id, (promptDraft) => {
          promptDraft.function_type = prompt.function_type;

          if (prompt.function_data) {
            promptDraft.function_data = prompt.function_data;
            promptDraft.prompt_data ??= {};
          }

          if (prompt.prompt_data) {
            promptDraft.prompt_data = prompt.prompt_data;
          }

          if (includeOrigin) {
            promptDraft.prompt_data.origin = {
              project_id: prompt.project_id,
              prompt_id: prompt.id,
              prompt_version: prompt[TransactionIdField],
            };
          }

          const position = promptDraft.prompt_data?.options?.position;
          if (position) {
            promptDraft.prompt_data.options ??= {};
            promptDraft.prompt_data.options.position = position;
          }
        });
        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptFromContext],
    );

  const resetPrompt: SyncedPromptsContextType["resetPrompt"] = useCallback(
    async ({ id, prompt, includeOrigin = true }) => {
      setSyncedPromptData(id, (promptData) => {
        if (prompt.prompt_data) {
          promptData.prompt = prompt.prompt_data.prompt;
          promptData.options = {
            ...prompt.prompt_data.options,
            position: promptData.options?.position,
          };
          if (prompt.prompt_data.tool_functions) {
            promptData.tool_functions = prompt.prompt_data.tool_functions;
          }

          if (prompt.prompt_data.origin) {
            promptData.origin = prompt.prompt_data.origin;
          }

          if (includeOrigin) {
            promptData.origin = {
              project_id: prompt.project_id,
              prompt_id: prompt.id,
              prompt_version: prompt[TransactionIdField],
            };
          }
        }
      });
      return saveSyncedPrompt(id);
    },
    [saveSyncedPrompt, setSyncedPromptData],
  );

  const duplicateEditor_ROOT: SyncedPromptsContextType["duplicateEditor_ROOT"] =
    useCallback(
      async ({ id }) => {
        const newPromptId = newId();
        setSyncedPrompts((draft) => {
          const idx = draft.findIndex((p) => p.id === id);
          if (idx === -1) return draft;

          const { prompt_data, function_data, function_type } = draft[idx];
          const { options, ...rest } = prompt_data;
          const newPrompt = {
            id: newPromptId,
            [TransactionIdField]: "0",
            function_data,
            prompt_data: {
              ...rest,
              // do not allow saved prompts to be included twice in the same playground
              // https://braintrustdata.slack.com/archives/C085BEBQ0N9/p1747833115730089
              origin: null,
              options: {
                ...options,
                position: getLastPosition(draft),
              },
            },
            function_type,
          };
          draft.push(newPrompt);

          focusedEditor.current = {
            promptId: newPromptId,
            messageIndex: 0,
            partIndex: 0,
          };
        });
        return saveSyncedPrompt(newPromptId);
      },
      [saveSyncedPrompt, setSyncedPrompts],
    );

  const deleteEditor_ROOT_NO_SAVE: SyncedPromptsContextType["deleteEditor_ROOT_NO_SAVE"] =
    useCallback(
      ({ id }) => {
        setSyncedPrompts((draft) => {
          const idx = draft.findIndex((p) => p.id === id);
          if (idx === -1) return draft;

          draft.splice(idx, 1);
        });
      },
      [setSyncedPrompts],
    );

  const appendNewEditor_ROOT: SyncedPromptsContextType["appendNewEditor_ROOT"] =
    useCallback(
      ({
        origin,
        functionData = { type: "prompt" },
        promptData = {},
        functionType,
      }) => {
        const newPromptId = newId();
        setSyncedPrompts((draft) => {
          const { origin: _origin, options, ...rest } = promptData;
          const overriddenOrigin = origin ?? _origin;
          const newPrompt = {
            id: newPromptId,
            [TransactionIdField]: "0",
            function_data: functionData,
            prompt_data: {
              ...rest,
              options: {
                ...options,
                position: getLastPosition(draft),
              },
              ...(overriddenOrigin ? { origin: overriddenOrigin } : {}),
            },
            function_type: functionType,
          };
          draft.push(newPrompt);

          focusedEditor.current = {
            promptId: newPromptId,
            messageIndex: 0,
            partIndex: 0,
          };
        });
        return saveSyncedPrompt(newPromptId);
      },
      [saveSyncedPrompt, setSyncedPrompts],
    );

  const addMessage: SyncedPromptsContextType["addMessage"] = useCallback(
    async ({ id, afterIndex, message }) => {
      // Calculate insertion index - will be recalculated inside the callback for accuracy
      const insertionIndex = afterIndex != undefined ? afterIndex + 1 : 0;

      setSyncedPromptData(id, (promptData) => {
        if (promptData.prompt?.type === "completion") return;

        let newMessage = message;
        if (newMessage == null) {
          let role: MessageRole;
          const hasSystemMessage = promptData.prompt?.messages.some(
            (msg) => msg.role === "system",
          );

          if (!hasSystemMessage && insertionIndex === 0) {
            // First message and no system message exists - use system
            role = "system";
          } else {
            // After system message (or if no system), alternate between user and assistant
            const lastNonSystemIndex =
              promptData.prompt?.messages
                .slice(0, insertionIndex)
                .findLastIndex((msg) => msg.role !== "system") ?? -1;

            if (lastNonSystemIndex === -1) {
              role = "user";
            } else {
              const lastNonSystemRole =
                promptData.prompt?.messages[lastNonSystemIndex]?.role;
              // Alternate between user and assistant
              role = lastNonSystemRole === "user" ? "assistant" : "user";
            }
          }

          newMessage = createPromptMessage(role);
        }

        // Calculate accurate values from the current state inside the callback
        const messageLengths =
          promptData.prompt?.type === "chat"
            ? (promptData.prompt?.messages?.length ?? 0)
            : 0;
        const actualInsertionIndex =
          afterIndex != undefined ? afterIndex + 1 : messageLengths;

        // Add the message to the state
        promptData.prompt?.messages.splice(actualInsertionIndex, 0, newMessage);

        // Track analytics here with accurate data after successful state modification
        try {
          appAnalytics.track("playgroundEdit", {
            playgroundId: promptSessionId || "",
            projectId: projectId || "",
            triggerEntity: "human",
            entryPoint: "promptEditor",
            editType: "messageAdded",
            source: "web",
            details: {
              taskId: id,
              messageIndex: actualInsertionIndex,
              messageRole: newMessage?.role || "auto-determined",
            },
          });
        } catch (error) {
          console.error("Failed to track message add analytics:", error);
        }

        focusedEditor.current = {
          promptId: id,
          messageIndex: actualInsertionIndex,
          partIndex: 0,
          // this is a hack to ensure that the correct message is focused by remounting the editor at that index
          focusKey: newId(),
        };
      });
      return saveSyncedPrompt(id);
    },
    [
      saveSyncedPrompt,
      setSyncedPromptData,
      appAnalytics,
      promptSessionId,
      projectId,
    ],
  );

  const appendMessages: SyncedPromptsContextType["appendMessages"] =
    useCallback(
      async ({ id, messages }) => {
        setSyncedPromptData(id, (promptData) => {
          if (promptData.prompt?.type === "completion") return;
          promptData.prompt?.messages.push(...messages);
        });
        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptData],
    );

  const removeMessage: SyncedPromptsContextType["removeMessage"] = useCallback(
    async ({ id, index }) => {
      // Get task info for analytics before deletion
      const task = currentTasks.find((p) => p.id === id);
      const messageToDelete =
        task?.prompt_data?.prompt?.type === "chat"
          ? task?.prompt_data?.prompt?.messages?.[index]
          : undefined;

      setSyncedPromptData(id, (promptData) => {
        if (promptData.prompt?.type === "completion") return;
        promptData.prompt?.messages.splice(index, 1);

        const messageLengths = promptData.prompt?.messages.length ?? 1;
        focusedEditor.current = {
          promptId: id,
          messageIndex: Math.min(index, messageLengths - 1),
          partIndex: 0,
          // this is a hack to ensure that the correct message is focused by remounting the editor at that index
          focusKey: newId(),
        };
      });

      // Track message deletion analytics
      try {
        appAnalytics.track("playgroundEdit", {
          playgroundId: promptSessionId || "",
          projectId: projectId || "",
          triggerEntity: "human",
          entryPoint: "other",
          editType: "messageDeleted",
          source: "web",
          details: {
            taskId: id,
            messageIndex: index,
            messageRole: messageToDelete?.role || "unknown",
          },
        });
      } catch (error) {
        console.error("Failed to track message delete analytics:", error);
      }

      return saveSyncedPrompt(id);
    },
    [
      saveSyncedPrompt,
      setSyncedPromptData,
      currentTasks,
      appAnalytics,
      promptSessionId,
      projectId,
    ],
  );

  const updateMessageRole: SyncedPromptsContextType["updateMessageRole"] =
    useCallback(
      async ({ id, index, newRole, newIsMultimodal }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type === "chat" &&
            promptData.prompt?.messages[index]
          ) {
            setMessageRole({
              message: promptData.prompt?.messages[index],
              newRole,
              newIsMultimodal,
            });
          }
        });
        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptData],
    );

  const updateMessageToolId_NO_SAVE: SyncedPromptsContextType["updateMessageToolId_NO_SAVE"] =
    useCallback(
      ({ id, index, toolId }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type === "chat" &&
            promptData.prompt?.messages[index]?.role === "tool"
          ) {
            promptData.prompt.messages[index].tool_call_id = toolId;
          }
        });
      },
      [setSyncedPromptData],
    );

  const updateMessageToolCalls_NO_SAVE: SyncedPromptsContextType["updateMessageToolCalls_NO_SAVE"] =
    useCallback(
      ({ id, index, toolCalls }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type === "chat" &&
            promptData.prompt?.messages[index]?.role === "assistant"
          ) {
            promptData.prompt.messages[index].tool_calls = toolCalls;
          }
        });
      },
      [setSyncedPromptData],
    );

  const toggleMessageToolCalls: SyncedPromptsContextType["toggleMessageToolCalls"] =
    useCallback(
      async ({ id, index }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type === "chat" &&
            promptData.prompt?.messages[index]?.role === "assistant"
          ) {
            promptData.prompt.messages[index].tool_calls = !!promptData.prompt
              .messages[index].tool_calls
              ? undefined
              : [];
          }
        });
        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptData],
    );

  const addMessagePart: SyncedPromptsContextType["addMessagePart"] =
    useCallback(
      async ({ id, index, type }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type !== "chat" ||
            !promptData.prompt?.messages[index]
          ) {
            return;
          }

          const content = promptData.prompt?.messages[index].content;
          const newPart: ContentPart =
            type === "image_url"
              ? { type: "image_url", image_url: { url: "" } }
              : { type: "text", text: "" };

          if (Array.isArray(content)) {
            // This is implicitly assuming that the set of content parts does not change with this mapping.
            // Typescript has a hard time recognizing this, but we know it to be true...
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            (content as ContentPart[]).push(newPart);
          } else {
            promptData.prompt.messages[index].content = [
              { type: "text", text: content ?? "" },
              newPart,
            ];
          }

          focusedEditor.current = {
            promptId: id,
            messageIndex: index,
            partIndex:
              (promptData.prompt.messages[index].content?.length ?? 1) - 1,
          };
        });
        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptData],
    );

  const removeMessagePart: SyncedPromptsContextType["removeMessagePart"] =
    useCallback(
      async ({ id, index, partIndex }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type !== "chat" ||
            !promptData.prompt?.messages[index] ||
            !Array.isArray(promptData.prompt?.messages[index].content)
          ) {
            return;
          }

          promptData.prompt.messages[index].content.splice(partIndex, 1);

          const partLengths =
            promptData.prompt?.messages?.[index].content?.length ?? 0;
          focusedEditor.current = {
            promptId: id,
            messageIndex: index,
            partIndex: Math.min(partIndex, partLengths - 1),
          };
        });

        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptData],
    );

  const updateMessagePart_NO_SAVE: SyncedPromptsContextType["updateMessagePart_NO_SAVE"] =
    useCallback(
      ({ id, index, partIndex, value }) => {
        setSyncedPromptData(id, (promptData) => {
          if (
            promptData.prompt?.type !== "chat" ||
            !promptData.prompt?.messages[index]
          ) {
            return;
          }

          const content = promptData.prompt.messages[index].content;
          if (Array.isArray(content)) {
            const part = content[partIndex];
            if (part.type === "text") {
              part.text = value;
            } else if (part.type === "image_url") {
              part.image_url.url = value;
            }
          } else {
            promptData.prompt.messages[index].content = value;
          }
        });
      },
      [setSyncedPromptData],
    );

  const updateModel: SyncedPromptsContextType["updateModel"] = useCallback(
    async ({ id, model }) => {
      setSyncedPromptData(id, (promptData) => {
        const currentModel = promptData.options?.model;
        promptData.options ??= {};
        promptData.options.model = model;
        promptData.options.params = translatePromptModelParams({
          currentModel,
          newModel: model,
          allAvailableModels,
          params: promptData.options?.params,
        });
        promptData.prompt = convertMessages({
          prompt: promptData.prompt ?? undefined,
          newModel: model,
          allAvailableModels,
        });
      });

      return saveSyncedPrompt(id);
    },
    [allAvailableModels, saveSyncedPrompt, setSyncedPromptData],
  );

  /** some prompt params are shared across all prompts, so we update them in the root jotai state */
  const updateModelParams: SyncedPromptsContextType["updateModelParams"] =
    useCallback(
      async ({ id, params }) => {
        setSyncedPromptData(id, (promptData) => {
          promptData.options ??= {};
          promptData.options.params ??= {};

          if (typeof params === "function") {
            params(promptData.options.params);
            return;
          }

          promptData.options.params = params;
        });
        return saveSyncedPrompt(id);
      },
      [saveSyncedPrompt, setSyncedPromptData],
    );

  const updateStructuredOutput: SyncedPromptsContextType["updateStructuredOutput"] =
    useCallback(
      async ({ id, data }) => {
        return updateModelParams({
          id,
          params: (params) => {
            params.response_format = {
              type: "json_schema",
              json_schema: data,
            };
          },
        });
      },
      [updateModelParams],
    );

  const updateResponseType: SyncedPromptsContextType["updateResponseType"] =
    useCallback(
      async ({ id, type }) => {
        return updateModelParams({
          id,
          params: (params) => {
            params.response_format = { type };
          },
        });
      },
      [updateModelParams],
    );

  const updateTools: SyncedPromptsContextType["updateTools"] = useCallback(
    async ({ id, toolFunctions, rawTools, toolChoice }) => {
      setSyncedPromptData(id, (promptData) => {
        promptData.tool_functions = toolFunctions;
        if (promptData.prompt?.type === "chat") {
          promptData.prompt.tools = rawTools ? JSON.stringify(rawTools) : "";
        }
        promptData.options ??= {};
        promptData.options.params ??= {};
        promptData.options.params.tool_choice = toolChoice;
      });
      return saveSyncedPrompt(id);
    },
    [saveSyncedPrompt, setSyncedPromptData],
  );

  const updateParser: SyncedPromptsContextType["updateParser"] = useCallback(
    async ({ id, parser }) => {
      setSyncedPromptData(id, (promptData) => {
        promptData.parser = parser;
      });
      return saveSyncedPrompt(id);
    },
    [saveSyncedPrompt, setSyncedPromptData],
  );

  const updateOrigin: SyncedPromptsContextType["updateOrigin"] = useCallback(
    async ({ id, origin }) => {
      setSyncedPromptData(id, (promptData) => {
        promptData.origin = origin;
      });
      return saveSyncedPrompt(id);
    },
    [saveSyncedPrompt, setSyncedPromptData],
  );

  const updateRemoteEvalParameter_NO_SAVE__ROOT: SyncedPromptsContextType["updateRemoteEvalParameter_NO_SAVE__ROOT"] =
    useCallback(
      ({ id, name, value }) => {
        setSyncedPromptFromContext(id, (prompt) => {
          if (prompt.function_data.type !== "remote_eval") {
            return;
          }
          prompt.function_data.parameters[name] = value;
        });
      },
      [setSyncedPromptFromContext],
    );

  const updateCode_NO_SAVE__ROOT: SyncedPromptsContextType["updateCode_NO_SAVE__ROOT"] =
    useCallback(
      ({ id, code }) => {
        setSyncedPromptFromContext(id, (prompt) => {
          if (
            prompt.function_data.type !== "code" ||
            prompt.function_data.data.type !== "inline"
          ) {
            return;
          }
          prompt.function_data.data.code = code;
        });
      },
      [setSyncedPromptFromContext],
    );

  const updateScorerTab__ROOT: SyncedPromptsContextType["updateScorerTab__ROOT"] =
    useCallback(
      async ({ id, tab, savedContent }) => {
        setSyncedPromptFromContext(id, (prompt) => {
          if (savedContent) {
            prompt.function_data = savedContent.function_data;
            if (savedContent.prompt_data) {
              prompt.prompt_data = savedContent.prompt_data;
            }
            return;
          }

          if (tab === "llm") {
            const firstAvailableModel = Object.keys(allAvailableModels)[0];
            const params = translatePromptModelParams({
              newModel: firstAvailableModel,
              allAvailableModels,
            });
            const promptMessages = convertMessages({
              newModel: firstAvailableModel,
              allAvailableModels,
            });

            prompt.prompt_data = {
              prompt: promptMessages,
              options: {
                model: firstAvailableModel,
                params,
              },
            };
            prompt.function_data = { type: "prompt" };
          } else {
            const [runtime_context, code] =
              tab === "ts"
                ? [{ runtime: "node" as const, version: "22" }, PLACEHOLDER_TS]
                : [
                    { runtime: "python" as const, version: "3.12" },
                    PLACEHOLDER_PY,
                  ];

            prompt.function_data = {
              type: "code",
              data: { type: "inline", runtime_context, code },
            };
          }
        });
        return saveSyncedPrompt(id);
      },
      [allAvailableModels, saveSyncedPrompt, setSyncedPromptFromContext],
    );

  return (
    <SyncedPromptsContext.Provider
      value={useMemo(
        () => ({
          setSyncedPrompts_ROOT: setSyncedPrompts,
          saveSyncedPrompt,
          sortedSyncedPromptsAtom_ROOT,
          setAgentEdges_NO_SAVE,
          setAgentNodes_NO_SAVE,
          resetFunction_ROOT,
          resetPrompt,
          deleteEditor_ROOT_NO_SAVE,
          duplicateEditor_ROOT,
          appendNewEditor_ROOT,
          appendMessages,
          addMessage,
          removeMessage,
          updateMessageRole,
          addMessagePart,
          removeMessagePart,
          updateMessagePart_NO_SAVE,
          updateMessageToolId_NO_SAVE,
          updateMessageToolCalls_NO_SAVE,
          toggleMessageToolCalls,
          updateModel,
          updateModelParams,
          updateStructuredOutput,
          updateResponseType,
          updateTools,
          updateParser,
          updateOrigin,
          updateRemoteEvalParameter_NO_SAVE__ROOT,
          updateCode_NO_SAVE__ROOT,
          updateScorerTab__ROOT,
          focusedEditor,
        }),
        [
          setSyncedPrompts,
          saveSyncedPrompt,
          sortedSyncedPromptsAtom_ROOT,
          setAgentEdges_NO_SAVE,
          setAgentNodes_NO_SAVE,
          resetFunction_ROOT,
          resetPrompt,
          deleteEditor_ROOT_NO_SAVE,
          duplicateEditor_ROOT,
          appendNewEditor_ROOT,
          appendMessages,
          addMessage,
          removeMessage,
          updateMessageRole,
          addMessagePart,
          removeMessagePart,
          updateMessagePart_NO_SAVE,
          updateMessageToolId_NO_SAVE,
          updateMessageToolCalls_NO_SAVE,
          toggleMessageToolCalls,
          updateModel,
          updateModelParams,
          updateStructuredOutput,
          updateResponseType,
          updateTools,
          updateParser,
          updateOrigin,
          updateRemoteEvalParameter_NO_SAVE__ROOT,
          updateCode_NO_SAVE__ROOT,
          updateScorerTab__ROOT,
          focusedEditor,
        ],
      )}
    >
      {children}
    </SyncedPromptsContext.Provider>
  );
}

// @ts-ignore
const FALLBACK_CONTEXT: SyncedPromptsContextType = {};

/**
 * _NO_SAVE functions only update Jotai state but don't persist to the backend
 *   - they're useful when we want to update the UI immediately and handle persistence separately
 *   - e.g. for text editors with debounced persistence
 *
 * _ROOT functions only operate on the root jotai state, which is the an array of top-level task block in the playground.
 *
 * all other functions update jotai state, then persist the new value. they either operate on the top-level task block,
 * or whatever nested prompt data the `scoper` from the nearest `SyncedPromptsScoperProvider` produces.
 */
function useSyncedPrompts() {
  const context = useContext(SyncedPromptsContext);
  return context ?? FALLBACK_CONTEXT;
}

export { useSyncedPrompts, SyncedPromptsProvider };

type PromptBlockMessageRole = Exclude<MessageRole, "model" | "function">;

export const MESSAGE_ROLES: {
  [name in ModelFormat]: PromptBlockMessageRole[];
} = {
  openai: ["system", "user", "assistant", "tool"],
  anthropic: ["system", "user", "assistant", "tool"],
  google: ["system", "user", "assistant", "tool"],
  js: ["user"],
  window: ["user"],
  converse: ["system", "user", "assistant", "tool"],
};

export function getPosition(prompt?: SyncedPlaygroundBlock) {
  return prompt?.prompt_data.options?.position;
}

function getLastPosition(promptDraft: Draft<SyncedPlaygroundBlock[]>) {
  const lastPosition = getPosition(
    promptDraft.toSorted((a, b) =>
      (getPosition(a) ?? "").localeCompare(getPosition(b) ?? ""),
    )[promptDraft.length - 1],
  );
  if (!lastPosition) return LexoRank.middle().toString();
  return LexoRank.parse(lastPosition).genNext().toString();
}

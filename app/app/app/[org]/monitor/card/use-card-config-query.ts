import { useAvailableModels } from "#/ui/prompts/models";
import * as Query from "#/utils/btql/query-builder";
import { useOrg } from "#/utils/user";
import { useBtql, useIsRootBtqlSnippet } from "#/utils/btql/btql";
import { crunchCostsByTimeGroup } from "../costs-by-time-group";
import { type Expr, type ParsedQuery } from "@braintrust/btql/parser";
import { useEffect, useMemo, useState } from "react";
import { CreatedField } from "#/utils/duckdb";
import { getMonitorBtqlArgs } from "../use-monitor-query";
import { type ChartTimeFrame } from "../time-controls/time-range";
import { type MonitorCardConfig } from "./monitor-card-config.types";
import { getQueryTimeframe } from "./get-query-timeframe";
import { useFeatureFlags } from "#/lib/feature-flags";
import type { TIME_BUCKET } from "#/ui/charts/time-bucket";
import { CARD_GROUP_BY_PREFIX } from "../card-config/monitor-cards.constants";

interface UseCardConfigQueryProps {
  cardConfig: MonitorCardConfig;
  projectIds: string[];
  chartTimeFrame: ChartTimeFrame;
  timeBucket: TIME_BUCKET;
  groupBys?: string[];
  experimentIds: string[];
  from: "project_logs" | "experiment";
  tzUTC?: boolean;
  filters?: Expr[];
  allTimeQuery?: boolean;
}

export function useCardConfigQuery({
  cardConfig,
  projectIds,
  chartTimeFrame,
  timeBucket,
  groupBys,
  experimentIds,
  from,
  tzUTC,
  filters: filtersProp,
  allTimeQuery,
}: UseCardConfigQueryProps) {
  const { name: orgName } = useOrg();
  const { allAvailableModels } = useAvailableModels({ orgName });

  const isRootBtqlSnippet = useIsRootBtqlSnippet();
  const durationExpr = groupBys
    ? "metrics.end-metrics.start"
    : `${isRootBtqlSnippet} ? metrics.end-metrics.start : null`;

  const queryTimeframe = useMemo(() => {
    return getQueryTimeframe(chartTimeFrame, timeBucket);
  }, [chartTimeFrame, timeBucket]);

  const measures = useMemo(() => {
    return cardConfig.measures
      .map((m) => {
        let btql = m.btql;
        if (m.durationPercentile) {
          btql = `percentile(${durationExpr}, ${m.durationPercentile.toString()})`;
        }
        return {
          alias: m.alias,
          expr: { btql },
        };
      })
      .concat(
        cardConfig.toolMetric
          ? []
          : [
              {
                alias: "count",
                expr: { btql: `sum(${isRootBtqlSnippet})` },
              },
            ],
      );
  }, [cardConfig, durationExpr, isRootBtqlSnippet]);

  const {
    flags: { traceLevelMetrics },
  } = useFeatureFlags();

  const query = useMemo<ParsedQuery | null>(() => {
    const fromIds = from === "project_logs" ? projectIds : experimentIds;
    if (fromIds.length === 0) {
      return null;
    }

    const shape =
      from === "project_logs" &&
      filtersProp &&
      filtersProp.length > 0 &&
      traceLevelMetrics
        ? "traces"
        : "spans";

    const startTime = new Date(queryTimeframe.start).toISOString();
    const endTime = new Date(queryTimeframe.end).toISOString();

    // allTimeQuery disables timeframe filter
    const filters: Expr[] = allTimeQuery
      ? []
      : [
          {
            op: "gt",
            left: Query.ident(CreatedField),
            right: {
              op: "literal",
              value: startTime,
            },
          },
          {
            op: "lt",
            left: Query.ident(CreatedField),
            right: {
              op: "literal",
              value: endTime,
            },
          },
        ];

    if (cardConfig.toolMetric) {
      filters.push({
        op: "eq",
        left: Query.ident("span_attributes", "type"),
        right: {
          op: "literal",
          value: "tool",
        },
      });
    }

    if (filtersProp) {
      filters.push(...filtersProp);
    }

    cardConfig.chartFilters?.forEach((f) => {
      filters.push({
        btql: f.btql,
      });
    });

    const filter = Query.and(...filters);

    const pivot = cardConfig.pivot
      ? [{ alias: cardConfig.idName, expr: { btql: cardConfig.pivot } }]
      : undefined;
    const unpivot = cardConfig.unpivot
      ? [
          {
            expr: { btql: cardConfig.idName },
            alias: cardConfig.unpivot,
          },
        ]
      : undefined;

    return {
      from: Query.from(from, fromIds, shape),
      dimensions: [
        {
          alias: "time",
          expr: {
            op: "function",
            name: Query.ident(timeBucket),
            args: [Query.ident(CreatedField)],
          },
        },
        ...(cardConfig.additionalGroupBy
          ? [
              {
                alias: cardConfig.additionalGroupBy.alias,
                expr: { btql: cardConfig.additionalGroupBy.btql },
              },
            ]
          : []),
        ...(groupBys?.map((g) => ({
          alias: `${CARD_GROUP_BY_PREFIX}${g}`,
          expr: { btql: g },
        })) ?? []),
      ],
      pivot,
      unpivot,
      measures,
      filter,
      sort: [{ expr: { btql: "time" }, dir: "asc" }],
    };
  }, [
    from,
    projectIds,
    experimentIds,
    filtersProp,
    traceLevelMetrics,
    queryTimeframe.start,
    queryTimeframe.end,
    allTimeQuery,
    cardConfig.toolMetric,
    cardConfig.chartFilters,
    cardConfig.pivot,
    cardConfig.idName,
    cardConfig.unpivot,
    cardConfig.additionalGroupBy,
    timeBucket,
    groupBys,
    measures,
  ]);

  const { data, loading, error } = useBtql({
    name: cardConfig.header?.title ?? "",
    query,
    ...getMonitorBtqlArgs({
      startTime: new Date(queryTimeframe.start).toISOString(),
      projectIds,
    }),
    disableLimit: true,
    useLocalTZ: !(tzUTC === true),
  });

  const dataAsJson = useMemo(() => {
    return data?.toArray().map((r) => r.toJSON());
  }, [data]);

  const postProcessedData = useMemo(() => {
    let data = dataAsJson;

    // processing specific for costs
    if (dataAsJson && cardConfig.applyCrunchCosts) {
      data = crunchCostsByTimeGroup({
        costData: dataAsJson,
        allAvailableModels,
      });
    }

    return data;
  }, [dataAsJson, cardConfig, allAvailableModels]);

  const [lastTimeBucket, setLastTimeBucket] = useState<TIME_BUCKET>(timeBucket);

  // hack to tie prev data to time bucket
  // really we should base time bucket on response data
  useEffect(() => {
    setLastTimeBucket(timeBucket);
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return { data: postProcessedData, loading, error, lastTimeBucket };
}

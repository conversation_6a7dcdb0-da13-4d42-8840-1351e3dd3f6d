import type { ModelSpec } from "@braintrust/proxy/schema";
import { getMetricsDataGroups } from "./get-metrics-data-groups";
import type { MetricsData } from "./groups.types";
import { calculateTokenCosts } from "./costs";
import { CARD_GROUP_BY_PREFIX } from "./card-config/monitor-cards.constants";

type AvailableModelsType = { [name: string]: ModelSpec };
const BIG_ZERO = BigInt(0);

// for costs data, make some additional assumptions to special metrics data keys
interface MetricsCostData extends MetricsData {
  model?: string;
  promptUncachedTokens?: number | bigint | null;
  promptCacheCreationTokens?: number | bigint | null;
  promptCachedTokens?: number | bigint | null;
  completionTokens?: number | bigint | null;
}

// out has more restrictive model and cost values
interface MetricsCostDataOut extends MetricsData {
  promptUncachedTokens: number;
  promptCacheCreationTokens: number;
  promptCachedTokens: number;
  completionTokens: number;
}

export function crunchCostsByTimeGroup({
  costData,
  allAvailableModels,
}: {
  costData: MetricsCostData[];
  allAvailableModels: AvailableModelsType;
}): MetricsCostDataOut[] {
  const costDetailsByTimeGroup: Map<string, MetricsCostDataOut> = new Map();
  for (const item of costData) {
    const { time, model, count } = item;

    const { groupBys, groupKey } = getMetricsDataGroups(item);
    const bucketKey = `${time}-${groupKey}`;
    const groups = groupBys.reduce((acc: Record<string, string>, groupBy) => {
      acc[`${CARD_GROUP_BY_PREFIX}${groupBy.key}`] = groupBy.value;
      return acc;
    }, {});

    const bucket: MetricsCostDataOut = costDetailsByTimeGroup.get(
      bucketKey,
    ) ?? {
      promptUncachedTokens: 0,
      promptCacheCreationTokens: 0,
      promptCachedTokens: 0,
      completionTokens: 0,
      count: BigInt(0),
      time,
      ...groups,
    };

    if (!model || !allAvailableModels[model]) continue;

    const modelSchema = allAvailableModels[model];

    const promptUncachedTokens = calculateTokenCosts(
      item.promptUncachedTokens ?? BIG_ZERO,
      modelSchema.input_cost_per_mil_tokens,
    );
    const promptCachedTokens = calculateTokenCosts(
      item.promptCachedTokens ?? BIG_ZERO,
      modelSchema.input_cache_read_cost_per_mil_tokens,
    );
    const promptCacheCreationTokens = calculateTokenCosts(
      item.promptCacheCreationTokens ?? BIG_ZERO,
      modelSchema.input_cache_write_cost_per_mil_tokens,
    );
    const completionTokens = calculateTokenCosts(
      item.completionTokens ?? BIG_ZERO,
      modelSchema.output_cost_per_mil_tokens,
    );

    bucket.promptUncachedTokens += promptUncachedTokens;
    bucket.promptCachedTokens += promptCachedTokens;
    bucket.promptCacheCreationTokens += promptCacheCreationTokens;
    bucket.completionTokens += completionTokens;
    bucket.count += BigInt(count);
    costDetailsByTimeGroup.set(bucketKey, bucket);
  }

  return [...costDetailsByTimeGroup.values()];
}

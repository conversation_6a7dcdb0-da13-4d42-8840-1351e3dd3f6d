import { Request, Response } from "express";
import * as duckdb from "duckdb";
import { z } from "zod";
import { getRequestContext } from "./request_context";
import { OBJECT_CACHE } from "./object_cache";
import {
  AccessDeniedError,
  BadRequestError,
  BT_INTERNAL_TRACE_ID_HEADER,
  BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
  getWasCachedToken,
  InternalServerError,
  isEmpty,
  wrapZodError,
} from "./util";
import { genSingleObjectIdValue } from "./object_scan";
import { ObjectType } from "./schema";
import { buildParquet, createDuckdbJsonSchema, runBtql } from "./btql";
import {
  auditLogOriginSchema,
  BRAINTRUST_LOGICAL_SCHEMA,
  builtInMetrics,
  metricUnitSuffix,
} from "@braintrust/local/api-schema";
import { ResponseSchema } from "@braintrust/btql/binder";
import { CustomColumn, loadCustomColumnsUnchecked } from "./project-columns";
import { ident, join, sql, ToSQL } from "@braintrust/btql/planner";
import {
  APISecret,
  getAvailableModels,
  ModelEndpointType,
  ModelSpec,
} from "@braintrust/proxy/schema";
import {
  computeSummaryScoreBatches,
  constructFullSummaryScan,
  DiscriminatedProjectScore,
  discriminatedProjectScoreSchema,
  EXCLUDE_METRICS,
  experimentGroupStructureQuery,
  experimentScanSpanSummary,
  flattenNestedFields,
  isAggregateScore,
  isHumanReviewScore,
  isOnlineScore,
  lexicographicalComparison,
  makeChangesQueries,
  makeFullSummary,
  makeSummaryQueries,
  parseScoresAndMetrics,
  singleQuote,
  SUMMARY_DEFAULT_GROUP_KEY,
} from "@braintrust/local/query";
import { ScoreSummary, MetricSummary } from "braintrust";
import { duckq, getDuckDBConn } from "./db/duckdb";
import { ORG_NAME } from "./env";
import { customFetchRequest } from "./custom_fetch";
import {
  AclObjectType,
  projectSchema,
  type Project,
} from "@braintrust/typespecs";
import { sha256 } from "./hash";
import { getRedis } from "./redis";
import { PENDING_FLUSHABLES } from "./pending_flushables";
import { inferSchema } from "@braintrust/btql/schema";
import { AliasExpr } from "@braintrust/btql/parser";
import { getLogger } from "./instrumentation/logger";
import { otelWrapTraced } from "./instrumentation/api";
import { getPG } from "./db/pg";
import { isInteger } from "lodash";
import { brainstoreDefault } from "./brainstore/brainstore";

export const expSummaryRequestSchema = z
  .strictObject({
    experiment_id: z.string(),
    base_experiment_id: z.string().optional(),
    full: z
      .boolean()
      .optional()
      .describe(
        "For testing purposes, return all aggregates for each group, not just the top-level ones",
      ),
  })
  .strip();

export async function runExperimentSummarizeRequest(
  req: Request,
  res: Response,
) {
  const ctx = getRequestContext(req);
  const parsed = expSummaryRequestSchema.safeParse(ctx.data);
  if (!parsed.success) {
    res
      .status(400)
      .json({ error: "Invalid request", errors: parsed.error.errors });
    return;
  }
  const body = parsed.data;

  res.json(
    await runExperimentSummarizeRequestInternal({
      body,
      appOrigin: ctx.appOrigin,
      authToken: ctx.token,
    }),
  );
}

export async function runExperimentSummarizeRequestInternal({
  body,
  appOrigin,
  authToken,
}: {
  body: z.infer<typeof expSummaryRequestSchema>;
  appOrigin: string;
  authToken: string | undefined;
}) {
  const objectIds = [body.experiment_id].concat(
    body.base_experiment_id ? [body.base_experiment_id] : [],
  );

  const aclChecks = await OBJECT_CACHE.checkAndGetMulti({
    appOrigin,
    authToken,
    aclObjectType: "experiment",
    overrideRestrictObjectType: undefined,
    objectIds,
  });

  for (const id of objectIds) {
    const aclCheck = aclChecks[id];
    if (!aclCheck || !aclCheck.permissions.includes("read")) {
      throw new AccessDeniedError({
        permission: "read",
        objectType: "experiment",
        objectId: id,
      });
    }
  }

  const org = aclChecks[body.experiment_id].parent_cols.get("organization");
  if (!org) {
    throw new BadRequestError(`No organization for exp ${body.experiment_id}`);
  }
  const project = aclChecks[body.experiment_id].parent_cols.get("project");
  if (!project) {
    throw new BadRequestError(`No project for exp ${body.experiment_id}`);
  }

  return await runExperimentSummarize({
    experimentId: body.experiment_id,
    baseExperimentId: body.base_experiment_id,
    appOrigin,
    authToken,
    orgName: org.name,
    projectName: project.name,
    projectId: project.id,
    full: body.full,
  });
}

export async function runExperimentSummarize({
  experimentId,
  baseExperimentId,
  appOrigin,
  authToken,
  orgName,
  projectName,
  projectId,
  full,
}: {
  experimentId: string;
  baseExperimentId: string | undefined;
  appOrigin: string;
  authToken?: string;
  orgName: string;
  projectName: string;
  projectId: string;
  full?: boolean;
}) {
  const [_scoreConfig] = await Promise.all([
    fetchProjectScoreConfig({
      orgName,
      projectName,
      projectId,
      appOrigin,
      authToken,
    }),
  ]);
  const scoreConfig = _scoreConfig ?? [];
  const { path: experimentPath, cleanup: experimentCleanup } =
    await loadSummaryParquet(appOrigin, authToken, "experiment", experimentId);
  let baseExperimentPath = null;
  let baseExperimentCleanup = () => {};
  try {
    if (baseExperimentId) {
      const result = await loadSummaryParquet(
        appOrigin,
        authToken,
        "experiment",
        baseExperimentId,
      );
      baseExperimentPath = result.path;
      baseExperimentCleanup = result.cleanup;
    }

    return await finishExperimentSummarize({
      experimentParquet: experimentPath,
      baseExperimentParquet: baseExperimentPath,
      scoreConfig,
      full,
    });
  } finally {
    experimentCleanup();
    baseExperimentCleanup();
  }
}

export const fetchProjectScoreConfig = otelWrapTraced(
  "fetchProjectScoreConfig",
  wrapCachedControlPlaneRequest(async function fetchProjectScoreConfig({
    orgName,
    projectName,
    appOrigin,
    authToken,
  }: {
    orgName: string;
    projectName: string;
    appOrigin: string;
    authToken?: string;
  }): Promise<DiscriminatedProjectScore[] | null> {
    const projectScoresResp = await customFetchRequest(
      `${appOrigin}/api/project_score/get`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ org_name: orgName, project_name: projectName }),
      },
    );
    if (!projectScoresResp.ok) {
      const responseBody = await projectScoresResp.body.text();
      getLogger().warn(
        {
          statusCode: projectScoresResp.statusCode,
          responseBody,
        },
        "Failed to fetch project scores. Returning null.",
      );
      return null;
    }

    const unknownArray = z
      .array(z.unknown())
      .parse(await projectScoresResp.body.json());

    const scores: z.infer<typeof discriminatedProjectScoreSchema>[] = [];
    for (const x of unknownArray) {
      const parsed = discriminatedProjectScoreSchema.safeParse(x);
      if (parsed.success) {
        scores.push(parsed.data);
      } else {
        getLogger().warn(
          {
            score: x,
            errors: parsed.error.errors,
          },
          "Failed to parse score. Will ignore. You can likely solve this by updating your stack.",
        );
      }
    }

    return scores;
  }),
);

export const fetchProject = otelWrapTraced(
  "fetchProject",
  wrapCachedControlPlaneRequest(async function fetchProject({
    projectId,
    appOrigin,
    authToken,
  }: {
    projectId: string;
    appOrigin: string;
    authToken?: string;
  }): Promise<Project | null> {
    const projectResp = await customFetchRequest(
      `${appOrigin}/api/project/get_id`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id: projectId }),
      },
    );
    if (!projectResp.ok) {
      const responseBody = await projectResp.body.text();
      getLogger().warn(
        {
          statusCode: projectResp.statusCode,
          responseBody,
        },
        "Failed to fetch project. Returning null.",
      );
      return null;
    }
    const project = projectSchema.parse(await projectResp.body.json());
    return project;
  }),
);

export type ExperimentSummaryResponse = {
  scores: Record<string, ScoreSummary>;
  metrics: Record<string, MetricSummary>;
};

const fetchCustomModels = otelWrapTraced(
  "fetchCustomModels",
  wrapCachedControlPlaneRequest(async function fetchCustomModels({
    appOrigin,
    authToken,
    orgName,
  }: {
    appOrigin: string;
    authToken?: string;
    orgName: string;
  }): Promise<{ [name: string]: ModelSpec }> {
    let customModels: { [name: string]: ModelSpec } = {};
    const customModelsResp = await customFetchRequest(
      `${appOrigin}/api/secret`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          types: ModelEndpointType,
          org_name: orgName,
          mode: "full-skip-sec",
        }),
      },
    );

    if (customModelsResp.ok) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      const models = (await customModelsResp.body.json()) as any;
      customModels = models
        .filter(
          (row: APISecret) => ORG_NAME === "*" || row.org_name === ORG_NAME,
        )
        .reduce(
          (acc: { [name: string]: ModelSpec }, row: APISecret) => ({
            ...acc,
            ...(row.metadata?.customModels
              ? { ...row.metadata.customModels }
              : {}),
          }),
          {},
        );
    }
    return customModels;
  }),
);

export const fetchCustomColumns = otelWrapTraced(
  "fetchCustomColumns",
  wrapCachedControlPlaneRequest(async function fetchCustomColumns({
    objectId,
    objectType,
    subtype,
    variant,
  }: {
    objectType?: string;
    objectId?: string;
    subtype?: string;
    variant?: string;
  }): Promise<CustomColumn[]> {
    return loadCustomColumnsUnchecked({
      objectType,
      objectId,
      subtype,
      variant,
    });
  }),
);

function wrapCachedControlPlaneRequest<A, T>(
  requestFn: (
    args: A & {
      appOrigin: string;
      authToken?: string;
      projectId: string;
    },
  ) => Promise<T>,
): (
  args: A & { appOrigin: string; authToken?: string; projectId: string },
) => Promise<T> {
  return async (args) => {
    const key = `summary-cache/${args.projectId}/${requestFn.name}/${sha256(JSON.stringify(args))}`;
    const redis = await getRedis();
    const cached = await redis.get(key);
    if (cached) {
      return JSON.parse(cached);
    }

    const result = await requestFn(args);
    PENDING_FLUSHABLES.add(
      (async () => {
        await Promise.all([
          redis.set(key, JSON.stringify(result), { EX: 60 }),
          redis.sAdd(makeSummaryProjectIdSetKey(args.projectId), key),
          redis.expire(makeSummaryProjectIdSetKey(args.projectId), 60),
        ]);
      })(),
    );
    return result;
  };
}

type FullSummary = ReturnType<
  typeof makeFullSummary
>[typeof SUMMARY_DEFAULT_GROUP_KEY];

async function finishExperimentSummarize({
  experimentParquet,
  baseExperimentParquet,
  scoreConfig,
  full,
}: {
  experimentParquet: string;
  baseExperimentParquet: string | null;
  scoreConfig: DiscriminatedProjectScore[];
  full?: boolean;
}): Promise<FullSummary | ExperimentSummaryResponse> {
  const experimentScan = `SELECT * FROM parquet_scan(${singleQuote(
    experimentParquet,
  )})`;
  const baseExperimentScan = baseExperimentParquet
    ? `SELECT * FROM parquet_scan(${singleQuote(baseExperimentParquet)})`
    : null;

  const conn = getDuckDBConn();
  const [experimentScores, baseExperimentScores] = await Promise.all([
    loadScoresAndMetrics(conn, experimentScan),
    loadScoresAndMetrics(conn, baseExperimentScan),
  ]);

  const { allSummaryDefBatches, comparableDefBatches, summaryMetricNames } =
    computeSummaryScoreBatches({
      scoreFields: experimentScores.scores,
      metricFields: experimentScores.metricFields,
      comparisonScoreFields: baseExperimentScores.scores,
      comparisonMetricFields: baseExperimentScores.metricFields,
      scoreConfig,
      // NOTE: When we add support for custom user-defined metrics, we should fetch this alongside scoreConfig and propagate
      // it here.
      metricDefinitions: builtInMetrics,
    });

  const primaryExperimentScan = experimentScan;
  const comparisonExperimentScan = baseExperimentScan;

  const groupKey: () => string = () => singleQuote(SUMMARY_DEFAULT_GROUP_KEY);
  const comparisonKey = (relation: string) => `${relation}."comparison_key"`;

  const summaryQueries = makeSummaryQueries({
    experimentScan: primaryExperimentScan,
    allSummaryDefBatches,
    groupKey,
    comparisonKey,
  });

  const changesQueries = makeChangesQueries({
    primaryExperimentScan,
    comparisonExperimentScan,
    comparableDefBatches,
    groupKey,
    comparisonKey,
  });

  const summaryData = await Promise.all(
    (summaryQueries ?? []).map((q) => duckq(conn, q)),
  );

  const diffData =
    changesQueries !== null
      ? await Promise.all(changesQueries.map((q) => duckq(conn, q)))
      : null;

  const fullSummary = makeFullSummary({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    summaryData: summaryData.flat() as any,
    compareSummaryData: [],
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    diffData: (diffData?.flat() ?? null) as any,
  });

  if (full) {
    return fullSummary[SUMMARY_DEFAULT_GROUP_KEY];
  }

  const scoresAndMetrics = fullSummary[SUMMARY_DEFAULT_GROUP_KEY];

  const ret: ExperimentSummaryResponse = { scores: {}, metrics: {} };
  for (const [key, value] of Object.entries(scoresAndMetrics ?? {})) {
    if (EXCLUDE_METRICS.includes(key)) continue;

    if (summaryMetricNames.includes(key) && !isEmpty(value.avg)) {
      ret.metrics[key] = {
        name: key,
        metric: value.avg,
        unit: (() => {
          for (const [key_substring, symbol] of [
            ["cost", "$"],
            ["token", "tok"],
            ["duration", "s"],
          ]) {
            if (key.includes(key_substring)) {
              return symbol;
            }
          }

          for (const metric of builtInMetrics) {
            if (metric.field_name === key && metric.unit) {
              return metricUnitSuffix(metric.unit);
            }
          }

          return "";
        })(),
        diff: value.diffAvg ?? undefined,
        improvements: value.improvements?.length ?? 0,
        regressions: value.regressions?.length ?? 0,
      };
    } else if (!isEmpty(value.avg)) {
      ret.scores[key] = {
        name: key,
        score: value.avg,
        diff: value.diffAvg ?? undefined,
        improvements: value.improvements?.length ?? 0,
        regressions: value.regressions?.length ?? 0,
      };
    }
  }

  return ret;
}

async function loadScoresAndMetrics(
  conn: duckdb.Connection,
  experimentScan: string | null,
) {
  if (!experimentScan) {
    return {
      scores: [],
      metricFields: [],
    };
  }
  const experimentStructure = experimentGroupStructureQuery({
    hasScores: true,
    hasMetrics: true,
    experimentScan,
  });
  const results = await duckq(conn, experimentStructure);

  const [dataScoreFields, metricFields] = parseScoresAndMetrics(results[0]);
  return {
    scores: flattenNestedFields(dataScoreFields ?? []),
    metricFields: flattenNestedFields(metricFields ?? []),
  };
}

async function loadSummaryParquet(
  appOrigin: string,
  ctxToken: string | undefined,
  objectType: ObjectType,
  objectId: string,
) {
  const btqlResult = await runBtql({
    body: {
      query: {
        from: {
          op: "function",
          name: {
            op: "ident",
            name: [objectType],
          },
          args: [{ op: "literal", value: objectId }],
          shape: "summary",
        },
        select: [
          {
            op: "star",
          },
        ],
      },
      use_brainstore: true,
      brainstore_realtime: true,
      disable_limit: true,
    },
    appOrigin,
    ctxToken,
    // These checks should be cached, and they're used to populate org name, etc. fields.
    // We could instead pass these directly.
    skipAclCheck: false,
    skipCustomColumns: true, // Not needed for high level summary
  });
  if ("explain" in btqlResult) {
    throw new InternalServerError("Unexpected explain result");
  }

  const parquet = await buildParquet(
    btqlResult.rows,
    btqlResult.resultSchema,
    btqlResult.duckdbSchema,
  );
  return parquet;
}

export const datasetSummaryRequestSchema = z
  .strictObject({
    dataset_id: z.string(),
  })
  .strip();

export async function runDatasetSummarizeRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const parsed = datasetSummaryRequestSchema.safeParse(ctx.data);
  if (!parsed.success) {
    res
      .status(400)
      .json({ error: "Invalid request", errors: parsed.error.errors });
    return;
  }
  const body = parsed.data;
  return res.json(
    await runDatasetSummarize({
      datasetId: body.dataset_id,
      appOrigin: ctx.appOrigin,
      ctxToken: ctx.token,
      setTraceIdHeader: (traceId) => {
        res.setHeader(BT_INTERNAL_TRACE_ID_HEADER, traceId);
      },
    }),
  );
}

const datasetSummarySchema = z.strictObject({
  total_records: z.number().int(),
});
export type DatasetSummary = z.infer<typeof datasetSummarySchema>;
export async function runDatasetSummarize({
  datasetId,
  appOrigin,
  ctxToken,
  setTraceIdHeader,
}: {
  datasetId: string;
  appOrigin: string;
  ctxToken?: string;
  setTraceIdHeader?: (traceId: string) => void;
}): Promise<DatasetSummary | null> {
  const defaultStatus = brainstoreDefault();
  const btqlResult = await runBtql({
    body: {
      query: {
        from: {
          op: "function",
          name: {
            op: "ident",
            name: ["dataset"],
          },
          args: [{ op: "literal", value: datasetId }],
        },
        measures: [
          {
            alias: "total_records",
            expr: {
              btql: "count(1)",
            },
          },
        ],
      },
      use_brainstore: defaultStatus === "force" || defaultStatus === "default",
      brainstore_realtime: true,
    },
    appOrigin,
    ctxToken,
    setTraceIdHeader,
  });
  if ("rows" in btqlResult && btqlResult.rows.length === 1) {
    return datasetSummarySchema.parse(btqlResult.rows[0]);
  } else {
    return null;
  }
}

export async function createSummaryData({
  orgName,
  projectName,
  projectId,
  rows,
  resultSchema,
  appOrigin,
  authToken,
  objectType,
  objectId,
}: {
  orgName: string;
  projectName: string;
  projectId: string;
  rows: Record<string, unknown>[];
  resultSchema: ResponseSchema;
  appOrigin: string;
  authToken: string | undefined;
  objectType: AclObjectType;
  objectId: string;
}): Promise<{
  rows: Record<string, unknown>[];
  resultSchema: ResponseSchema;
  duckdbSchema: Record<string, unknown>;
}> {
  const [
    _scoreConfig,
    projectInfo,
    { path: experimentPath, cleanup: experimentCleanup },
  ] = await Promise.all([
    fetchProjectScoreConfig({
      orgName,
      projectName,
      projectId,
      appOrigin,
      authToken,
    }),
    fetchProject({ projectId, appOrigin, authToken }),
    buildParquet(rows, resultSchema),
  ]);
  const scoreConfig = _scoreConfig ?? [];

  try {
    const experimentScan = `SELECT * FROM parquet_scan(${singleQuote(
      experimentPath,
    )})`;

    const conn = getDuckDBConn();
    const [experimentScores, availableModelCosts] = await Promise.all([
      loadScoresAndMetrics(conn, experimentScan),
      fetchModelCosts({ appOrigin, authToken, orgName, projectId }),
    ]);

    const modelCostsTableName = "model_specs_summary";
    const modelSelects = Object.entries(availableModelCosts).map(
      ([modelName, modelCosts], i) => {
        const {
          input_cost_per_mil_tokens,
          output_cost_per_mil_tokens,
          input_cache_write_cost_per_mil_tokens,
          input_cache_read_cost_per_mil_tokens,
        } = modelCosts;
        const costOpt = (cost?: number | null) =>
          !isEmpty(cost) ? `CAST(${cost} AS DOUBLE)` : "NULL";
        return `SELECT CAST('${modelName}' AS VARCHAR)${
          i === 0 ? ' AS "model"' : ""
        }, ${costOpt(input_cost_per_mil_tokens)}${
          i === 0 ? ' AS "input_cost_per_mil_tokens"' : ""
        }, ${costOpt(output_cost_per_mil_tokens)}${
          i === 0 ? ' AS "output_cost_per_mil_tokens"' : ""
        }, ${costOpt(input_cache_write_cost_per_mil_tokens)}${
          i === 0 ? ' AS "input_cache_write_cost_per_mil_tokens"' : ""
        }, ${costOpt(input_cache_read_cost_per_mil_tokens)}${
          i === 0 ? ' AS "input_cache_read_cost_per_mil_tokens"' : ""
        }`;
      },
    );

    await duckq(
      conn,
      `CREATE TEMPORARY TABLE ${modelCostsTableName} AS ${modelSelects.join(
        " UNION ALL ",
      )}`,
    );

    const scanTable = experimentScanSpanSummary({
      experimentScanRaw: experimentScan,
      modelSpecScan: `SELECT * FROM ${modelCostsTableName}`,
      auditLogScan: null /* TODO */,
      scoreFields: experimentScores.scores,
      scoreConfig,
      comparisonKey: projectInfo?.settings?.comparison_key ?? null,
      hasErrorField: true,
      isRootAvailable: true,
      /* we should be able to skp all the filter fields, i.e.
      plainFilters, postAggregationFilters, commentsFilters, tags */
    });
    const summaryScan = `SELECT *, NULL as comments FROM (${scanTable})`;

    const primaryExperimentScan = constructFullSummaryScan({
      experimentScanRaw: experimentScan,
      summaryScan,
      auditLogScan: null /* TODO */,
      customColumns: [],
      includeScoresMap: false,
      customColumnFilters: [],
    });

    if (!primaryExperimentScan) {
      throw new InternalServerError(
        "Failed to construct primary experiment scan",
      );
    }

    const previewSize = 128 - 3;
    const previewScan = `
    SELECT * REPLACE (
      LEFT(input, ${previewSize}) AS input,
      LEFT(output, ${previewSize}) AS output,
      LEFT(expected, ${previewSize}) AS expected,
      LEFT(error, ${previewSize}) AS error,
    )
    FROM (${primaryExperimentScan})
    `;

    const data = await duckq(conn, previewScan);

    const { duckdbSchema } = modifyResultSchemaMakeDuckdbSchema({
      resultSchema,
      data,
      scoreConfig,
      customColumns: [],
    });

    // Manually deserialize the JSON objects that were joined in via the
    // raw table scan
    data.forEach((row) => {
      row.span_type_info = JSON.parse(row.span_type_info);

      row.input = partialParseJson(row.input);
      row.output = partialParseJson(row.output);
      row.expected = partialParseJson(row.expected);
      row.error = partialParseJson(row.error);

      row.metadata = JSON.parse(row.metadata);
      row.span_attributes = JSON.parse(row.span_attributes);
      row.origin = JSON.parse(row.origin);
      row.tags = JSON.parse(row.tags);
    });

    return {
      rows: data,
      resultSchema,
      duckdbSchema,
    };
  } finally {
    experimentCleanup();
  }
}

export type ModelCostsMap = Record<
  string,
  {
    input_cost_per_mil_tokens?: number;
    output_cost_per_mil_tokens?: number;
    input_cache_write_cost_per_mil_tokens?: number;
    input_cache_read_cost_per_mil_tokens?: number;
  }
>;

export const fetchModelCosts = otelWrapTraced(
  "fetchModelCosts",
  async function fetchModelCosts({
    appOrigin,
    authToken,
    orgName,
    projectId,
  }: {
    appOrigin: string;
    authToken: string | undefined;
    orgName: string;
    projectId: string;
  }): Promise<ModelCostsMap> {
    const customModels = await fetchCustomModels({
      appOrigin,
      authToken,
      orgName,
      projectId,
    });

    const availableModelCosts = Object.entries({
      ...getAvailableModels(),
      ...customModels,
    }).reduce(
      (
        acc: {
          [key: string]: {
            input_cost_per_mil_tokens?: number;
            output_cost_per_mil_tokens?: number;
            input_cache_read_cost_per_mil_tokens?: number;
            input_cache_write_cost_per_mil_tokens?: number;
          };
        },
        [modelName, modelSpec],
      ) => {
        const {
          input_cost_per_mil_tokens,
          output_cost_per_mil_tokens,
          input_cache_read_cost_per_mil_tokens,
          input_cache_write_cost_per_mil_tokens,
          input_cost_per_token,
          output_cost_per_token,
        } = modelSpec;
        if (
          !input_cost_per_mil_tokens &&
          !output_cost_per_mil_tokens &&
          !input_cost_per_token &&
          !output_cost_per_token
        )
          return acc;
        acc[modelName] = {
          input_cost_per_mil_tokens:
            input_cost_per_mil_tokens ??
            (input_cost_per_token && input_cost_per_token * 1000000) ??
            undefined,
          output_cost_per_mil_tokens:
            output_cost_per_mil_tokens ??
            (output_cost_per_token && output_cost_per_token * 1000000) ??
            undefined,
          input_cache_read_cost_per_mil_tokens:
            input_cache_read_cost_per_mil_tokens ?? undefined,
          input_cache_write_cost_per_mil_tokens:
            input_cache_write_cost_per_mil_tokens ?? undefined,
        };
        return acc;
      },
      {},
    );

    return availableModelCosts;
  },
);

function partialParseJson(value: string) {
  try {
    return JSON.parse(value);
  } catch {
    if (value[0] === '"') {
      return value.slice(1).replace(/\\"/g, '"').replace(/\\n/g, "\n") + "...";
    } else {
      return value;
    }
  }
}

function scorePosition(scoreConfig: DiscriminatedProjectScore[], name: string) {
  const config = scoreConfig.find((s) => s.name === name);
  if (!config) {
    return 1;
  }
  if (isHumanReviewScore(config.score_type)) return 2;
  if (isAggregateScore(config.score_type)) {
    return 0;
  }
  return 1;
}

const METRIC_ORDER = [
  "cached",
  "start",
  "end",
  "duration",
  "llm_duration",
  ...builtInMetrics.map((m) => m.field_name),
];
function makeMetricOrder(name: string): Record<string, unknown> {
  const index = METRIC_ORDER.indexOf(name);
  return {
    name: index === -1 ? METRIC_ORDER.length : index,
    value: name,
  };
}

export function modifyResultSchemaMakeDuckdbSchema({
  resultSchema,
  data,
  scoreConfig,
  customColumns,
  dedupeScoresAndMetrics,
}: {
  resultSchema: ResponseSchema;
  data: Record<string, unknown>[];
  scoreConfig: DiscriminatedProjectScore[];
  customColumns: AliasExpr[];
  dedupeScoresAndMetrics?: boolean;
}) {
  const scoreSetInsensitive = new Set<string>();
  const metricSetInsensitive = new Set<string>();
  const scoreSet = new Set<string>();
  const metricSet = new Set<string>();
  const intMetrics = new Set<string>();
  for (const row of data) {
    Object.entries(row?.scores ?? {}).forEach(([k, v]) => {
      if (v === null || v === undefined) {
        return;
      }
      if (
        !dedupeScoresAndMetrics ||
        !scoreSetInsensitive.has(k.toLowerCase())
      ) {
        scoreSet.add(k);
      }
      scoreSetInsensitive.add(k.toLowerCase());
    });
    Object.entries(row?.metrics ?? {}).forEach(([k, v]) => {
      if (
        !dedupeScoresAndMetrics ||
        !metricSetInsensitive.has(k.toLowerCase())
      ) {
        metricSet.add(k);
        if (isInteger(v) || typeof v === "bigint") {
          intMetrics.add(k);
        }
      }
      metricSetInsensitive.add(k.toLowerCase());
    });
  }
  for (const score of scoreConfig) {
    if (!score.config?.destination && !isOnlineScore(score.score_type)) {
      scoreSet.add(score.name);
    }
  }
  const scoreNames = Array.from(scoreSet);

  scoreNames.sort((a, b) => {
    return scorePosition(scoreConfig, a) - scorePosition(scoreConfig, b);
  });

  const metricNames = Array.from(metricSet);
  metricNames.sort((a, b) => {
    return lexicographicalComparison(makeMetricOrder(a), makeMetricOrder(b), [
      "name",
      "value",
    ]);
  });

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
  const logicalSchema = (BRAINTRUST_LOGICAL_SCHEMA as any).properties.experiment
    .items;
  resultSchema.items.properties = {
    id: { type: "string" },
    _xact_id: { type: "string" },
    comparison_key: { type: "string" },
    span_id: { type: "string" },
    root_span_id: { type: "string" },
    span_type_info: {
      type: "object",
    },

    input: {},
    output: {},
    expected: {},
    tags: { type: ["array", "null"], items: { type: "string" } },

    scores: {
      type: "object",
      properties: Object.fromEntries(
        scoreNames.map((k) => [k, { type: ["number", "null"] }]),
      ),
      additionalProperties: { type: ["number", "null"] },
    },
    metrics: {
      type: "object",
      properties: Object.fromEntries(
        metricNames.map((k) => [
          k,
          {
            type: intMetrics.has(k) ? ["integer", "null"] : ["number", "null"],
          },
        ]),
      ),
      additionalProperties: { type: ["number", "null"] },
    },
    metadata: {},
    error: { type: ["object", "null"] },
    origin: { type: ["object", "null"] },

    created: { type: "string", format: "date-time" },
  };

  const customColumnSchema = {
    type: "object" as const,
    properties: Object.fromEntries(customColumns.map((c) => [c.alias, {}])),
  };
  const customColumnNames = new Set<string>();
  for (const customColumn of customColumns) {
    customColumnNames.add(customColumn.alias);
  }

  // The main purpose of this is to infer the schema of the custom columns
  // which may be truncated anyway, so use depth 0 and look at a handful of rows.
  for (const row of data.slice(0, 10)) {
    inferSchema({
      schema: customColumnSchema,
      value: row,
      depth: 1,
      ignoreAdditionalProperties: true,
    });
  }

  for (const [name, schema] of Object.entries(customColumnSchema.properties)) {
    if (customColumnNames.has(name)) {
      resultSchema.items.properties[name] = schema;
    }
  }

  // Put these at the end to be consistent with the UI
  resultSchema.items.properties = {
    ...resultSchema.items.properties,
    comments: { type: "object" },
    span_attributes: logicalSchema.properties!["span_attributes"],
  };

  const duckdbSchema = createDuckdbJsonSchema(resultSchema);

  // Specialize the scores & metrics types
  duckdbSchema.scores =
    scoreNames.length > 0
      ? `STRUCT(${scoreNames
          .map((k) => sql`${ident(k)} DOUBLE`.toPlainStringQuery())
          .join(", ")})`
      : "JSON";

  duckdbSchema.metrics =
    metricNames.length > 0
      ? `STRUCT(${metricNames
          .map((k) =>
            intMetrics.has(k)
              ? sql`${ident(k)} BIGINT`.toPlainStringQuery()
              : sql`${ident(k)} DOUBLE`.toPlainStringQuery(),
          )
          .join(", ")})`
      : "JSON";

  return {
    duckdbSchema,
  };
}

function makeSummaryProjectIdSetKey(projectId: string) {
  return `summary-keys/${projectId}`;
}

export async function flushSummaryCacheRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);

  const { projectId } = wrapZodError(() =>
    z.object({ projectId: z.string() }).parse(ctx.data),
  );

  const permissions = await OBJECT_CACHE.checkAndGet({
    appOrigin: ctx.appOrigin,
    authToken: ctx.token,
    aclObjectType: "project",
    overrideRestrictObjectType: undefined,
    objectId: projectId,
    wasCachedToken: getWasCachedToken(
      req,
      BT_OBJECT_CACHE_WAS_CACHED_REDIS_TOKEN_HEADER,
    ),
  });
  if (!permissions.permissions.includes("update")) {
    throw new AccessDeniedError({
      permission: "update",
      objectType: "project",
      objectId: projectId,
    });
  }

  await flushSummaryCache({ projectId });
  res.json({ success: true });
}

// NOTE: We should also call this when we update costs on the control plane, but
// that's not yet implemented.
export async function flushSummaryCache({ projectId }: { projectId: string }) {
  const redis = await getRedis();
  const keys = await redis.sMembers(makeSummaryProjectIdSetKey(projectId));
  if (keys.length > 0) {
    await redis.del(keys);
  }
}

const MAX_ID_FETCH_SIZE = 1000;

export const fetchComments = otelWrapTraced(
  "fetchComments",
  async function fetchComments({
    objectType,
    objectId,
    ids,
  }: {
    objectType: ObjectType;
    objectId: string;
    ids: Set<string>;
  }) {
    let origins: ToSQL | null = null;
    if (ids.size === 0) {
      return {};
    } else if (ids.size <= MAX_ID_FETCH_SIZE) {
      origins = sql`, origins(origin_id) AS (
        VALUES
          ${join(
            Array.from(ids).map((id) => sql`(${id})`),
            ",",
          )}
      )`;
    }

    /**
     * ChatGPT helped me make sure this query uses the index on (object_id, origin_id)
     * Explain:
     * Sort  (cost=222.60..222.61 rows=1 width=111)
     *   Sort Key: ((comments.data ->> 'created'::text)) DESC
     *   CTE target_obj
     *     ->  Result  (cost=0.00..0.01 rows=1 width=32)
     *   ->  Subquery Scan on comments  (cost=222.56..222.58 rows=1 width=111)
     *         Filter: (NOT comments._object_delete)
     *         ->  Unique  (cost=222.56..222.57 rows=1 width=125)
     *               ->  Sort  (cost=222.56..222.57 rows=1 width=125)
     *                     Sort Key: c.id, c._xact_id DESC
     *                     ->  Nested Loop  (cost=0.42..222.55 rows=1 width=125)
     *                           ->  CTE Scan on target_obj t  (cost=0.00..0.02 rows=1 width=32)
     *                           ->  Nested Loop  (cost=0.42..221.62 rows=90 width=772)
     *                                 ->  Values Scan on "*VALUES*"  (cost=0.00..1.12 rows=90 width=32)
     *                                 ->  Index Scan using comments_make_object_id_origin_id__xact_id_idx on comments c  (cost=0.42..2.44 rows=1 width=772)
     *                                       Index Cond: ((COALESCE(('experiment:'::text || experiment_id), ('dataset:'::text || dataset_id), ('prompt_session:'::text || prompt_session_id), CASE WHEN (log_id = 'g'::text) THEN ('global_log:'::text || project_id) WHEN (log_id = 'p'::text) THEN ('prompt:'::text || project_id) ELSE ('log:'::text || log_id) END) = t.obj_id) AND (origin_id = "*VALUES*".column1))
     *
     * If you change this query, make sure the Index Cond looks at both obj_id and origin_id.
     * You may need to ping @ankrgyl or @manugoyal to run the explain against prod if your local
     * doesn't have enough comments in the table for postgres to use the index.
     */
    const commentsQuery = sql`
WITH target_obj AS (
        SELECT ${genSingleObjectIdValue({ objectType, objectIds: [objectId] })} AS obj_id
)
${origins ?? sql``}
SELECT data, origin_id FROM (
  SELECT DISTINCT ON (c.id)
      c.data || jsonb_build_object('created', c.row_created::text) AS data,
      c.origin_id,
      c._object_delete
  FROM    comments AS c
  JOIN    target_obj  t ON make_object_id(c.project_id,
                                          c.experiment_id,
                                          c.dataset_id,
                                          c.prompt_session_id,
                                          c.log_id) = t.obj_id
  ${origins ? sql`JOIN    origins     o USING (origin_id)` : sql``}
  ORDER BY c.id, c._xact_id DESC) comments
WHERE NOT _object_delete
ORDER BY (data->>'created') DESC
    `;

    const { query, params } = commentsQuery.toNumericParamQuery();

    const db = getPG();
    const { rows } = await db.query(query, params);
    const rowsParsed = z
      .object({
        origin_id: z.string().nullish(),
        data: z
          .object({
            comment: z.unknown().nullish(),
            origin: z.unknown().nullish(),
          })
          .passthrough(),
      })
      .array()
      .parse(rows);

    const comments: Record<string, Record<string, unknown>[]> = {};

    for (const row of rowsParsed) {
      if (!row.origin_id || !ids.has(row.origin_id)) {
        continue;
      }

      const data = row.data;
      if (data.comment) {
        const origin = auditLogOriginSchema.safeParse(data.origin);
        if (origin.success) {
          const originId = origin.data.id;
          if (!comments[originId]) {
            comments[originId] = [];
          }
          comments[originId].push(data);
        }
      }
    }

    return comments;
  },
);

import {
  CARD_GROUP_BY_PREFIX,
  NO_GROUP_INTERNAL_NAME,
} from "./card-config/monitor-cards.constants";
import { type GroupBy, type MetricsData } from "./groups.types";

// consistent and unique key for given group bys, regardless of order
const getGroupKey = (groups: GroupBy[]) => {
  if (!groups.length) {
    return NO_GROUP_INTERNAL_NAME;
  }
  return groups
    .sort((a, b) => a.key.localeCompare(b.key))
    .map((g) => `${g.key}:${g.value}`)
    .join(",");
};

/**
 * For a given data item, find the group bys
 * and create a consistent group key
 */
export const getMetricsDataGroups = (item: MetricsData) => {
  const groupBys: GroupBy[] = Object.entries(item)
    .filter(([key]) => key.startsWith(CARD_GROUP_BY_PREFIX))
    .map(([groupKey, groupValue]) => ({
      key: groupKey.split(CARD_GROUP_BY_PREFIX, 2)[1],
      value: String(groupValue ?? NO_GROUP_INTERNAL_NAME),
    }));

  const groupKey = getGroupKey(groupBys);

  return {
    groupBys,
    groupKey,
  };
};

import type Orb from "orb-billing";
import Link from "next/link";
import { useState } from "react";
import {
  ArrowDown,
  ChartNoAxesColumn,
  CreditCard,
  Mail,
  Receipt,
} from "lucide-react";
import { cn } from "#/utils/classnames";
import { But<PERSON>, buttonVariants } from "#/ui/button";
import { getPageName } from "#/utils/analytics";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { isEnterprisePlan, isFreePlan, isProPlan } from "./plans";
import { useOrg } from "#/utils/user";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { Alert, AlertDescription } from "#/ui/alert";
import BillingEmailDialog from "./email-dialog";
import DowngradeModal from "./downgrade-dialog";

function formatDate(date: string | null): string {
  if (!date) return "unknown";
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

function CurrentSubscriptionSummary({
  currentSubscription,
  paymentMethodSummary,
  invoices,
  billingReportingNotice,
  refreshSubscription,
}: {
  currentSubscription: Orb.Subscription;
  paymentMethodSummary:
    | {
        last4: string;
        brand: string;
        exp_month: number;
        exp_year: number;
      }
    | null
    | undefined;
  invoices: Orb.Invoice[] | null | undefined;
  billingReportingNotice: string;
  refreshSubscription: () => Promise<void>;
}) {
  const [showDowngradeModal, setShowDowngradeModal] = useState(false);
  const [showBillingEmailDialog, setShowBillingEmailDialog] = useState(false);
  const { track } = useAppAnalytics();
  const org = useOrg();

  const planId = currentSubscription.plan.id;
  const planName = currentSubscription.plan.name;
  const status = currentSubscription.status;
  const customerBillingEmail = currentSubscription.customer.email;
  const startDate = formatDate(currentSubscription.start_date);
  const renewsOn = formatDate(
    currentSubscription.current_billing_period_end_date,
  );

  let subscriptionStatusMessage = isFreePlan(planId)
    ? "This organization is currently on the free plan"
    : `This organization is currently on the ${planName} plan and the subscription is ${status}`;
  const isEnterprise = isEnterprisePlan(currentSubscription.plan);
  if (isEnterprise) {
    subscriptionStatusMessage = `This organization is currently on an Enterprise contract`;
  }

  const customerPortalUrl = currentSubscription.customer.portal_url;

  const isPro = isProPlan(planId);

  return (
    <div className="mb-12">
      <h2 className="mb-4 text-lg font-semibold">Current plan</h2>
      <div className="rounded-md border p-6">
        <p className="mb-1 text-base">{subscriptionStatusMessage}</p>
        {isPro && (
          <p className="mb-1 text-sm text-primary-600">
            This plan was activated on {startDate} and renews on {renewsOn}.
          </p>
        )}
        {paymentMethodSummary && (
          <p className="my-4 font-mono text-sm text-primary-600">
            {paymentMethodSummary.brand && (
              <span className="capitalize">{paymentMethodSummary.brand}</span>
            )}{" "}
            **** {paymentMethodSummary.last4}
            {paymentMethodSummary.exp_month &&
              paymentMethodSummary.exp_year && (
                <span className="ml-2">
                  Exp: {String(paymentMethodSummary.exp_month).padStart(2, "0")}
                  /{String(paymentMethodSummary.exp_year).slice(-2)}
                </span>
              )}
          </p>
        )}
        {customerBillingEmail && (
          <p className="my-4 font-mono text-sm text-primary-600">
            Billing Email: {customerBillingEmail}
          </p>
        )}

        <div className="flex flex-col flex-wrap items-start gap-2 sm:flex-row">
          {customerPortalUrl && (
            <Link
              target="_blank"
              href={customerPortalUrl}
              className={cn(
                buttonVariants({
                  size: "sm",
                }),
              )}
              onClick={() => {
                track("viewUsageClick", {
                  orgName: org?.name ?? "",
                  orgId: org?.id ?? "",
                  entryPoint: "billingPage",
                  destinationUrl: customerPortalUrl,
                  sourcePage:
                    typeof window !== "undefined"
                      ? getPageName(window.location.pathname)
                      : "",
                });
              }}
            >
              <ChartNoAxesColumn className="size-3" />
              View usage
            </Link>
          )}
          {invoices?.length && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  Icon={Receipt}
                  iconClassName="size-3"
                  isDropdown
                >
                  Invoices
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Recent invoices</DropdownMenuLabel>
                {invoices.map((invoice) => (
                  <DropdownMenuItem key={invoice.id} className="p-0">
                    <Link
                      href={
                        invoice.hosted_invoice_url || customerPortalUrl || "#"
                      }
                      target="_blank"
                      className="flex w-full flex-col gap-1 p-2 text-xs"
                      onClick={() => {
                        track("viewInvoiceClick", {
                          orgName: org?.name ?? "",
                          orgId: org?.id ?? "",
                          entryPoint: "billingPage",
                          destinationUrl:
                            invoice.hosted_invoice_url ||
                            customerPortalUrl ||
                            "#",
                          invoiceId: invoice.id,
                          invoiceNumber:
                            invoice.invoice_number ||
                            `Invoice ${invoice.id.slice(-8)}`,
                          invoiceAmount: new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              invoice.currency === "credits"
                                ? "USD"
                                : invoice.currency,
                          }).format(parseFloat(invoice.amount_due)),
                          sourcePage:
                            typeof window !== "undefined"
                              ? getPageName(window.location.pathname)
                              : "",
                        });
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                          {invoice.invoice_number ||
                            `Invoice ${invoice.id.slice(-8)}`}
                        </span>
                        <span className="tabular-nums">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              invoice.currency === "credits"
                                ? "USD"
                                : invoice.currency,
                          }).format(parseFloat(invoice.amount_due))}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-primary-500">
                        <span>{formatDate(invoice.invoice_date)}</span>
                        <span className="capitalize">{invoice.status}</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          {paymentMethodSummary && (
            <Link
              href="billing/payment"
              className={cn(buttonVariants({ size: "sm" }))}
            >
              <CreditCard className="size-3" />
              Update payment method
            </Link>
          )}
          {customerBillingEmail && (
            <Button
              size="sm"
              Icon={Mail}
              iconClassName="size-3"
              onClick={() => setShowBillingEmailDialog(true)}
            >
              Update billing email
            </Button>
          )}
          {isPro && (
            <Button
              size="sm"
              Icon={ArrowDown}
              iconClassName="size-3"
              onClick={() => setShowDowngradeModal(true)}
            >
              Downgrade to free
            </Button>
          )}
        </div>

        {billingReportingNotice && (
          <Alert className="mt-6">
            <AlertDescription>
              <strong>Notice:</strong> {billingReportingNotice}
            </AlertDescription>
          </Alert>
        )}
      </div>
      <DowngradeModal
        isOpen={showDowngradeModal}
        onOpenChange={setShowDowngradeModal}
        onSuccess={refreshSubscription}
      />
      <BillingEmailDialog
        isOpen={showBillingEmailDialog}
        onOpenChange={setShowBillingEmailDialog}
        onSuccess={refreshSubscription}
      />
    </div>
  );
}

export { CurrentSubscriptionSummary };

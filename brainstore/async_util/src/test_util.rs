use util::max_counter::MaxCounter;

/// Sleep for short windows a bunch of times (adding up to 10ms). This is useful to force
/// concurrency between concurrently-joined async tasks in tests.
pub async fn yield_loop() {
    for _ in 0..100 {
        tokio::time::sleep(tokio::time::Duration::from_micros(100)).await;
    }
}

pub async fn yield_loop_explicit(num_yields: usize) {
    for _ in 0..num_yields {
        tokio::time::sleep(tokio::time::Duration::from_micros(100)).await;
    }
}

/// Guard for a max counter which increments, yields, and then decrements upon drop.
pub struct MaxCounterGuard<'a> {
    counter: &'a MaxCounter,
}

impl<'a> MaxCounterGuard<'a> {
    pub async fn new(counter: &'a MaxCounter) -> Self {
        counter.incr();
        yield_loop().await;
        Self { counter }
    }
}

impl Drop for MaxCounterGuard<'_> {
    fn drop(&mut self) {
        self.counter.decr();
    }
}

#[derive(Debug)]
pub struct TwoWaySyncPointSendAndWait {
    sender: tokio::sync::oneshot::Sender<()>,
    receiver: tokio::sync::oneshot::Receiver<()>,
}

pub struct TwoWaySyncPointWaitAndSend {
    sender: tokio::sync::oneshot::Sender<()>,
    receiver: tokio::sync::oneshot::Receiver<()>,
}

pub struct TwoWaySyncPointSend {
    sender: tokio::sync::oneshot::Sender<()>,
}

pub struct TwoWaySyncPointWait {
    receiver: tokio::sync::oneshot::Receiver<()>,
}

/// A two-way one-shot event barrier utility. This can be used for bi-directional synchronization
/// between two async tasks. The TwoWaySyncPointInitiator can send a signal to indicate it has
/// reached a certain wait point. The TwoWaySyncPointResponder can wait for this signal to be sent,
/// and then send back a signal to indicate that the first task can resume.
pub fn two_way_sync_point() -> (TwoWaySyncPointSendAndWait, TwoWaySyncPointWaitAndSend) {
    let (send_tx, send_rx) = tokio::sync::oneshot::channel();
    let (wait_tx, wait_rx) = tokio::sync::oneshot::channel();
    (
        TwoWaySyncPointSendAndWait {
            sender: send_tx,
            receiver: wait_rx,
        },
        TwoWaySyncPointWaitAndSend {
            sender: wait_tx,
            receiver: send_rx,
        },
    )
}

impl TwoWaySyncPointSendAndWait {
    pub fn send(self) -> TwoWaySyncPointWait {
        self.sender.send(()).unwrap();
        TwoWaySyncPointWait {
            receiver: self.receiver,
        }
    }

    pub async fn send_and_wait(self) {
        let waiter = self.send();
        waiter.wait().await;
    }
}

impl TwoWaySyncPointWaitAndSend {
    pub async fn wait(self) -> TwoWaySyncPointSend {
        self.receiver.await.unwrap();
        TwoWaySyncPointSend {
            sender: self.sender,
        }
    }

    // If true, the receiver has received a signal, and cannot be polled again. If false, then the
    // receiver is still waiting.
    pub fn try_wait(&mut self) -> bool {
        self.receiver.try_recv().is_ok()
    }

    pub async fn wait_and_send(self) {
        let sender = self.wait().await;
        sender.send();
    }
}

impl TwoWaySyncPointSend {
    pub fn send(self) {
        self.send_err().unwrap();
    }

    pub fn send_err(self) -> Result<(), ()> {
        self.sender.send(())
    }
}

impl TwoWaySyncPointWait {
    pub async fn wait(self) {
        self.receiver.await.unwrap();
    }

    // If true, the receiver has received a signal, and cannot be polled again. If false, then the
    // receiver is still waiting.
    pub fn try_wait(&mut self) -> bool {
        self.receiver.try_recv().is_ok()
    }
}

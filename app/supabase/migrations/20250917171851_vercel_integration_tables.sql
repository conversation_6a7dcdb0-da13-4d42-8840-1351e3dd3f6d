-- Vercel integration metadata

create table vercel_installations (
  id uuid primary key default uuid_generate_v4(),
  -- Unique id per installation of the Vercel integration
  installation_id text not null unique,
  -- Unique id for installing Vercel account
  account_id text not null,
  -- For now account_id maps to a single org_id but we could allow users to connect
  -- multiple Braintrust orgs in the future
  org_id uuid not null references organizations(id),
  integration_type text not null check (integration_type in ('marketplace', 'external')),
  access_token text not null, -- encrypted at application layer using aes-gcm
  created timestamptz default current_timestamp
);

create index idx_vercel_installations_org_id on vercel_installations(org_id);
create index idx_vercel_installations_account_id on vercel_installations(account_id);

alter table vercel_installations enable row level security;

create table vercel_user_mappings (
  id uuid primary key default uuid_generate_v4(),
  user_id uuid not null references users(id),
  -- Unique id for this Vercel user / account / installation combination.
  -- This is not a globally unique identifier for the Vercel user which is why we need to
  -- maintain this mapping.
  vercel_user_id text not null,
  created timestamptz default current_timestamp,
  unique(vercel_user_id),
  unique(user_id, vercel_user_id)
);

create index idx_vercel_user_mappings_user_id on vercel_user_mappings(user_id);

alter table vercel_user_mappings enable row level security;

---
title: "<PERSON><PERSON>"
---

import ModuleInstaller from "#/ui/docs/module-installer";
import { Callout } from "fumadocs-ui/components/callout";

# Mastra

[Mastra](https://mastra.ai/) is a TypeScript framework for building AI agents. [Mastra AI tracing](https://mastra.ai/en/docs/observability/ai-tracing) supports exporting agent traces to Braintrust.

<ModuleInstaller languages={["ts"]} packageNames="@mastra/core @mastra/braintrust @ai-sdk/openai" />

```typescript #skip-compile title="mastra-demo.ts"
import { openai } from "@ai-sdk/openai";
import { Agent } from "@mastra/core/agent";
import { Mastra } from "@mastra/core/mastra";
import { BraintrustExporter } from "@mastra/braintrust";

const agent = new Agent({
  name: "Demo Assistant",
  instructions: "You help with geography. Be accurate and helpful.",
  model: openai("gpt-4o-mini"),
});

const mastra = new Mastra({
  agents: { agent },
  observability: {
    configs: {
      braintrust: {
        serviceName: "demo-project",
        exporters: [
          new BraintrustExporter({
            apiKey: process.env.BRAINTRUST_API_KEY,
            endpoint: process.env.BRAINTRUST_API_URL,
            projectName: process.env.PROJECT_NAME,
          }),
        ],
      },
    },
  },
});

async function main() {
  const agent = mastra.getAgent("agent");
  if (!agent) {
    throw new Error("agent not found");
  }

  const response = await agent.generateVNext("What's the capital of France?");
  console.log("response:", response.text);
}

main();
```

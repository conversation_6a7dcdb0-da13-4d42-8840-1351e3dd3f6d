import { type Permission } from "@braintrust/typespecs";
import { ClientPage, type Params } from "./clientpage";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { getOrganization } from "#/app/app/actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { trackServerEvent } from "#/utils/server-analytics";

export default async function Page(props: {
  params: Promise<Params>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  const org_name = decodeURIComponentPatched(params.org);
  const org = await getOrganization({ org_name });

  let orgPermissions: Permission[] = [];

  try {
    const orgPerms = await getObjectAclPermissions({
      objectType: "organization",
      objectId: org?.id,
    });
    orgPermissions = orgPerms ?? [];
  } catch (e) {
    console.error("Failed to get permissions on payment page", e);
  }

  // Use flowId from URL params if available, otherwise generate a new one
  const flowId =
    typeof searchParams.flowId === "string"
      ? searchParams.flowId
      : `checkout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Track server-side checkout start when user lands on payment page
  if (org?.id && org?.name) {
    try {
      await trackServerEvent("checkoutStart", {
        orgId: org.id,
        orgName: org.name,
        flowId,
        sourcePage: "payment",
      });
    } catch (error) {
      console.error("Failed to track server-side checkoutStart event:", error);
    }
  }

  return <ClientPage orgPermissions={orgPermissions} flowId={flowId} />;
}

import { getMetricsLabel } from "./utils";
import { isNumber } from "#/utils/object";
import { getMetricsDataGroups } from "./get-metrics-data-groups";
import type {
  ColorMap,
  GroupBy,
  MetricsData,
  TimeGroups,
  ToolMetric,
} from "./groups.types";
import { NO_GROUP_INTERNAL_NAME } from "./card-config/monitor-cards.constants";

// no group internal processing value - hope to avoid any collisions with customer data
const NO_GROUP_DISPLAY_NAME = "None"; // what we show to customers

export function getSeriesColorMap(allSeries: string[]): ColorMap {
  const map: ColorMap = {};
  allSeries.forEach((series, i) => {
    map[series] = { metadata: i, none: i, score: i, metric: i };
  });
  return map;
}

export function getGroupNames(data: TimeGroups): string[] {
  const groups = new Set<string>();
  Object.values(data).forEach((timeData) => {
    Object.keys(timeData.groups).forEach((group) => groups.add(group));
  });
  return Array.from(groups).sort();
}

const normGroupValue = (groupValue?: string) => {
  if (!groupValue || groupValue === NO_GROUP_INTERNAL_NAME) {
    return NO_GROUP_DISPLAY_NAME;
  }
  return groupValue;
};

const getGroupName = (
  groupKey: string,
  groupKeyToGroupBys?: Map<string, GroupBy[]>,
) => {
  const groupBys = groupKeyToGroupBys?.get(groupKey);
  if (groupBys) {
    return groupBys.map((g) => normGroupValue(g.value)).join(",");
  }
  return normGroupValue(groupKey);
};

export function getGroupedSeriesNames({
  groupNames,
  selectedSeries,
  metricToDisplayName,
  groupKeyToGroupBys,
}: {
  groupNames: string[];
  selectedSeries: string[];
  metricToDisplayName?: Map<string, string>;
  groupKeyToGroupBys?: Map<string, GroupBy[]>;
}) {
  const groupedSeriesNames: string[] = [];
  const groupBys: GroupBy[][] = [];
  groupNames.forEach((groupName) => {
    selectedSeries.forEach((series) => {
      const metric =
        metricToDisplayName?.get(series) ?? getMetricsLabel(series);
      const group = getGroupName(groupName, groupKeyToGroupBys);
      groupedSeriesNames.push(`${metric} (${group})`);
      groupBys.push(groupKeyToGroupBys?.get(groupName) ?? []);
    });
  });
  return { names: groupedSeriesNames, groupBys };
}

export function getDataSeries({
  data,
  selectedSeries,
  toolMetric = "count",
}: {
  data: MetricsData[];
  selectedSeries: string[];
  toolMetric?: ToolMetric;
}) {
  return data.map((d, i) => ({
    x: d.time,
    y: selectedSeries.map((series) => {
      if (d.scores) {
        if (isNumber(d.scores[series]?.avg)) {
          return { value: d.scores[series].avg };
        }
        return null;
      } else if (d.tools) {
        if (isNumber(d.tools[series]?.[toolMetric])) {
          return { value: Number(d.tools[series][toolMetric]) };
        }
        return null;
      } else if (isNumber(d[series])) {
        return { value: Number(d[series]) };
      }

      return null;
    }),
    metadata: {
      time: new Date(d.time).toISOString(),
      count: BigInt(d.count ?? 0),
    },
  }));
}

export function getGroupedDataSeries({
  groupedData,
  groupNames,
  selectedSeries,
  toolMetric = "count",
}: {
  groupedData: TimeGroups;
  groupNames: string[];
  selectedSeries: string[];
  toolMetric?: ToolMetric;
}) {
  return Object.entries(groupedData).map(([time, data]) => {
    let totalGroupCount: bigint = BigInt(0);
    const seriesValues: ({ value: number } | null)[] = [];

    groupNames.forEach((groupName) => {
      const groupData = data.groups[groupName];

      if (groupData) {
        totalGroupCount += groupData.count;

        selectedSeries.forEach((series) => {
          if (groupData.scores) {
            const value = groupData.scores[series];
            seriesValues.push(
              value && isNumber(value.avg) ? { value: value.avg } : null,
            );
          } else if (groupData.tools) {
            const value = groupData.tools[series];
            seriesValues.push(
              value && isNumber(value[toolMetric])
                ? { value: Number(value[toolMetric]) }
                : null,
            );
          } else {
            const value = groupData[series];
            seriesValues.push(
              isNumber(value) ? { value: Number(value) } : null,
            );
          }
        });
      } else {
        selectedSeries.forEach(() => seriesValues.push(null));
      }
    });
    return {
      x: Number(time),
      y: seriesValues,
      metadata: {
        time: new Date(Number(time)).toISOString(),
        count: totalGroupCount,
      },
    };
  });
}

export function isEmptyMetricsData(data: MetricsData): boolean {
  return Object.entries(data).every(([key, value]) => {
    // Ignore time and count fields
    if (key === "time" || key === "count") {
      return true;
    }
    return value === null;
  });
}

export function groupData(data: MetricsData[]): TimeGroups {
  return data.reduce((acc: TimeGroups, item) => {
    const time = item.time;
    if (!acc[time]) {
      acc[time] = { groups: {} };
    }

    // Use NO_GROUP_INTERNAL_NAME for null group
    const groupName = item.group ?? NO_GROUP_INTERNAL_NAME;

    // If the metric is all nulls, don't add it to a group
    // This prevents empty series from being shown
    if (!isEmptyMetricsData(item)) {
      acc[time].groups[groupName] = item;
    }

    return acc;
  }, {});
}

export function binAllGroups(bins: MetricsData[]): {
  timeGroups: TimeGroups;
  groupKeyToGroupBys: Map<string, GroupBy[]>;
} {
  const aggregated: TimeGroups = {};
  const groupKeyToGroupBys: Map<string, GroupBy[]> = new Map();

  bins
    .filter((b) => !isEmptyMetricsData(b))
    .forEach((b) => {
      const { time } = b;
      const timeBin = aggregated[time] ?? { groups: {} };

      const { groupBys, groupKey } = getMetricsDataGroups(b);

      // note there is no protection against dupe group bins yet
      timeBin.groups[groupKey] = b;
      groupKeyToGroupBys.set(groupKey, groupBys);

      aggregated[time] = timeBin;
    });

  return { timeGroups: aggregated, groupKeyToGroupBys };
}

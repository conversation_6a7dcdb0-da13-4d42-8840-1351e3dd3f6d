use std::sync::Arc;

use tracing::instrument;
use util::{anyhow::Result, uuid::Uuid};

use crate::{
    global_locks_manager::GlobalLocksManager,
    global_store::{GlobalStore, LastCompactedIndexMeta, SegmentMetadataUpdate},
    tantivy_index::TantivyIndexScope,
};

pub struct ClearCompactedIndexInput<'a> {
    pub segment_id: Uuid,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: &'a dyn GlobalLocksManager,
}

#[instrument(err, skip(input), fields(segment_id = %input.segment_id))]
pub async fn clear_compacted_index(input: ClearCompactedIndexInput<'_>) -> Result<()> {
    let index_scope = TantivyIndexScope::Segment(input.segment_id);

    let _lock = input.locks_manager.write(&index_scope.lock_name()).await?;

    let current_last_compacted_index_meta = {
        let mut segment_metadatas = input
            .global_store
            .query_segment_metadatas(&[input.segment_id])
            .await?;
        segment_metadatas.remove(0).last_compacted_index_meta
    };

    clear_compacted_index_inner(ClearCompactedIndexInnerInput {
        segment_id: input.segment_id,
        global_store: input.global_store,
        current_last_compacted_index_meta,
    })
    .await
}

pub(crate) struct ClearCompactedIndexInnerInput {
    pub(crate) segment_id: Uuid,
    pub(crate) global_store: Arc<dyn GlobalStore>,
    pub(crate) current_last_compacted_index_meta: Option<LastCompactedIndexMeta>,
}

pub(crate) async fn clear_compacted_index_inner(
    input: ClearCompactedIndexInnerInput,
) -> Result<()> {
    // Procedure:
    //
    // 1. Mark all of the entries in the segment WAL as un-compacted.
    //
    // 2. Clear out any index metadata in the global store. The next time compaction runs, it
    //    should find the empty metadata and clear out any existing index files.
    input
        .global_store
        .update_all_segment_wal_entries_is_compacted_non_atomic(&[input.segment_id], false)
        .await?;

    if input.current_last_compacted_index_meta.is_some() {
        input
            .global_store
            .upsert_segment_metadatas(
                [(
                    input.segment_id,
                    SegmentMetadataUpdate {
                        last_compacted_index_meta: Some((
                            input.current_last_compacted_index_meta,
                            None,
                        )),
                        ..Default::default()
                    },
                )]
                .into_iter()
                .collect(),
            )
            .await?;
    }

    Ok(())
}

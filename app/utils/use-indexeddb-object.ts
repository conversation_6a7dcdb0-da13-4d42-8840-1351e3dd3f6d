import { useCallback, useEffect, useRef, useState } from "react";
import { loadBtCacheDB } from "./btapi/load-bt-cache-db";

const BROADCAST_CHANNEL_TYPE = "indexeddb-updates";

interface UseIndexedDBObjectOptionsWithDefault<T> {
  store: string;
  key?: IDBValidKey;
  defaultValue: T;
}

interface UseIndexedDBObjectOptionsWithoutDefault {
  store: string;
  key?: IDBValidKey;
  defaultValue?: never;
}

type UseIndexedDBObjectOptions<T> =
  | UseIndexedDBObjectOptionsWithDefault<T>
  | UseIndexedDBObjectOptionsWithoutDefault;

export function useIndexedDBObject<T>(
  options: UseIndexedDBObjectOptionsWithDefault<T>,
): [T, (value: T | ((prev: T) => T)) => void, VoidFunction, boolean];

export function useIndexedDBObject<T>(
  options: UseIndexedDBObjectOptionsWithoutDefault,
): [
  T | undefined,
  (value: T | ((prev: T | undefined) => T)) => void,
  VoidFunction,
  boolean,
];

export function useIndexedDBObject<T>({
  store,
  key = "default",
  defaultValue,
}: UseIndexedDBObjectOptions<T>): [
  T | undefined,
  (value: T | ((prev: T | undefined) => T)) => void,
  VoidFunction,
  boolean,
] {
  const [state, setState] = useState<T | undefined>(defaultValue);
  const [isLoaded, setIsLoaded] = useState(false);
  const dbRef = useRef<IDBDatabase | null>(null);
  const broadcastChannelRef = useRef<BroadcastChannel | null>(null);

  const ensureChannel = useCallback(() => {
    if (!broadcastChannelRef.current) {
      broadcastChannelRef.current = new BroadcastChannel("indexeddb-updates");
    }

    return broadcastChannelRef.current;
  }, []);

  useEffect(() => {
    const loadInitialValue = async () => {
      try {
        const db = await loadBtCacheDB();
        dbRef.current = db;

        const transaction = db.transaction([store], "readwrite");
        const objectStore = transaction.objectStore(store);
        const request = objectStore.get(key);

        request.onsuccess = () => {
          const result = request.result;
          if (result !== undefined) {
            setState(result);
          }
          setIsLoaded(true);
        };

        request.onerror = () => {
          console.error("Failed to load from IndexedDB:", request.error);
          setIsLoaded(true);
        };
      } catch (error) {
        console.error("Failed to initialize IndexedDB:", error);
        setIsLoaded(true);
      }
    };

    loadInitialValue();
  }, [store, key]);

  const saveToIndexedDB = useCallback(
    async (newValue: T) => {
      if (!dbRef.current || !isLoaded) return;

      try {
        const transaction = dbRef.current.transaction([store], "readwrite");
        const objectStore = transaction.objectStore(store);
        objectStore.put(newValue, key);
      } catch (error) {
        console.error("Failed to save to IndexedDB:", error);
      }
    },
    [store, key, isLoaded],
  );

  const setValue = useCallback(
    (value: T | ((prev: T | undefined) => T)) => {
      setState((prev) => {
        const newValue =
          typeof value === "function"
            ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              (value as (prev: T | undefined) => T)(prev)
            : value;

        saveToIndexedDB(newValue);

        return newValue;
      });

      // Broadcast the update to other tabs
      ensureChannel().postMessage({
        type: BROADCAST_CHANNEL_TYPE,
        store,
        key,
      });
    },
    [ensureChannel, key, saveToIndexedDB, store],
  );

  const clearValue = useCallback(() => {
    setState(undefined);
    if (dbRef.current && isLoaded) {
      const transaction = dbRef.current.transaction([store], "readwrite");
      const objectStore = transaction.objectStore(store);
      objectStore.delete(key);

      // Broadcast the update to other tabs
      ensureChannel().postMessage({
        type: BROADCAST_CHANNEL_TYPE,
        store,
        key,
      });
    }
  }, [ensureChannel, store, key, isLoaded]);

  // Listen for updates from other tabs
  useEffect(() => {
    const channel = ensureChannel();
    channel.onmessage = (event) => {
      if (
        event.data.type === BROADCAST_CHANNEL_TYPE &&
        event.data.store === store &&
        event.data.key === key &&
        dbRef.current
      ) {
        const transaction = dbRef.current.transaction([store], "readwrite");
        const objectStore = transaction.objectStore(store);
        const request = objectStore.get(key);

        request.onsuccess = () => {
          const result = request.result;
          setState(result);
        };
      }
    };

    return () => {
      channel.onmessage = null;
      channel.close();
      if (broadcastChannelRef.current === channel) {
        broadcastChannelRef.current = null;
      }
    };
  }, [ensureChannel, key, store]);

  return [state, setValue, clearValue, isLoaded];
}

---
title: "Strands Agent SDK"
---

import { CodeT<PERSON>s, PYTab, TSTab } from "#/ui/docs/code-tabs";
import ModuleInstaller from "#/ui/docs/module-installer";

# Strands Agent SDK

The [Strands Agent SDK](https://strandsagents.com/) integration provides automatic tracing and logging of agent executions to Braintrust using OpenTelemetry. It captures agent invocations, tool calls, and multi-step interactions.

The code below adapts the [Weather Forecaster Strands example](https://strandsagents.com/latest/documentation/docs/examples/python/weather_forecaster/) to send traces and logs to Braintrust.

First, install the necessary dependencies and set environment variables.

<ModuleInstaller languages={["py"]} packageNames="braintrust[otel] strands-agents-tools strands-agents[openai]" />

```bash title=".env" #skip-compile
BRAINTRUST_API_KEY="<your Braintrust API key>"
BRAINTRUST_PARENT="project_name:<your Braintrust Project Name>"
OPENAI_API_KEY="<your OpenAI API key>"

# If you are self-hosting Braintrust, set the URL of your hosted dataplane. You can omit this otherwise.
# BRAINTRUST_API_URL=https://api.braintrust.dev
```

Use `BraintrustSpanProcessor` to configure Strands agent traces to send to Braintrust. This example adapts the [Weather Forecaster Strands example](https://strandsagents.com/latest/documentation/docs/examples/python/weather_forecaster/) to send traces and logs to Braintrust.

```python title="trace-strands-agent.py" #skip-compile
# Load packages to form the Strands agent using an OpenAI model
# Load packages to execute the agent
import asyncio
import os

from braintrust.otel import BraintrustSpanProcessor
from dotenv import load_dotenv

# Load OpenTelemetry assets and Braintrust span processor added to the project from the braintrust[otel] library
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from strands import Agent
from strands.models.openai import OpenAIModel
from strands.telemetry import StrandsTelemetry
from strands_tools import http_request

load_dotenv()

# Define a weather-focused system prompt
WEATHER_SYSTEM_PROMPT = """You are a weather assistant with HTTP capabilities. You can:

1. Make HTTP requests to the National Weather Service API
2. Process and display weather forecast data
3. Provide weather information for locations in the United States

When retrieving weather information:
1. First get the coordinates or grid information using https://api.weather.gov/points/{latitude},{longitude} or https://api.weather.gov/points/{zipcode}
2. Then use the returned forecast URL to get the actual forecast

When displaying responses:
- Format weather data in a human-readable way
- Highlight important information like temperature, precipitation, and alerts
- Handle errors appropriately
- Convert technical terms to user-friendly language

Always explain the weather conditions clearly and provide context for the forecast.
"""

# Configure the global OTel tracer provider
provider = TracerProvider()
trace.set_tracer_provider(provider)

# Add the Braintrust span processor to the tracer provider and configure Strands telemetry
provider.add_span_processor(BraintrustSpanProcessor())
telemetry = StrandsTelemetry(provider)

# Configure the OpenAI model to be used by the Strands agent
model = OpenAIModel(
    client_args={
        "api_key": os.getenv("OPENAI_API_KEY"),
    },
    # **model_config
    model_id="gpt-4o-mini",
)

# Create an agent with HTTP tool call capabilities and the OpenAI model
weather_agent = Agent(
    system_prompt=WEATHER_SYSTEM_PROMPT,
    tools=[http_request],  # Explicitly enable http_request tool
    model=model,
)


# Create an async function to run the agent
async def main():
    result = await weather_agent.invoke_async(prompt="What is the weather in San Francisco?")
    print(result)


if __name__ == "__main__":
    asyncio.run(main())
```

![Example of automatic Strands agent tracing and logs sent to Braintrust](./strands-agent.png)

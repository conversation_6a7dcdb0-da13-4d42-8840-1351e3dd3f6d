import { MessageBubble } from "#/ui/LLMView";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  type ChatCompletionMessage,
  useRunPrompt,
  type StreamChunkData,
} from "./use-run-prompt";
import { produce } from "immer";
import { Button } from "#/ui/button";
import {
  ArrowUpRight,
  CircleArrowUp,
  CurlyBraces,
  Equal,
  MessageSquare,
  MessagesSquare,
  MoreHorizontal,
  X,
} from "lucide-react";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import useEvent from "react-use-event-hook";
import Text<PERSON>rea from "#/ui/text-area";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { DataTextEditor } from "#/ui/data-text-editor";
import { getObjValueByPath, isObject } from "braintrust/util";
import { cn } from "#/utils/classnames";
import { usePromptVariables } from "#/ui/prompts/function-editor/use-prompt-variables";
import { unfoldNestedFields } from "#/utils/queries/metadata";
import {
  ExtraMessageEditor,
  type ExtraMessagesFormValues,
  extraMessagesSchema,
} from "#/app/app/[org]/p/[project]/prompts/extra-messages";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "#/ui/form";
import { InfoBanner } from "#/ui/info-banner";
import { chatCompletionMessageParamSchema } from "@braintrust/typespecs";
import z from "zod";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

type Props = {
  id: string;
  runData: RunData;
  setRunData: Dispatch<SetStateAction<RunData>>;
  extraMessagesPath: string | undefined;
  setExtraMessagesPath: Dispatch<SetStateAction<string | undefined>>;
};
export type RunData = {
  input?: unknown;
  expected?: unknown;
  metadata?: Record<string, unknown>;
};

export function PromptChat({
  id,
  runData,
  setRunData,
  extraMessagesPath,
  setExtraMessagesPath,
}: Props) {
  const { projectId } = useContext(ProjectContext);

  const { sortedSyncedPromptsAtom_ROOT, addMessage, appendMessages } =
    useSyncedPrompts();
  const promptData = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0].prompt_data),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const [messages, setMessages] = useState<ChatCompletionMessage[]>([]);
  /** Hide tool messages, which are necessary for the LLM to continue conversation, but which we instead display inline in the assistant message block */
  const displayMessages = useMemo(
    () => messages.filter((m) => m.role !== "tool"),
    [messages],
  );
  const [toolDefinitions, setToolDefinitions] = useState<Map<string, string>>(
    new Map(),
  );
  const [pendingTools, setPendingTools] = useState<Set<string>>(new Set());
  const [toolResults, setToolResults] = useState<Map<string, unknown>>(
    new Map(),
  );
  const [input, setInput] = useState("");
  const [shouldAutoClear, setShouldAutoClear] = useState(false);
  const [variablesPanelOpen, setVariablesPanelOpen] = useState(false);
  const [scrollFromBottom, setScrollFromBottom] = useState<number | null>(null);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const extraMessagesParsed = useMemo(() => {
    if (!extraMessagesPath) return { messages: undefined, error: undefined };
    const value = getObjValueByPath(runData, extraMessagesPath.split("."));
    const messages = z.array(chatCompletionMessageParamSchema).safeParse(value);
    return { messages: messages.data, error: messages.error };
  }, [runData, extraMessagesPath]);

  const onStreamChunk = useEvent((data: StreamChunkData) => {
    messagesContainerRef.current?.scrollTo({
      top: messagesContainerRef.current?.scrollHeight,
      behavior: "smooth",
    });

    setPendingTools(data.pendingTools);
    setToolResults((prev) => new Map([...prev, ...data.toolResults]));

    const toolCalls =
      data.toolCalls?.map((toolCall) => ({
        id: toolCall.id,
        type: "function" as const,
        function: {
          name: toolCall.name,
          arguments: JSON.stringify(toolCall.args),
        },
      })) ?? [];

    setMessages(
      produce(messages, (draft) => {
        // If we are displaying a loading assistant message and have not received text chunks yet, don't remove the loading message.
        if (data.textChunks.length === 0) {
          const lastMessage = draft.at(-1);
          if (
            lastMessage &&
            lastMessage.role === "assistant" &&
            lastMessage.content === "" &&
            toolCalls.length > 0
          ) {
            lastMessage.tool_calls = toolCalls;
          }
          return;
        }

        // Remove all trailing assistant messages
        while (
          draft.length > 0 &&
          draft[draft.length - 1].role === "assistant"
        ) {
          draft.pop();
        }

        data.textChunks.forEach((content, index) => {
          const message: ChatCompletionMessage = {
            content,
            role: "assistant" as const,
          };

          if (index === 0 && toolCalls.length > 0) {
            message.tool_calls = toolCalls;
          }

          draft.push(message);
        });
      }),
    );
  });

  const onStreamDone = useEvent((newMessages: ChatCompletionMessage[]) => {
    setMessages(
      produce(messages, (draft) => {
        // Remove all trailing assistant messages
        while (
          draft.length > 0 &&
          draft[draft.length - 1].role === "assistant"
        ) {
          draft.pop();
        }
        // Add final messages
        draft.push(...newMessages);
      }),
    );
    setPendingTools(new Set());
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  });

  const onError = useCallback(() => {
    // Remove loading assistant message
    setMessages((prev) =>
      prev.filter((m) => !(m.role === "assistant" && m.content === "")),
    );
    setPendingTools(new Set());
  }, []);

  const { runPrompt, isGenerating } = useRunPrompt({
    promptData,
    runData,
    onStreamChunk,
    onStreamDone,
    onError,
    setToolDefinitions,
    extraMessages: extraMessagesParsed.messages,
    parent: useMemo(
      () =>
        projectId
          ? {
              object_type: "project_logs",
              object_id: projectId,
            }
          : undefined,
      [projectId],
    ),
  });

  const { isMissingPromptVariables, addPromptVariables } = usePromptVariables({
    promptContent: promptData?.prompt,
    runData,
    setRunData,
    shouldPrependInput: true,
  });

  // Restore scroll position when variables panel toggles
  useEffect(() => {
    if (scrollFromBottom !== null && messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      // Wait for layout to settle
      setTimeout(() => {
        const newScrollTop =
          container.scrollHeight - container.clientHeight - scrollFromBottom;
        container.scrollTop = Math.max(0, newScrollTop);
        setScrollFromBottom(null);
      }, 0);
    }
  }, [variablesPanelOpen, scrollFromBottom]);

  const onSubmit = () => {
    const newMessages = shouldAutoClear ? [] : [...messages];
    // Allow submitting without a user message
    if (input.trim().length > 0) {
      newMessages.push({
        content: input,
        role: "user" as const,
      });
    }

    // Don't include the loading assistant message in the submitted prompt
    runPrompt(newMessages);

    newMessages.push({
      content: "",
      role: "assistant" as const,
    });

    setMessages(newMessages);
    setInput("");
    setPendingTools(new Set());

    // Delay scroll so that new messages are added first
    setTimeout(() => {
      messagesContainerRef.current?.scrollTo({
        top: messagesContainerRef.current?.scrollHeight,
        behavior: "smooth",
      });
    }, 100);
  };

  return (
    <div className="flex min-h-0 flex-1 flex-col items-center">
      <div
        className="flex size-full flex-auto flex-col items-center gap-1 overflow-y-auto px-3 py-4"
        ref={messagesContainerRef}
      >
        <div className="w-full max-w-3xl">
          {extraMessagesParsed.messages &&
            extraMessagesParsed.messages.map((message, index) => (
              <MessageBubble
                key={index}
                message={message}
                toolDefinitions={toolDefinitions}
                extraMessagesPath={extraMessagesPath}
              />
            ))}
          {displayMessages.map((message, index) => (
            <MessageBubble
              key={index}
              message={message}
              toolDefinitions={toolDefinitions}
              toolResponses={toolResults}
              isLoading={
                isGenerating &&
                message.role === "assistant" &&
                message.content === ""
              }
              pendingTools={
                isGenerating &&
                message.role === "assistant" &&
                index === displayMessages.length - 1
                  ? pendingTools
                  : undefined
              }
              onDelete={
                isGenerating
                  ? undefined
                  : () => {
                      const indexInFullMessages = messages.findIndex(
                        (msg) => msg === message,
                      );
                      setMessages(
                        produce(messages, (draft) => {
                          draft.splice(indexInFullMessages, 1);

                          // When deleting an assistant message with tool calls, also remove the corresponding tool messages
                          if (
                            message.role === "assistant" &&
                            message.tool_calls
                          ) {
                            const toolCallIds = new Set(
                              message.tool_calls.map((tc) => tc.id),
                            );

                            for (
                              let i = draft.length - 1;
                              i >= 0 && toolCallIds.size > 0;
                              i--
                            ) {
                              const msg = draft[i];
                              if (
                                msg.role === "tool" &&
                                msg.tool_call_id &&
                                toolCallIds.has(msg.tool_call_id)
                              ) {
                                toolCallIds.delete(msg.tool_call_id);
                                draft.splice(i, 1);
                              }
                            }
                          }
                        }),
                      );
                    }
              }
              onAdd={
                isGenerating
                  ? undefined
                  : () => {
                      // We hide the tool messages and only show the results in the assistant tool block.
                      // When adding an assistant message with tool calls, add the corresponding tool messages as well.
                      if (message.role === "assistant") {
                        const relevantToolMessages =
                          message.tool_calls
                            ?.map((toolCall) =>
                              messages.find(
                                (message) =>
                                  message.role === "tool" &&
                                  message.tool_call_id === toolCall.id,
                              ),
                            )
                            .filter(Boolean) ?? [];
                        appendMessages({
                          id,
                          // @ts-expect-error - Typescript doesn't know that .filter(Boolean) removes the undefined values
                          messages: [message, ...relevantToolMessages],
                        });
                      } else {
                        addMessage({ id, message });
                      }
                    }
              }
            />
          ))}
        </div>
        {messages.length === 0 && (
          <div className="flex size-full flex-col items-center justify-center gap-4 text-primary-200">
            <MessagesSquare className="size-8" />
          </div>
        )}
      </div>
      <div className="@container flex w-full max-w-4xl flex-1 justify-center">
        <div
          className={cn(
            "flex w-full flex-col overflow-y-auto border-t bg-background placeholder:text-primary-500/90 @4xl:rounded-t-md @4xl:border dark:bg-primary-50",
            !variablesPanelOpen && "max-h-48",
          )}
        >
          <TextArea
            className="flex min-h-9 resize-none rounded-none border-0 bg-background px-3 focus-visible:ring-0 dark:bg-primary-50"
            value={input}
            minRows={1}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Chat with your prompt..."
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey && !isGenerating) {
                e.preventDefault();
                onSubmit();
              }
            }}
            ref={inputRef}
            disabled={isGenerating}
          />
          <div className="flex items-center justify-between pr-3 pb-2 pl-1.5">
            <div className="flex items-center">
              <Button
                size="xs"
                variant="ghost"
                isDropdown={!variablesPanelOpen}
                onClick={() => {
                  const container = messagesContainerRef.current;
                  // Store scroll position before toggling
                  if (container) {
                    const currentScrollFromBottom =
                      container.scrollHeight -
                      container.scrollTop -
                      container.clientHeight;
                    setScrollFromBottom(currentScrollFromBottom);
                  }

                  setVariablesPanelOpen((prev) => !prev);
                  if (isMissingPromptVariables) {
                    addPromptVariables();
                  }

                  if (variablesPanelOpen) {
                    inputRef.current?.focus();
                  }
                }}
                className={cn("mr-1 text-primary-500")}
              >
                {variablesPanelOpen ? "Hide variables" : "Variables"}
              </Button>
              {messages.length > 0 && !shouldAutoClear && (
                <Button
                  size="xs"
                  variant="ghost"
                  onClick={() => {
                    setMessages([]);
                    inputRef.current?.focus();
                  }}
                  className="text-primary-500"
                  Icon={X}
                >
                  Clear chat
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="xs" variant="ghost" Icon={MoreHorizontal} />
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuCheckboxItem
                    checked={shouldAutoClear}
                    onSelect={() => {
                      setShouldAutoClear((prev) => !prev);
                      inputRef.current?.focus();
                    }}
                  >
                    Auto-clear chat on each run
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="size-7"
              onClick={onSubmit}
              disabled={isGenerating}
              Icon={CircleArrowUp}
              iconClassName="size-4"
            />
          </div>
          {variablesPanelOpen && (
            <VariablesPanel
              setRunData={setRunData}
              runData={runData}
              id={id}
              extraMessagesPath={extraMessagesPath}
              setExtraMessagesPath={setExtraMessagesPath}
            />
          )}
        </div>
      </div>
    </div>
  );
}

function validateMetadataValue(v: unknown) {
  if (v != null && !isObject(v)) {
    throw new Error("Metadata must be an object");
  }
}

const ALLOWED_RENDER_OPTIONS_STRUCTURED = ["json" as const, "yaml" as const];
const ALLOWED_RENDER_OPTIONS_UNKNOWN = [
  ...ALLOWED_RENDER_OPTIONS_STRUCTURED,
  "text" as const,
];

function VariablesPanel({
  id,
  setRunData,
  runData,
  extraMessagesPath,
  setExtraMessagesPath,
}: {
  setRunData: Dispatch<SetStateAction<RunData>>;
  runData: RunData;
  id: string;
  extraMessagesPath: string | undefined;
  setExtraMessagesPath: (extraMessagesPath: string | undefined) => void;
}) {
  const form = useForm<ExtraMessagesFormValues>({
    resolver: zodResolver(extraMessagesSchema),
    defaultValues: {
      expression: extraMessagesPath,
    },
  });

  const expr = form.watch("expression");
  const hasErrors = Object.keys(form.formState.errors).length > 0;
  useEffect(() => {
    if (hasErrors) {
      return;
    }

    // Debounce state update to prevent infinite re-renders when typing quickly
    const timeoutId = setTimeout(() => {
      setExtraMessagesPath(expr);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [hasErrors, setExtraMessagesPath, expr]);

  const datasetPaths = useMemo(() => {
    const inferredPaths = unfoldNestedFields([], runData, false).map((path) =>
      path.join("."),
    );
    // Ensure that inferred paths tab is always shown with the top level fields
    return Array.from(
      new Set([...inferredPaths, "metadata", "expected", "input"]),
    );
  }, [runData]);

  return (
    <div className="flex max-h-[calc(100vh-228px)] flex-col gap-2 overflow-y-scroll p-3 pt-0">
      <div>
        <CollapsibleSection
          title="Input"
          Icon={ArrowUpRight}
          localStorageKey={`${id}-runDataInput`}
        >
          <DataTextEditor
            value={runData.input ?? ""}
            onChange={(v) =>
              setRunData(
                produce(runData, (draft) => {
                  draft.input = v;
                }),
              )
            }
            autoFocus
            formatOnBlur
            allowedRenderOptions={ALLOWED_RENDER_OPTIONS_UNKNOWN}
          />
        </CollapsibleSection>
      </div>
      <div>
        <CollapsibleSection
          title="Expected"
          Icon={Equal}
          localStorageKey={`${id}-runDataExpected`}
        >
          <DataTextEditor
            value={runData.expected ?? ""}
            onChange={(v) =>
              setRunData(
                produce(runData, (draft) => {
                  draft.expected = v;
                }),
              )
            }
            formatOnBlur
            allowedRenderOptions={ALLOWED_RENDER_OPTIONS_UNKNOWN}
          />
        </CollapsibleSection>
      </div>
      <div>
        <CollapsibleSection
          title="Metadata"
          Icon={CurlyBraces}
          localStorageKey={`${id}-runDataMetadata`}
        >
          <DataTextEditor
            value={runData.metadata}
            onChange={(v) =>
              setRunData(
                produce(runData, (draft) => {
                  if (!isObject(v)) {
                    return;
                  }

                  draft.metadata = v;
                }),
              )
            }
            formatOnBlur
            validateFn={validateMetadataValue}
            allowedRenderOptions={ALLOWED_RENDER_OPTIONS_STRUCTURED}
          />
        </CollapsibleSection>
      </div>
      <div>
        <CollapsibleSection
          title="Extra messages"
          Icon={MessageSquare}
          localStorageKey={`${id}-extraMessages`}
          defaultCollapsed
        >
          <Form {...form}>
            <div className="rounded-md border bg-primary-50 px-2">
              <InfoBanner>
                Define a path from variables to messages. If an array of
                messages is found, they will be appended to the prompt.
              </InfoBanner>
              <ExtraMessageEditor
                currentExpression={extraMessagesPath}
                form={form}
                datasetPaths={datasetPaths}
              />
            </div>
          </Form>
        </CollapsibleSection>
      </div>
    </div>
  );
}

import { z } from "zod";
import { type Tool } from "./types";
import { PromptData } from "@braintrust/typespecs";

export const getAvailableScorersParamsSchema = z.object({
  id: z
    .string()
    .optional()
    .describe(
      "Leave empty to get an overview of available scorers. Provide id to get full details such as code or prompt of a specific scorer.",
    ),
});
export type GetAvailableScorersParams = z.infer<
  typeof getAvailableScorersParamsSchema
>;

export const getAvailableScorersResultSchema = z.object({
  id: z.string().describe("The id of the scorer."),
  name: z.string().describe("The name of the scorer."),
  description: z.string().describe("The description of the scorer."),
  slug: z.string().nullable().optional().describe("The slug of the scorer."),
  type: z.string().optional().describe("The type of the scorer."),
  code: z
    .string()
    .optional()
    .describe("The code of the scorer (for code-based scorers)."),
  runtime: z
    .string()
    .optional()
    .describe(
      "The runtime context (python/typescript) for code-based scorers.",
    ),
  prompt: z
    .custom<PromptData>()
    .optional()
    .describe("The prompt data for LLM as a judge scorers."),
});
export type GetAvailableScorersResult = z.infer<
  typeof getAvailableScorersResultSchema
>;

export const GetAvailableScorersTool: Tool<
  GetAvailableScorersParams,
  GetAvailableScorersResult[]
> = {
  description:
    "Get a list of scorers that could be added or removed from the eval using the edit_scorers tool or edited with the create_code_scorer or create_llm_scorer tools. This is helpful when you want to add or remove a scorer from the eval. You can also get the full details such as code or prompt of a specific scorer by providing the id of the scorer.",
  parameters: getAvailableScorersParamsSchema,
};

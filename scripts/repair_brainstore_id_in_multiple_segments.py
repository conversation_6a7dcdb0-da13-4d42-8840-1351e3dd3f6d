#! /usr/bin/env python3
# /// script
# requires-python = ">=3.10"
# dependencies = [
#   "psycopg2-binary",
# ]
# ///
#
import argparse

import psycopg2


def fix_row_id(cursor, id_name, id_to_fix):
    print(f"Checking {id_name}:", id_to_fix)

    cursor.execute(
        f"""
        SELECT brainstore_global_store_{id_name}_to_segment_id.segment_id
        FROM
            brainstore_global_store_{id_name}_to_segment_id
            JOIN brainstore_global_store_segment_id_to_liveness
                ON brainstore_global_store_{id_name}_to_segment_id.segment_id = brainstore_global_store_segment_id_to_liveness.segment_id
                AND brainstore_global_store_segment_id_to_liveness.is_live
        WHERE brainstore_global_store_{id_name}_to_segment_id.{id_name} = %s
    """,
        (id_to_fix,),
    )
    segment_ids = [x[0] for x in cursor.fetchall()]
    if len(segment_ids) <= 1:
        print(f"id_to_fix {id_to_fix} belongs to {len(segment_ids)} segments. It should be fine. Skipping")
        return "skipped_fine"
    elif len(segment_ids) > 2:
        print(f"id_to_fix {id_to_fix} belongs to {len(segment_ids)} segments. Must resolve manually. Skipping")
        return "skipped_notfine"

    print("Fetching the full set of rows in each segment")
    cursor.execute(
        f"""
        SELECT
            brainstore_global_store_{id_name}_to_segment_id.segment_id,
            brainstore_global_store_{id_name}_to_segment_id.{id_name}
        FROM brainstore_global_store_{id_name}_to_segment_id
            JOIN brainstore_global_store_segment_id_to_liveness
                ON brainstore_global_store_{id_name}_to_segment_id.segment_id = brainstore_global_store_segment_id_to_liveness.segment_id
                AND brainstore_global_store_segment_id_to_liveness.is_live
        WHERE brainstore_global_store_{id_name}_to_segment_id.segment_id = ANY(%s::uuid[])
    """,
        (segment_ids,),
    )
    all_ids_to_fix = set()
    segment_id_to_ids_to_fix = {}
    for row in cursor.fetchall():
        segment_id, id_to_fix = row
        all_ids_to_fix.add(id_to_fix)
        if segment_id not in segment_id_to_ids_to_fix:
            segment_id_to_ids_to_fix[segment_id] = set()
        segment_id_to_ids_to_fix[segment_id].add(id_to_fix)
    for segment_id in segment_ids:
        if segment_id_to_ids_to_fix[segment_id] != all_ids_to_fix:
            print(f"segment_id {segment_id} does not contain all ids_to_fix. Must resolve manually. Skipping")
            return "skipped_notfine"

    print("The segments are identical. Fetching the segment metadatas of these segments")
    cursor.execute(
        f"""
        SELECT segment_id, is_live, last_written_ts
        FROM brainstore_global_store_segment_id_to_liveness
        WHERE segment_id = ANY(%s::uuid[]) and is_live
        ORDER BY last_written_ts ASC
    """,
        (segment_ids,),
    )
    all_segment_ids = [(row[0], row[1], row[2]) for row in cursor.fetchall()]
    if set(x[0] for x in all_segment_ids) != set(segment_ids):
        print("The set of live segments may have changed. Skipping")
        return "skipped_notfine"
    print(
        f"Planning to mark segments {[x[0] for x in all_segment_ids[:-1]]} as non-live, in favor of {all_segment_ids[-1][0]}"
    )
    for segment_id, is_live, last_written_ts in all_segment_ids[:-1]:
        print(
            f"Duplicate segment_id {segment_id}, is_live {is_live}, last_written_ts {last_written_ts}. Hit enter to go ahead and mark it non-live"
        )
        input()
        cursor.execute(
            f"""
            UPDATE brainstore_global_store_segment_id_to_liveness
            SET is_live = false
            WHERE segment_id = %s
            """,
            (segment_id,),
        )
    return "fixed"


def main(args):
    ids_to_fix = []
    for line in open(args.ids_to_fix_file):
        id_to_fix = line.strip()
        if args.file_type == "root_span_id":
            id_to_fix = id_to_fix.split(":")[-1]
        ids_to_fix.append(id_to_fix)
    id_name = args.file_type

    num_fixed = 0
    num_skipped_fine = 0
    num_skipped_notfine = 0
    for id_to_fix in ids_to_fix:
        with psycopg2.connect(args.pg_url) as conn:
            with conn.cursor() as cursor:
                result = fix_row_id(cursor, id_name, id_to_fix)
                if result == "fixed":
                    num_fixed += 1
                elif result == "skipped_fine":
                    num_skipped_fine += 1
                elif result == "skipped_notfine":
                    num_skipped_notfine += 1
                else:
                    raise Exception(f"Unknown result {result}")
    print(f"Fixed {num_fixed} ids_to_fix")
    print(f"Skipped {num_skipped_fine} ids_to_fix that were fine")
    print(f"Skipped {num_skipped_notfine} ids_to_fix that were not fine")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Fix row_ids/root_span_ids that belong to multiple segments",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument("--ids-to-fix-file", type=str, required=True, help="File containing row IDs to fix.")
    parser.add_argument("--pg-url", type=str, required=True, help="PG URL")
    parser.add_argument(
        "--file-type", type=str, choices=["row_id", "root_span_id"], required=True, help="row ids or root span ids"
    )

    args = parser.parse_args()
    main(args)

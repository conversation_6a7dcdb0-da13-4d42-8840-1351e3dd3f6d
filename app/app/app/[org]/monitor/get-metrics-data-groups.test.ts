import { describe, expect, test } from "vitest";

import {
  CARD_GROUP_BY_PREFIX,
  NO_GROUP_INTERNAL_NAME,
} from "./card-config/monitor-cards.constants";
import { getMetricsDataGroups } from "./get-metrics-data-groups";

const prefixed = (groupName: string) => `${CARD_GROUP_BY_PREFIX}${groupName}`;

const baseEpoch = 1731571200000;
const ITEM_BASE = {
  time: baseEpoch,
  count: BigInt(1),
};

describe("groups", () => {
  test("no group case", () => {
    // empty case
    expect(getMetricsDataGroups(ITEM_BASE)).toEqual({
      groupBys: [],
      groupKey: NO_GROUP_INTERNAL_NAME,
    });
  });
  test("single group cases", () => {
    // simple single case
    expect(
      getMetricsDataGroups({
        ...ITEM_BASE,
        [prefixed("fruit")]: "apple",
      }),
    ).toEqual({
      groupBys: [{ key: "fruit", value: "apple" }],
      groupKey: "fruit:apple",
    });
    expect(
      getMetricsDataGroups({
        ...ITEM_BASE,
        [prefixed("fruit")]: null,
      }),
    ).toEqual({
      groupBys: [{ key: "fruit", value: NO_GROUP_INTERNAL_NAME }],
      groupKey: `fruit:${NO_GROUP_INTERNAL_NAME}`,
    });
  });
  test("two group case", () => {
    expect(
      getMetricsDataGroups({
        ...ITEM_BASE,
        [prefixed("fruit")]: "apple",
        [prefixed("animal")]: "dog",
      }),
    ).toEqual({
      groupBys: [
        { key: "animal", value: "dog" },
        { key: "fruit", value: "apple" },
      ],
      groupKey: "animal:dog,fruit:apple",
    });

    // order independent
    expect(
      getMetricsDataGroups({
        ...ITEM_BASE,
        [prefixed("animal")]: "dog",
        [prefixed("fruit")]: "apple",
      }),
    ).toEqual({
      groupBys: [
        { key: "animal", value: "dog" },
        { key: "fruit", value: "apple" },
      ],
      groupKey: "animal:dog,fruit:apple",
    });
  });

  test("many groups case", () => {
    expect(
      getMetricsDataGroups({
        ...ITEM_BASE,
        [prefixed("key1")]: "one",
        [prefixed("key8")]: "eight",
        [prefixed("key2")]: "two",
        [prefixed("key4")]: "four",
        [prefixed("key9")]: "nine",
        [prefixed("key0")]: "zero",
        [prefixed("key3")]: "three",
      }),
    ).toEqual({
      groupBys: [
        { key: "key0", value: "zero" },
        { key: "key1", value: "one" },
        { key: "key2", value: "two" },
        { key: "key3", value: "three" },
        { key: "key4", value: "four" },
        { key: "key8", value: "eight" },
        { key: "key9", value: "nine" },
      ],
      groupKey:
        "key0:zero,key1:one,key2:two,key3:three,key4:four,key8:eight,key9:nine",
    });
  });
});

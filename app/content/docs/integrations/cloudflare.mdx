---
title: "Cloudflare"
description: "Trace AI requests through Cloudflare AI Gateway with Braintrust"
---

import ModuleInstaller from "#/ui/docs/module-installer";
import { CodeTabs, TSTab, PYTab } from "#/ui/docs/code-tabs";
import { Callout } from "fumadocs-ui/components/callout";

# Cloudflare

[Cloudflare AI Gateway](https://developers.cloudflare.com/ai-gateway/) provides a unified interface to access multiple AI providers with enhanced observability, caching, and rate limiting. Braintrust automatically traces requests made through Cloudflare AI Gateway, giving you comprehensive observability for your AI applications.

Install the `braintrust` and `openai` packages.

<ModuleInstaller packageNames="braintrust openai" />

Use `wrapOpenAI` to automatically trace requests made through Cloudflare AI Gateway. Configure your client to use Cloudflare's unified API endpoint, and Braintrust will capture all requests, responses, and metrics.

```bash title=".env"
OPENAI_API_KEY=<your-provider-api-key>
CLOUDFLARE_ACCOUNT_ID=<your-cloudflare-account-id>
CLOUDFLARE_AI_GATEWAY_NAME=<your-gateway-name>
BRAINTRUST_API_KEY=<your-braintrust-api-key>
```

<CodeTabs>
<TSTab>
```typescript title="cloudflare-tracing.ts" #skip-compile
import { OpenAI } from "openai";
import { initLogger, wrapOpenAI } from "braintrust";

// Initialize Braintrust logging
initLogger({
  projectName: "My Project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

// Create OpenAI client configured for Cloudflare AI Gateway
const client = wrapOpenAI(
  new OpenAI({
    // OpenAI SDK automatically adds /chat/completions
    baseURL: `https://gateway.ai.cloudflare.com/v1/${process.env.CLOUDFLARE_ACCOUNT_ID}/${process.env.CLOUDFLARE_AI_GATEWAY_NAME}/compat`,
    apiKey: process.env.OPENAI_API_KEY,
  }),
);

// This request will be automatically traced by Braintrust
const result = await client.chat.completions.create({
  model: "openai/gpt-4o",
  messages: [{ role: "user", content: "What is 1+1?" }],
});

console.log(result);
```
</TSTab>
<PYTab>
```python title="cloudflare-tracing.py" #skip-compile
import os

import openai
from braintrust import init_logger, wrap_openai

# Initialize Braintrust logging
logger = init_logger(project="My Project")

# Create OpenAI client configured for Cloudflare AI Gateway
client = wrap_openai(
    # OpenAI client automatically adds /chat/completions
    openai.OpenAI(
        base_url=f"https://gateway.ai.cloudflare.com/v1/{os.getenv('CLOUDFLARE_ACCOUNT_ID')}/{os.getenv('CLOUDFLARE_AI_GATEWAY_NAME')}/compat",
        api_key=os.getenv("OPENAI_API_KEY"),
    )
)

# This request will be automatically traced by Braintrust
result = client.chat.completions.create(
    model="openai/gpt-4o",
    messages=[{"role": "user", "content": "What is 1+1?"}],
)

print(result)
```
</PYTab>
</CodeTabs>

## Switching providers

Cloudflare AI Gateway's unified API allows you to easily switch between different AI providers by changing the `model` parameter and corresponding API key. All requests are automatically traced by Braintrust regardless of which provider you use.

<CodeTabs>
<TSTab>
```typescript #skip-compile
// Switch to Anthropic
const client = wrapOpenAI(
  new OpenAI({
    baseURL: `https://gateway.ai.cloudflare.com/v1/${process.env.CLOUDFLARE_ACCOUNT_ID}/${process.env.CLOUDFLARE_AI_GATEWAY_NAME}/compat`,
    apiKey: process.env.ANTHROPIC_API_KEY, // Use Anthropic's API key
  }),
);

const result = await client.chat.completions.create({
  model: "anthropic/claude-3-5-sonnet-********", // Use Anthropic model
  messages: [{ role: "user", content: "Hello!" }],
});
```
</TSTab>
<PYTab>
```python #skip-compile
# Switch to Anthropic
client = wrap_openai(
    openai.OpenAI(
        base_url=f"https://gateway.ai.cloudflare.com/v1/{os.getenv('CLOUDFLARE_ACCOUNT_ID')}/{os.getenv('CLOUDFLARE_AI_GATEWAY_NAME')}/compat",
        api_key=os.getenv("ANTHROPIC_API_KEY"),  # Use Anthropic's API key
    )
)

result = client.chat.completions.create(
    model="anthropic/claude-3-5-sonnet-********",  # Use Anthropic model
    messages=[{"role": "user", "content": "Hello!"}],
)
```
</PYTab>
</CodeTabs>

Supported providers include OpenAI, Anthropic, Google AI Studio, Groq, Mistral, Cohere, Perplexity, DeepSeek, Cerebras, xAI, and Workers AI. See the [Cloudflare documentation](https://developers.cloudflare.com/ai-gateway/usage/chat-completion/) for the complete list.

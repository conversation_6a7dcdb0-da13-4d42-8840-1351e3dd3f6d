Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      - Label:
          default: "General configuration"
        Parameters:
          - OrgName

      - Label:
          default: "Database configuration"
        Parameters:
          - ManagedPostgres
          - EncryptDatabase
          - PostgresInstanceType
          - PostgresStorageType
          - PostgresStorageSize
          - PostgresMaxStorageSize
          - PostgresVersion
          - RunDraftMigrations

      - Label:
          default: "Brainstore configuration"
        Parameters:
          - EnableBrainstore
          - BrainstoreDefault
          - BrainstoreLicenseKey
          - BrainstoreInstanceType
          - BrainstoreAutoscalingMinInstanceCount
          - BrainstoreAutoscalingMaxInstanceCount
          - BrainstoreAutoscalingCpuTargetValue
          - BrainstoreWriterInstanceType
          - BrainstoreWriterAutoscalingMinInstanceCount
          - BrainstoreWriterAutoscalingMaxInstanceCount
          - BrainstoreWriterAutoscalingCpuTargetValue
          - BrainstoreInstanceKeyPairName
          - BrainstorePort
          - BrainstoreVersionOverride
          - BrainstoreBackfillDisableLogs
          - BrainstoreBackfillHistoricalBatchSize
          - BrainstoreEnableRetention
          - BrainstoreRetentionIntervalSeconds
          - BrainstoreVacuumCleanupDelaySeconds
          - BrainstoreEnableObjectStoreLocksManager
          - BrainstoreVersioningExpirationDays
          - BrainstoreCacheFileSize

      - Label:
          default: "Clickhouse configuration"
        Parameters:
          - ManagedClickhouse
          - ClickhouseInstanceKeyPairName
          - ClickhouseBucketGlobalName
          - ClickhouseLinuxAMI
          - ClickhouseInstanceType
          - ClickhouseMetadataStorageSize
          - ClickhouseVersion
          - ClickhouseRelaxedSearchMode

      - Label:
          default: "Advanced: Networking"
        Parameters:
          - VPCCIDR
          - PrivateSubnet1AZ
          - PrivateSubnet1CIDR
          - PrivateSubnet2AZ
          - PrivateSubnet2CIDR
          - PrivateSubnet3AZ
          - PrivateSubnet3CIDR
          - PublicSubnet1AZ
          - PublicSubnet1CIDR

      - Label:
          default: "Advanced: Quarantine VPC"
        Parameters:
          - EnableQuarantine
          - QuarantineVPCCIDR
          - QuarantinePrivateSubnet1AZ
          - QuarantinePrivateSubnet1CIDR
          - QuarantinePrivateSubnet2AZ
          - QuarantinePrivateSubnet2CIDR
          - QuarantinePrivateSubnet3AZ
          - QuarantinePrivateSubnet3CIDR
          - QuarantinePublicSubnet1AZ
          - QuarantinePublicSubnet1CIDR

      - Label:
          default: "Advanced: Service configuration"
        Parameters:
          - APIHandlerMemorySize
          - ProvisionedConcurrency
          - WhitelistedOrigins
          - OutboundRateLimitWindowMinutes
          - OutboundRateLimitMaxRequests
          - RateLimitApiLogsOrg
          - RateLimitApiLogsOrgWindowSecs
          - RateLimitApiLogsOrgEnforce
          - UseGlobalProxy
          - CustomCertificateArn
          - CustomDomain
          - EnableBillingTelemetry
          - DisableBillingTelemetryAggregation
          - PrimaryOrgName

      - Label:
          default: "Advanced: Braintrust remote monitoring"
        Parameters:
          - EnableBraintrustSupportLogsAccess
          - EnableBraintrustSupportShellAccess
          - DisableSysadminTelemetry
          - InternalObservabilityApiKey
          - InternalObservabilityRegion
          - InternalObservabilityEnvName
          - LogLevel
          - BillingTelemetryLogLevel
          - MonitoringTelemetry
          - APISegmentWriteKey

      - Label:
          default: "External datastores (do not use without consulting support)"
        Parameters:
          - PostgresAlternativeHost
          - RedisAlternativeUrl

      - Label:
          default: "Advanced: Performance testing (do not use without consulting support)"
        Parameters:
          - BrainstoreDisableEtlLoop
          - BrainstoreDisableBackfillCompaction
          - BrainstoreDisableOptimizationWorker
          - BrainstoreInsertRowRefs
          - InsertLogs2
          - BrainstoreExtraEnvVars
          - DisableAsyncScoring
          - DisableAsyncScoringObjectIds
          - MaxLimitForQueries

      - Label:
          default: "Internal: Braintrust hosted data plane (do not use for self-hosting)"
        Parameters:
          - InternalOnlyBraintrustHostedDataPlane

Parameters:
  OrgName:
    Type: String
    Description: Enter your organization's name (e.g. acme.com)
  PrimaryOrgName:
    Type: String
    Description:
      Enter your primary organization's name. This is only required if you intend have multiple organizations
      on your data plane. Owners in this organization will have special permissions to manage data plane internals.
    Default: ""
  ManagedPostgres:
    Type: String
    Description: Spin up an RDS Instance
    AllowedValues:
      - "true"
      - "false"
    Default: "true"
  RedisAlternativeUrl:
    Type: String
    Description: (Advanced) Alternative URL for an existing Redis instance (e.g. redis://user:pass@host:port). If provided, an ElastiCache instance will not be created.
    Default: ""
  BrainstoreInstanceType:
    Type: String
    Description: |
      EC2 instance type for Brainstore reader nodes. Must be a Graviton instance type. Preferably with 16GB of
      memory and a local SSD for cache data. The default value is for production deployments and
      costs roughly $580 per month.
    Default: c8gd.2xlarge
  BrainstoreWriterInstanceType:
    Type: String
    Description: |
      EC2 instance type for Brainstore writer node.  Preferably with 32GB of
      memory and a local SSD for cache data. The default value is for production deployments and
      costs roughly $1000 per month.
    Default: c8gd.8xlarge
  BrainstoreInstanceKeyPairName:
    Description: The EC2 Key Pair to allow SSH access to the Brainstore instance. Can be left blank if you do not need to SSH into the instance or if you use something like SSM.
    Type: String
    Default: ""
  BrainstoreVersionOverride:
    Type: String
    Description: |
      This will lock Brainstore to a specific docker tag.
      Warning: If you set this, you will need to manually delete this value in future deployments if you want to get the latest version.
    Default: ""
  BrainstoreAutoscalingMinInstanceCount:
    Type: Number
    Description: Minimum number of Brainstore reader instances.
    Default: 2
    MinValue: 0
  BrainstoreAutoscalingMaxInstanceCount:
    Type: Number
    Description: Maximum number of Brainstore reader instances for autoscaling and deployments.
    Default: 10
    MinValue: 0
  BrainstoreAutoscalingCpuTargetValue:
    Type: Number
    Description: Target CPU utilization percentage for Brainstore reader autoscaling.
    Default: 70.0
    MinValue: 1.0
    MaxValue: 100.0
  BrainstoreWriterAutoscalingMinInstanceCount:
    Type: Number
    Description: Minimum number of Brainstore writer instances.
    Default: 1
    MinValue: 0
  BrainstoreWriterAutoscalingMaxInstanceCount:
    Type: Number
    Description: Maximum number of Brainstore writer instances for autoscaling and deployments.
    Default: 10
    MinValue: 0
  BrainstoreWriterAutoscalingCpuTargetValue:
    Type: Number
    Description: Target CPU utilization percentage for Brainstore writer autoscaling.
    Default: 70.0
    MinValue: 1.0
    MaxValue: 100.0
  BrainstorePort:
    Type: Number
    Description: Port for Brainstore
    Default: 4000
  BrainstoreLicenseKey:
    Type: String
    Description: License key for Brainstore. Required or Brainstore will not start.
    Default: ""
  ManagedClickhouse:
    Type: String
    Description: (Deprecated - ClickHouse no longer supported) Spin up a Clickhouse Instance for faster analytics
    AllowedValues:
      - "true"
      - "false"
    Default: "false"
  ClickhouseBucketGlobalName:
    Type: String
    AllowedValues:
      - "true"
      - "false"
    Default: "false"
    Description: (Deprecated - ClickHouse no longer supported) You should not need to set this unless you deployed a Clickhouse bucket in the past.
  EncryptDatabase:
    Type: String
    Description: Encrypt the database
    AllowedValues:
      - "true"
      - "false"
    Default: "false"
  PostgresInstanceType:
    Type: String
    Description: Instance type for the RDS instance
    Default: "db.t4g.xlarge"
  PostgresVersion:
    Type: String
    Description: Version for the RDS instance
    Default: "16.6"
  PostgresStorageType:
    Type: String
    Description: Storage type for the RDS instance
    Default: "gp2"
    AllowedValues:
      - "gp2"
      - "gp3"
      - "io1"
      - "standard"
  PostgresStorageSize:
    Type: Number
    Description: Storage size for the RDS instance in GB
    Default: 100
  PostgresMaxStorageSize:
    Type: Number
    Description: Maximum storage size to allow the RDS instance to grow to.
    Default: 4000
  PostgresAlternativeHost:
    Type: String
    Description: Alternative host for the RDS instance
    Default: ""

  ClickhouseLinuxAMI:
    Type: "AWS::SSM::Parameter::Value<AWS::EC2::Image::Id>"
    Default: "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2"
    Description: (Deprecated - ClickHouse no longer supported)
  ClickhouseInstanceType:
    Type: String
    Description: (Deprecated - ClickHouse no longer supported) EC2 instance type
    Default: "c5.2xlarge"
    ConstraintDescription: Must be a valid EC2 instance type.
  ClickhouseMetadataStorageSize:
    Type: Number
    Description: (Deprecated - ClickHouse no longer supported) Storage size for Clickhouse metadata (<1 TB is fine)
    Default: 256
    MinValue: 32
    MaxValue: 16384
    ConstraintDescription: Must be between 32 and 16384
  ClickhouseInstanceKeyPairName:
    Description: (Deprecated - ClickHouse no longer supported) The EC2 Key Pair to allow SSH access to the Clickhouse instance
    Type: String
    ConstraintDescription: Must be the name of an existing EC2 KeyPair.
    Default: ""
  ClickhouseRelaxedSearchMode:
    Type: String
    Description: (Deprecated - ClickHouse no longer supported) Enable relaxed search mode for Clickhouse (more efficient, but may return duplicate results)
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  APIHandlerMemorySize:
    Type: Number
    Description: Memory size for the API handler
    Default: 10240
    MinValue: 2048
    MaxValue: 10240
    ConstraintDescription: Must be between 128 and 3008
  LogLevel:
    Type: String
    Description: Log level for API handler and Clickhouse catchup ETL functions
    Default: ""
    AllowedValues:
      - ""
      - "info"
      - "warn"
      - "error"
      - "debug"
  ProvisionedConcurrency:
    Type: Number
    Description: Enter the number of concurrent executions to provision for this function
    Default: 0
    MinValue: 0
    MaxValue: 1000
    ConstraintDescription: Must be between 0 and 1000
  WhitelistedOrigins:
    Type: CommaDelimitedList
    Description: List of origins to whitelist for CORS
    Default: ""

  OutboundRateLimitWindowMinutes:
    Type: Number
    Description: The time frame in minutes over which rate per-user rate limits are accumulated
    Default: 1
  OutboundRateLimitMaxRequests:
    Type: Number
    Description: The maximum number of requests per user allowed in the time frame specified by OutboundRateLimitMaxRequests. Setting to 0 will disable rate limits
    Default: 0
    MinValue: 0
    ConstraintDescription: Must be nonnegative

  RateLimitApiLogsOrg:
    Type: String
    Description: Comma-separated list of org rate limits in format "org_id=limit, org_id=limit" (e.g. "abc123=5, def456=10"). Leave empty to disable org-level rate limiting.
    Default: ""

  RateLimitApiLogsOrgWindowSecs:
    Type: Number
    Description: Time window in seconds for org-level rate limiting
    Default: 60
    MinValue: 1
    ConstraintDescription: Must be positive

  RateLimitApiLogsOrgEnforce:
    Type: String
    Description: Whether to enforce org-level rate limits (true) or just log warnings (false)
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  CodeFunctionExecutionTimeout:
    Type: Number
    Description: The timeout for code function execution in seconds
    Default: 30
    MinValue: 1
    MaxValue: 900

  DisableAttachmentOptimization:
    Type: String
    Description: Do not automatically convert base64 payloads to attachments.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  UseGlobalProxy:
    Type: String
    Description: Use the global proxy
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  CustomCertificateArn:
    Type: String
    Description: Custom certificate ARN for Cloudfront. Specify this if you are using a custom domain and a custom certificate. You do not need to set this unless instructed by support.
    Default: ""

  CustomDomain:
    Type: String
    Description: Custom domain for the API. Specify this if you are using a custom domain. You do not need to set this unless instructed by support.
    Default: ""

  OriginReadTimeout:
    Type: Number
    Description: Timeout for the origin in seconds. This is the timeout for the API handler to connect to the database and other services. Only change this if you had AWS support increase your limit explicitly.
    Default: 60
    MinValue: 1
    MaxValue: 180

  RunDraftMigrations:
    Type: String
    Description: Enable draft migrations for database schema updates
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  EnableBrainstore:
    Type: String
    Description: Enable Brainstore object-storage data backend
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreDefault:
    Type: String
    Description: Use Brainstore as the default data backend. You should only do this once backfill has completed. "true" is the same as "default" and is only allowed for back-compat reasons.
    AllowedValues:
      - "default"
      - "force"
      - "true"
      - "false"
    Default: "false"

  BrainstoreBackfillDisableLogs:
    Type: String
    Description: Disable backfill of logs. (This is an advanced setting you should only use to debug with the Braintrust team).
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreBackfillHistoricalBatchSize:
    Type: Number
    Description: The number of objects to backfill in each historical batch.
    Default: 40000
    MinValue: 1000
    MaxValue: 40000

  EnableBraintrustSupportLogsAccess:
    Type: String
    Description: "Enable Cloudwatch logs access for Braintrust staff"
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  EnableBraintrustSupportShellAccess:
    Type: String
    Description: |
      If set to true, creates an EC2 bastion host that Braintrust support staff can connect to using EC2 Instance Connect.
      This host will only be accessible to a specific Braintrust IAM role. The Bastion host is restricted to only be able
      to connect to resources in this Cloudformation Stack.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  EnableBillingTelemetry:
    Type: String
    Description: DEPRECATED. Use the MonitoringTelemetry parameter with or without `usage` instead.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  DisableBillingTelemetryAggregation:
    Type: String
    Description: Disable billing telemetry aggregation. Do not enable this unless instructed by support.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BillingTelemetryLogLevel:
    Type: String
    Description: Log level for billing telemetry. Defaults to "error" if empty, or unspecified.
    Default: ""
    AllowedValues:
      - ""
      - "info"
      - "warn"
      - "error"
      - "debug"

  DisableSysadminTelemetry:
    Type: String
    Description: Sysadmin telemetry gives the Braintrust team access to performance metrics (no data) for Brainstore. You can disable this if you do not want to share this data.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  InternalObservabilityApiKey:
    Type: String
    Description: Support for internal observability agent. Do not set this unless instructed by support.
    Default: ""

  InternalObservabilityEnvName:
    Type: String
    Description: Support for internal observability agent. Do not set this unless instructed by support.
    Default: ""

  InternalObservabilityRegion:
    Type: String
    Description: Support for internal observability agent. Do not set this unless instructed by support.
    Default: "us5"

  BrainstoreEnableRetention:
    Type: String
    Description: "Enable time-based retention for Brainstore."
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreRetentionIntervalSeconds:
    Type: Number
    Description: How frequently (in seconds) the retention worker runs.
    Default: 3600
    MinValue: 600

  BrainstoreVacuumCleanupDelaySeconds:
    Type: Number
    Description: Grace period (in seconds) before vacuum purges soft-deleted data.
    Default: 86400
    MinValue: 3600

  BrainstoreEnableObjectStoreLocksManager:
    Type: String
    Description: Use object storage for locks manager.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreVersioningExpirationDays:
    Type: Number
    Description: The number of days to keep versioned objects in Brainstore's S3 bucket.
    Default: 7
    MinValue: 1
    MaxValue: 365

  BrainstoreDisableEtlLoop:
    Type: String
    Description: Disable the ETL loop in Brainstore.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreDisableBackfillCompaction:
    Type: String
    Description: Disable backfill-triggered compaction in Brainstore.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreDisableOptimizationWorker:
    Type: String
    Description: Disable the optimization worker in Brainstore.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreInsertRowRefs:
    Type: String
    Description: Insert object store row references into the main DB.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  InsertLogs2:
    Type: String
    Description: Insert into the secondary logs table.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  BrainstoreExtraEnvVars:
    Type: String
    Description: Comma-separated list of additional key-value pairs to set in Brainstore. E.g. FOO=bar,BAZ=qux
    Default: ""

  BrainstoreCacheFileSize:
    Type: String
    Description: The size of the cache file.
    Default: 50gb
    ConstraintDescription: Must be a valid size.

  MonitoringTelemetry:
    Type: String
    Description: The telemetry to send to Braintrust's control plane to monitor your deployment. Should be in the form of comma-separated values. Available options are metrics, logs, traces, status, memprof, and usage.
    Default: status,metrics,usage

  DisableAsyncScoring:
    Type: String
    Description: Disable async scoring entirely.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  DisableAsyncScoringObjectIds:
    Type: String
    Description: Comma-separated list of brainstore object ids to disable async scoring for.
    Default: ""

  InternalOnlyBraintrustHostedDataPlane:
    Type: String
    Description: Internal only. Do not use for self-hosting.
    AllowedValues:
      - "true"
      - "false"
    Default: "false"

  MaxLimitForQueries:
    Type: Number
    Description: If nonzero, imposes a maximum on the limit parameter in queries.
    Default: 0
    MinValue: 0

  APISegmentWriteKey:
    Type: String
    Description: Segment write key for API server analytics tracking
    Default: ""

Conditions:
  UseProvisionedConcurrency: !Not [!Equals [!Ref ProvisionedConcurrency, 0]]
  UseManagedPostgres: !Equals [!Ref ManagedPostgres, "true"]
  UsePostgresAlternativeHost: !Not [!Equals [!Ref PostgresAlternativeHost, ""]]
  UseEncryptDatabase: !Equals [!Ref EncryptDatabase, "true"]
  UseManagedClickhouse: !Equals [!Ref ManagedClickhouse, "true"]
  UseRedisAlternativeUrl: !Not [!Equals [!Ref RedisAlternativeUrl, ""]]
  UseManagedRedis: !Equals [!Ref RedisAlternativeUrl, ""]
  ClickhouseKeyPairSpecified:
    !Not [!Equals [!Ref ClickhouseInstanceKeyPairName, ""]]
  ClickhouseBucketGlobalNameSpecified:
    !Equals [!Ref ClickhouseBucketGlobalName, "true"]
  UseGlobalProxy: !Equals [!Ref UseGlobalProxy, "true"]
  HasCustomDomain: !Not [!Equals [!Ref CustomDomain, ""]]
  UseBrainstore: !Equals [!Ref EnableBrainstore, "true"]
  HasBrainstoreWriterInstances: !And
    - !Equals [!Ref EnableBrainstore, "true"]
    - !Not [!Equals [!Ref BrainstoreWriterAutoscalingMinInstanceCount, 0]]
  BrainstoreKeyPairSpecified:
    !Not [!Equals [!Ref BrainstoreInstanceKeyPairName, ""]]
  HasBraintrustSupportLogsAccess:
    !Equals [!Ref EnableBraintrustSupportLogsAccess, "true"]
  HasBraintrustSupportShellAccess:
    !Equals [!Ref EnableBraintrustSupportShellAccess, "true"]
  HasBraintrustSupportAccess: !Or
    - !Equals [!Ref EnableBraintrustSupportLogsAccess, "true"]
    - !Equals [!Ref EnableBraintrustSupportShellAccess, "true"]
  HasInternalObservabilityEnabled:
    !Not [!Equals [!Ref InternalObservabilityApiKey, ""]]

Resources:
  APIHandler:
    DependsOn:
      - DatabaseSecret
      - ClickhouseSecret
    Properties:
      MemorySize: !Ref APIHandlerMemorySize
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          CLICKHOUSE_PG_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "postgres://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:9005/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
          CLICKHOUSE_CONNECT_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "http://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:8123/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
          CLICKHOUSE_RELAXED_SEARCH_MODE: !Ref ClickhouseRelaxedSearchMode
          CATCHUP_ETL_ARN:
            !If [UseManagedClickhouse, !GetAtt ClickhouseCatchupEtl.Arn, ""]
          WHITELISTED_ORIGINS: !Join [",", !Ref WhitelistedOrigins]

          OUTBOUND_RATE_LIMIT_WINDOW_MINUTES:
            Ref: OutboundRateLimitWindowMinutes
          OUTBOUND_RATE_LIMIT_MAX_REQUESTS:
            Ref: OutboundRateLimitMaxRequests
          BRAINSTORE_ENABLED: !Ref EnableBrainstore
          BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE: !Ref BrainstoreBackfillHistoricalBatchSize
          BRAINSTORE_URL:
            !If [
              UseBrainstore,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_WRITER_URL:
            !If [
              HasBrainstoreWriterInstances,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreWriterNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_REALTIME_WAL_BUCKET:
            !If [UseBrainstore, !Ref BrainstoreS3Bucket, ""]
          RATELIMIT_API_LOGS_ORG:
            Ref: RateLimitApiLogsOrg
          RATELIMIT_API_LOGS_ORG_WINDOW_SECS:
            Ref: RateLimitApiLogsOrgWindowSecs
          RATELIMIT_API_LOGS_ORG_ENFORCE:
            Ref: RateLimitApiLogsOrgEnforce
          API_SEGMENT_WRITE_KEY:
            Ref: APISegmentWriteKey
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3
      AutoPublishAlias: live2
      ProvisionedConcurrencyConfig:
        !If [
          UseProvisionedConcurrency,
          ProvisionedConcurrentExecutions: !Ref ProvisionedConcurrency,
          !Ref AWS::NoValue,
        ]
      EphemeralStorage:
        Size: 4096
      Role: !GetAtt APIHandlerRole.Arn
  APIHandlerJS:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ../../api-ts/dist/lambda/index.zip
      Runtime: nodejs22.x
      Architectures:
        - arm64
      Layers:
        # See https://github.com/tobilg/duckdb-nodejs-layer
        - Fn::Sub: "arn:aws:lambda:${AWS::Region}:041475135427:layer:duckdb-nodejs-arm64:14"
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PRIMARY_ORG_NAME:
            Ref: PrimaryOrgName
          BRAINTRUST_HOSTED_DATA_PLANE: !Ref InternalOnlyBraintrustHostedDataPlane
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          CLICKHOUSE_PG_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "postgres://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:9005/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
          CLICKHOUSE_CONNECT_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "http://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:8123/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
          CLICKHOUSE_RELAXED_SEARCH_MODE: !Ref ClickhouseRelaxedSearchMode

          CATCHUP_ETL_ARN:
            !If [UseManagedClickhouse, !GetAtt ClickhouseCatchupEtl.Arn, ""]
          WHITELISTED_ORIGINS: !Join [",", !Ref WhitelistedOrigins]
          RESPONSE_BUCKET:
            Ref: LambdaResponsesBucket
          CODE_BUNDLE_BUCKET:
            Ref: CodeBundleBucket
          OUTBOUND_RATE_LIMIT_WINDOW_MINUTES:
            Ref: OutboundRateLimitWindowMinutes
          OUTBOUND_RATE_LIMIT_MAX_REQUESTS:
            Ref: OutboundRateLimitMaxRequests
          AI_PROXY_FN_ARN: !GetAtt AIProxyFn.Arn
          AI_PROXY_FN_URL: !GetAtt AIProxyFnUrl.FunctionUrl
          AI_PROXY_INVOKE_ROLE: !GetAtt AIProxyInvokeRole.Arn
          CODE_FUNCTION_EXECUTION_TIMEOUT_S: !Ref CodeFunctionExecutionTimeout
          DISABLE_ATTACHMENT_OPTIMIZATION: !Ref DisableAttachmentOptimization
          QUARANTINE_INVOKE_ROLE:
            !If [HasQuarantine, !GetAtt QuarantineInvokeRole.Arn, ""]
          QUARANTINE_FUNCTION_ROLE:
            !If [HasQuarantine, !GetAtt QuarantineFunctionRole.Arn, ""]
          QUARANTINE_PRIVATE_SUBNET_1_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet1, ""]
          QUARANTINE_PRIVATE_SUBNET_2_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet2, ""]
          QUARANTINE_PRIVATE_SUBNET_3_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet3, ""]
          QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP:
            !If [
              HasQuarantine,
              !GetAtt QuarantinepubPrivateVPC.DefaultSecurityGroup,
              "",
            ]
          QUARANTINE_PUB_PRIVATE_VPC_ID:
            !If [HasQuarantine, !Ref QuarantinepubPrivateVPC, ""]

          FUNCTION_SECRET_KEY:
            Fn::Sub: "{{resolve:secretsmanager:${FunctionToolsSecret}::key}}"
          LOG_LEVEL: !Ref LogLevel
          BRAINSTORE_ENABLED: !Ref EnableBrainstore
          BRAINSTORE_DEFAULT: !Ref BrainstoreDefault
          BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE: !Ref BrainstoreBackfillHistoricalBatchSize
          BRAINSTORE_URL:
            !If [
              UseBrainstore,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_WRITER_URL:
            !If [
              HasBrainstoreWriterInstances,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreWriterNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_REALTIME_WAL_BUCKET:
            !If [UseBrainstore, !Ref BrainstoreS3Bucket, ""]
          CONTROL_PLANE_TELEMETRY: !Ref MonitoringTelemetry
          TELEMETRY_DISABLE_AGGREGATION: !Ref DisableBillingTelemetryAggregation
          TELEMETRY_LOG_LEVEL: !Ref BillingTelemetryLogLevel
          DISABLE_SYSADMIN_TELEMETRY: !Ref DisableSysadminTelemetry
          BRAINSTORE_DISABLE_ETL_LOOP: !Ref BrainstoreDisableEtlLoop
          BRAINSTORE_DISABLE_COMPACTION: !Ref BrainstoreDisableBackfillCompaction
          BRAINSTORE_INSERT_ROW_REFS: !Ref BrainstoreInsertRowRefs
          INSERT_LOGS2: !Ref InsertLogs2
          DISABLE_ASYNC_SCORING: !Ref DisableAsyncScoring
          DISABLE_ASYNC_SCORING_OBJECT_IDS: !Ref DisableAsyncScoringObjectIds
          RATELIMIT_API_LOGS_ORG: !Ref RateLimitApiLogsOrg
          RATELIMIT_API_LOGS_ORG_WINDOW_SECS: !Ref RateLimitApiLogsOrgWindowSecs
          RATELIMIT_API_LOGS_ORG_ENFORCE: !Ref RateLimitApiLogsOrgEnforce
          MAX_LIMIT_FOR_QUERIES: !Ref MaxLimitForQueries
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3
      AutoPublishAlias: live
      # ProvisionedConcurrencyConfig:
      #   !If [
      #     UseProvisionedConcurrency,
      #     ProvisionedConcurrentExecutions: !Ref ProvisionedConcurrency,
      #     !Ref AWS::NoValue,
      #   ]
      Role: !GetAtt APIHandlerRole.Arn
      Handler: index.handler
      MemorySize: !Ref APIHandlerMemorySize
      EphemeralStorage:
        Size: 4096
      Timeout: 600
      Tracing: PassThrough

  AIProxyFn:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ../../api-ts/dist/proxy/index.zip
      Runtime: nodejs22.x
      Architectures:
        - arm64
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          CODE_BUNDLE_BUCKET:
            Ref: CodeBundleBucket
          QUARANTINE_INVOKE_ROLE:
            !If [HasQuarantine, !GetAtt QuarantineInvokeRole.Arn, ""]
          QUARANTINE_FUNCTION_ROLE:
            !If [HasQuarantine, !GetAtt QuarantineFunctionRole.Arn, ""]
          QUARANTINE_PRIVATE_SUBNET_1_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet1, ""]
          QUARANTINE_PRIVATE_SUBNET_2_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet2, ""]
          QUARANTINE_PRIVATE_SUBNET_3_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet3, ""]
          QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP:
            !If [
              HasQuarantine,
              !GetAtt QuarantinepubPrivateVPC.DefaultSecurityGroup,
              "",
            ]
          QUARANTINE_PUB_PRIVATE_VPC_ID:
            !If [HasQuarantine, !Ref QuarantinepubPrivateVPC, ""]

          FUNCTION_SECRET_KEY:
            Fn::Sub: "{{resolve:secretsmanager:${FunctionToolsSecret}::key}}"
          CODE_FUNCTION_EXECUTION_TIMEOUT_S: !Ref CodeFunctionExecutionTimeout
          DISABLE_ATTACHMENT_OPTIMIZATION: !Ref DisableAttachmentOptimization
          LOG_LEVEL: !Ref LogLevel
          BRAINSTORE_ENABLED: !Ref EnableBrainstore
          BRAINSTORE_DEFAULT: !Ref BrainstoreDefault
          BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE: !Ref BrainstoreBackfillHistoricalBatchSize
          BRAINSTORE_URL:
            !If [
              UseBrainstore,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_WRITER_URL:
            !If [
              HasBrainstoreWriterInstances,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreWriterNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_REALTIME_WAL_BUCKET:
            !If [UseBrainstore, !Ref BrainstoreS3Bucket, ""]
          CONTROL_PLANE_TELEMETRY: !Ref MonitoringTelemetry
          TELEMETRY_DISABLE_AGGREGATION: !Ref DisableBillingTelemetryAggregation
          TELEMETRY_LOG_LEVEL: !Ref BillingTelemetryLogLevel
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3
      AutoPublishAlias: live
      EphemeralStorage:
        Size: 1024
      Handler: index.handler
      MemorySize: !Ref APIHandlerMemorySize
      Role: !GetAtt APIHandlerRole.Arn
      Timeout: 900
      Tracing: PassThrough
      FunctionUrlConfig:
        AuthType: NONE
        InvokeMode: RESPONSE_STREAM
        Cors:
          AllowCredentials: true
          AllowHeaders:
            - authorization
            - content-type
            - x-bt-org-name
            - x-bt-app-origin
            - x-bt-parent
            - x-bt-auth-token
            - x-bt-stream-fmt
            - x-bt-use-cache
            - x-stainless-os
            - x-stainless-lang
            - x-stainless-package-version
            - x-stainless-runtime
            - x-stainless-runtime-version
            - x-stainless-arch
          AllowMethods:
            - POST
            - GET
          AllowOrigins:
            - "*"
          ExposeHeaders:
            - content-type
            - keep-alive
            - access-control-allow-credentials
            - access-control-allow-origin
            - access-control-allow-methods
            - x-bt-internal-trace-id
          MaxAge: 86400

  AIProxyFnPermission:
    Type: AWS::Lambda::Permission
    Properties:
      Action: lambda:InvokeFunctionUrl
      FunctionName:
        Ref: AIProxyFn.Alias
      FunctionUrlAuthType: NONE
      Principal: "*"

  FunctionToolsSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: Function environment variables encryption key
      GenerateSecretString:
        SecretStringTemplate: '{"key": "value"}'
        GenerateStringKey: key
        PasswordLength: 32
        ExcludeCharacters: "\"'@/\\"
        ExcludePunctuation: true

  LambdaResponsesBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      LifecycleConfiguration:
        # Since there is no prefix, this should apply to all items.
        Rules:
          - Id: ExpireObjectsAfterOneDay
            Status: Enabled
            ExpirationInDays: 1
          - Id: AbortIncompleteMultipartAfterOneDay
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 1
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3600

  CodeBundleBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      LifecycleConfiguration:
        Rules:
          - Id: AbortIncompleteMultipartAfterOneDay
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 1
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - PUT
              - GET
              - HEAD
            AllowedOrigins:
              - "*"
            MaxAge: 3600
  APIHandlerJSInvokePermission:
    Properties:
      Action: lambda:InvokeFunction
      FunctionName:
        Ref: APIHandlerJS
      Principal: apigateway.amazonaws.com
      SourceArn:
        Fn::Sub:
          - arn:${AWS::Partition}:execute-api:${AWS::Region}:${AWS::AccountId}:${RestAPIId}/*
          - RestAPIId:
              Ref: RestAPI
    Type: AWS::Lambda::Permission
  ClickhouseCatchupEtl:
    DependsOn:
      - DatabaseSecret
      - ClickhouseSecret
    Properties:
      MemorySize: 1024
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          CLICKHOUSE_PG_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "postgres://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:9005/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
          CLICKHOUSE_CONNECT_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "http://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:8123/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
          CLICKHOUSE_RELAXED_SEARCH_MODE: !Ref ClickhouseRelaxedSearchMode
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          BRAINSTORE_ENABLED: !Ref EnableBrainstore
          BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE: !Ref BrainstoreBackfillHistoricalBatchSize
          BRAINSTORE_BACKFILL_DISABLE_LOGS: !Ref BrainstoreBackfillDisableLogs
          BRAINSTORE_URL:
            !If [
              UseBrainstore,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_WRITER_URL:
            !If [
              HasBrainstoreWriterInstances,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreWriterNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_REALTIME_WAL_BUCKET:
            !If [UseBrainstore, !Ref BrainstoreS3Bucket, ""]
          BRAINSTORE_DISABLE_ETL_LOOP: !Ref BrainstoreDisableEtlLoop
          BRAINSTORE_DISABLE_COMPACTION: !Ref BrainstoreDisableBackfillCompaction
          LOG_LEVEL: !Ref LogLevel
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3

  AutomationCron:
    DependsOn:
      - DatabaseSecret
    Properties:
      MemorySize: 1024
      Layers:
        # See https://github.com/tobilg/duckdb-nodejs-layer
        - Fn::Sub: "arn:aws:lambda:${AWS::Region}:041475135427:layer:duckdb-nodejs-arm64:14"
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          BRAINSTORE_ENABLED: !Ref EnableBrainstore
          BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE: !Ref BrainstoreBackfillHistoricalBatchSize
          BRAINSTORE_BACKFILL_DISABLE_LOGS: !Ref BrainstoreBackfillDisableLogs
          BRAINSTORE_URL:
            !If [
              UseBrainstore,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_WRITER_URL:
            !If [
              HasBrainstoreWriterInstances,
              !Sub [
                "http://${DNSName}:${BrainstorePort}",
                DNSName: !GetAtt BrainstoreWriterNLB.DNSName,
              ],
              "",
            ]
          BRAINSTORE_REALTIME_WAL_BUCKET:
            !If [UseBrainstore, !Ref BrainstoreS3Bucket, ""]
          FUNCTION_SECRET_KEY:
            Fn::Sub: "{{resolve:secretsmanager:${FunctionToolsSecret}::key}}"
          LOG_LEVEL: !Ref LogLevel
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3
      Role:
        Fn::GetAtt:
          - APIHandlerRole
          - Arn

  BillingCron:
    DependsOn:
      - DatabaseSecret
    Properties:
      MemorySize: 1024
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          LOG_LEVEL: !Ref LogLevel
          CONTROL_PLANE_TELEMETRY: !Ref MonitoringTelemetry
          TELEMETRY_DISABLE_AGGREGATION: !Ref DisableBillingTelemetryAggregation
          TELEMETRY_LOG_LEVEL: !Ref BillingTelemetryLogLevel
          SERVICE_TOKEN_SECRET_KEY:
            Fn::Sub: "{{resolve:secretsmanager:${FunctionToolsSecret}::key}}"
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3
      Role:
        Fn::GetAtt:
          - APIHandlerRole
          - Arn

  DefaultRole:
    Properties:
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
      # Somehow, Chalice seems to screw this up and give the lambda function permissios
      # to either kafka, or redis, but not both. So we have to manually add the permissions here.
      Policies:
        - PolicyName: DefaultRolePolicy
          PolicyDocument:
            Statement:
              - Action:
                  - elasticache:DescribeCacheClusters
                Effect: Allow
                Resource:
                  - "*"
                Sid: 87babe97375e4733bc25932eb8522ec6
              - Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Effect: Allow
                Resource: arn:*:logs:*:*:*
              - Action:
                  - secretsmanager:GetSecretValue
                Effect: Allow
                Resource: !Ref DatabaseSecret
            Version: "2012-10-17"
  APIHandlerRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Sid: ""
        Version: "2012-10-17"
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
      # Somehow, Chalice seems to screw this up and give the lambda function permissios
      # to either kafka, or redis, but not both. So we have to manually add the permissions here.
      Policies:
        - PolicyDocument:
            Statement:
              - Action:
                  - elasticache:DescribeCacheClusters
                Effect: Allow
                Resource:
                  - "*"
                Sid: 87babe97375e4733bc25932eb8522ec6
              - Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Effect: Allow
                Resource: arn:*:logs:*:*:*
              - Action:
                  - secretsmanager:GetSecretValue
                Effect: Allow
                Resource: !Ref DatabaseSecret
              - Action:
                  - lambda:InvokeFunction
                Effect: Allow
                Sid: ClickhouseCatchupEtlInvoke
                Resource:
                  !If [
                    UseManagedClickhouse,
                    !GetAtt ClickhouseCatchupEtl.Arn,
                    !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:nonExistentFunctionBraintrust",
                  ]
              - Action: s3:*
                Effect: Allow
                Resource:
                  - !Sub "arn:aws:s3:::${LambdaResponsesBucket}"
                  - !Sub "arn:aws:s3:::${LambdaResponsesBucket}/*"
              - Action: s3:*
                Effect: Allow
                Resource:
                  - !Sub "arn:aws:s3:::${CodeBundleBucket}"
                  - !Sub "arn:aws:s3:::${CodeBundleBucket}/*"
              - !If
                - UseBrainstore
                - Action: s3:*
                  Effect: Allow
                  Resource:
                    - !Sub "arn:aws:s3:::${BrainstoreS3Bucket}"
                    - !Sub "arn:aws:s3:::${BrainstoreS3Bucket}/*"
                - !Ref "AWS::NoValue"
              # Quarantine permissions
              - Action:
                  - lambda:CreateFunction
                  - lambda:PublishVersion
                Effect: Allow
                Sid: QuarantinePublish
                Resource:
                  - !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*"
              # https://aws.amazon.com/blogs/compute/using-aws-lambda-iam-condition-keys-for-vpc-settings/
              - Action:
                  - lambda:CreateFunction
                  - lambda:PublishVersion
                Effect: Deny
                Sid: EnforceQuarantineVPC
                Resource: "*"
                Condition:
                  StringNotEquals:
                    lambda:VpcIds:
                      - !If [HasQuarantine, !Ref QuarantinepubPrivateVPC, ""]
              - Action:
                  - lambda:TagResource
                Effect: Allow
                Sid: TagQuarantine
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/BraintrustQuarantine": "true"
              - Action:
                  - lambda:DeleteFunction
                  - lambda:UpdateFunctionCode
                  - lambda:UpdateFunctionConfiguration
                  - lambda:GetFunctionConfiguration
                Effect: Allow
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/BraintrustQuarantine": "true"
              - Action: iam:PassRole
                Effect: Allow
                Resource:
                  - !GetAtt QuarantineFunctionRole.Arn
              # These cannot be scoped to the VPC, so we need to allow all access
              - Action:
                  - ec2:DescribeSecurityGroups
                  - ec2:DescribeSubnets
                  - ec2:DescribeVpcs
                Effect: Allow
                Resource: "*"
              - Action:
                  - sts:AssumeRole
                Effect: Allow
                Resource: "*"
                Condition:
                  StringLike:
                    sts:ExternalId: "bt:*"
            Version: "2012-10-17"
          PolicyName: APIHandlerRolePolicy

  AIProxyInvokeRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - APIHandlerRole
                  - Arn
      Policies:
        - PolicyDocument:
            Statement:
              - Action: lambda:InvokeFunction
                Effect: Allow
                Resource:
                  - !GetAtt AIProxyFn.Arn
            Version: "2012-10-17"
          PolicyName: QuarantineInvokeRolePolicy

  QuarantineInvokeRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              AWS:
                Fn::GetAtt:
                  - APIHandlerRole
                  - Arn
      Policies:
        - PolicyDocument:
            Statement:
              - Action: lambda:InvokeFunction
                Effect: Allow
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/BraintrustQuarantine": "true"
            Version: "2012-10-17"
          PolicyName: QuarantineInvokeRolePolicy

  QuarantineFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Sid: ""
        Version: "2012-10-17"
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole

  ElastiCacheSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Condition: UseManagedRedis
    Properties:
      CacheSubnetGroupName: !Join
        - "-"
        - - "ElastiCacheSubnetGroupBT"
          - !Ref "AWS::StackName"
      SubnetIds:
        - !Ref privateSubnet1
        - !Ref privateSubnet2
        - !Ref privateSubnet3
      Description: "Subnet group for elasticache"
  ElastiCacheCluster:
    Type: AWS::ElastiCache::CacheCluster
    Condition: UseManagedRedis
    Properties:
      CacheNodeType: cache.t4g.small
      CacheSubnetGroupName: !Ref ElastiCacheSubnetGroup
      Engine: redis
      EngineVersion: 7.0
      VpcSecurityGroupIds: [!GetAtt pubPrivateVPC.DefaultSecurityGroup]
      NumCacheNodes: 1
      # Redis does not support this
      # TransitEncryptionEnabled: true

  Database:
    DependsOn:
      - DatabaseSecret
    Type: AWS::RDS::DBInstance
    Condition: UseManagedPostgres
    Properties:
      # NOTE: Many of these should be parameters
      AllocatedStorage: !Ref PostgresStorageSize
      MaxAllocatedStorage: !Ref PostgresMaxStorageSize
      StorageType: !Ref PostgresStorageType
      DBInstanceClass: !Ref PostgresInstanceType
      Engine: postgres
      EngineVersion: !Ref PostgresVersion
      MasterUsername:
        Fn::Sub: "{{resolve:secretsmanager:${DatabaseSecret}::username}}"
      MasterUserPassword:
        Fn::Sub: "{{resolve:secretsmanager:${DatabaseSecret}::password}}"
      MonitoringInterval: 60
      MonitoringRoleArn: !GetAtt DatabaseMonitoringRole.Arn
      VPCSecurityGroups: [!GetAtt pubPrivateVPC.DefaultSecurityGroup]
      DBSubnetGroupName: !Ref DBSubnetGroup
      DBParameterGroupName: !Ref DBParameterGroup
      StorageEncrypted: !If [UseEncryptDatabase, true, !Ref "AWS::NoValue"]
  # Note: Remember to keep in sync with the local DB config at
  # services/docker-compose.yml.
  DBParameterGroup:
    Type: "AWS::RDS::DBParameterGroup"
    # Condition: UseManagedPostgres
    Properties:
      # Make the family match the major version of postgres (e.g. postgres15, postgres16, etc)
      Family:
        Fn::Sub:
          - "postgres${MajorVersion}"
          - MajorVersion: !Select [0, !Split [".", !Ref PostgresVersion]]
      Description: "DB parameter group for RDS"
      Parameters:
        random_page_cost: 1
        statement_timeout: 3600000
        shared_preload_libraries: "pg_stat_statements,pg_hint_plan,pg_cron"
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    # Condition: UseManagedPostgres
    Properties:
      DBSubnetGroupDescription: "Subnet group for RDS"
      SubnetIds:
        - !Ref privateSubnet1
        - !Ref privateSubnet2
        - !Ref privateSubnet3
  DatabaseSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: Username/password for RDS
      GenerateSecretString:
        SecretStringTemplate: '{"username": "postgres"}'
        GenerateStringKey: password
        PasswordLength: 16
        ExcludeCharacters: "\"'@/\\"
        ExcludePunctuation: true
  # https://stackoverflow.com/questions/40294802/cloudformation-template-for-amazonrdsenhancedmonitoringrole
  DatabaseMonitoringRole:
    Type: "AWS::IAM::Role"
    # Condition: UseManagedPostgres
    Properties:
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
      AssumeRolePolicyDocument:
        Version: "2008-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: "monitoring.rds.amazonaws.com"
            Action: "sts:AssumeRole"

  # Brainstore
  BrainstoreS3Bucket:
    Type: AWS::S3::Bucket
    Condition: UseBrainstore
    Properties:
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpiration:
              NoncurrentDays: !Ref BrainstoreVersioningExpirationDays
          - Id: DeleteIndexDeletionLogs
            Status: Enabled
            Prefix: brainstore/index/delete_ops/
            ExpirationInDays: !Ref BrainstoreVersioningExpirationDays
          - Id: DeleteWalDeletionLogs
            Status: Enabled
            Prefix: brainstore/wal/delete_ops/
            ExpirationInDays: !Ref BrainstoreVersioningExpirationDays
          - Id: AbortIncompleteMultipartAfterOneDay
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 1
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
  BrainstoreEC2Role:
    Type: AWS::IAM::Role
    Condition: UseBrainstore
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: BrainstoreS3Access
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: s3:*
                Resource:
                  - Fn::Sub: arn:aws:s3:::${BrainstoreS3Bucket}
                  - Fn::Sub: arn:aws:s3:::${BrainstoreS3Bucket}/*
        - PolicyName: BrainstoreSecretsAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: secretsmanager:GetSecretValue
                Resource:
                  - !Ref DatabaseSecret
                  - !Ref FunctionToolsSecret
        - PolicyName: CloudWatchLogsAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                Resource:
                  - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/braintrust/${AWS::StackName}/brainstore:*"
                  - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/braintrust/${AWS::StackName}/brainstore/*:*"
  BrainstoreInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Condition: UseBrainstore
    Properties:
      Roles:
        - Ref: BrainstoreEC2Role
  BrainstoreLaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Condition: UseBrainstore
    Properties:
      LaunchTemplateData:
        ImageId:
          Fn::Sub: "{{resolve:ssm:/aws/service/canonical/ubuntu/server/22.04/stable/current/arm64/hvm/ebs-gp2/ami-id}}"
        InstanceType:
          Ref: BrainstoreInstanceType
        KeyName: !If
          - BrainstoreKeyPairSpecified
          - !Ref BrainstoreInstanceKeyPairName
          - !Ref "AWS::NoValue"
        SecurityGroupIds:
          - Fn::GetAtt:
              - pubPrivateVPC
              - DefaultSecurityGroup
        BlockDeviceMappings:
          - DeviceName: /dev/sda1
            Ebs:
              VolumeSize: 200
              VolumeType: gp3
              Encrypted: true
        IamInstanceProfile:
          Arn:
            Fn::GetAtt:
              - BrainstoreInstanceProfile
              - Arn
        MetadataOptions:
          HttpEndpoint: enabled
          HttpTokens: required
          HttpPutResponseHopLimit: 2
          InstanceMetadataTags: enabled
        Monitoring:
          Enabled: true
        UserData:
          Fn::Base64:
            Fn::Sub:
              - |
                #!/bin/bash
                # Help prevent immediate failure of apt commands if another background process is holding the lock
                echo 'DPkg::Lock::Timeout "60";' > /etc/apt/apt.conf.d/99apt-lock-retry

                apt-get install -y nvme-cli

                # Get the instance name from the metadata
                TOKEN=$(curl -s -X PUT "http://169.254.169.254/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
                INSTANCE_NAME=$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://169.254.169.254/latest/meta-data/tags/instance/Name)

                MOUNT_DIR="/mnt/tmp/brainstore"
                mkdir -p "$MOUNT_DIR"
                DEVICE=$(nvme list | grep 'Instance Storage' | head -n1 | awk '{print $1}')
                if [ -n "$DEVICE" ]; then
                  echo "Ephemeral device: $DEVICE"
                  blkid "$DEVICE" >/dev/null || mkfs.ext4 -F "$DEVICE"
                  mount "$DEVICE" "$MOUNT_DIR"
                  chmod -R 0777 "$MOUNT_DIR"
                  # Add to fstab using UUID rather than device name
                  UUID=$(blkid -s UUID -o value "$DEVICE")
                  echo "UUID=$UUID $MOUNT_DIR ext4 defaults 0 2" >> /etc/fstab
                else
                  echo "No ephemeral device found. Exiting with failure."
                  exit 1
                fi

                # Raise the file descriptor limit
                cat <<EOF > /etc/security/limits.d/brainstore.conf
                # Root users has to be set explicitly
                root soft nofile 65535
                root hard nofile 65535
                # All other users
                * soft nofile 65535
                * hard nofile 65535
                EOF

                # Set AWS region for CLI commands
                export AWS_DEFAULT_REGION=${AWS::Region}

                mkdir -p /etc/docker
                cat <<EOF > /etc/docker/daemon.json
                {
                  "log-driver": "json-file",
                  "log-opts": {
                    "max-size": "100m",
                    "max-file": "3"
                  }
                }
                EOF

                apt-get update
                apt-get install -y docker.io jq awscli earlyoom dstat
                systemctl start docker
                systemctl enable docker

                # Install CloudWatch agent
                arch=$(dpkg --print-architecture)
                wget https://s3.amazonaws.com/amazoncloudwatch-agent/ubuntu/$arch/latest/amazon-cloudwatch-agent.deb
                dpkg -i amazon-cloudwatch-agent.deb

                # Configure CloudWatch agent
                cat <<EOF > /opt/aws/amazon-cloudwatch-agent/bin/config.json
                {
                  "logs": {
                    "force_flush_interval": 5,
                    "logs_collected": {
                      "files": {
                        "collect_list": [
                          {
                            "file_path": "/var/lib/docker/containers/*/*.log",
                            "log_group_name": "/braintrust/${AWS::StackName}/brainstore",
                            "log_stream_name": "{instance_id}/containers",
                            "timezone": "UTC"
                          }
                        ]
                      }
                    }
                  }
                }
                EOF

                # Start CloudWatch agent
                /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/opt/aws/amazon-cloudwatch-agent/bin/config.json
                sed -i 's/Restart=.*/Restart=always/' /etc/systemd/system/amazon-cloudwatch-agent.service
                systemctl daemon-reload
                systemctl start amazon-cloudwatch-agent
                systemctl enable amazon-cloudwatch-agent

                # Get database credentials and service token secret from Secrets Manager
                DB_CREDS=$(aws secretsmanager get-secret-value --secret-id ${DatabaseSecret} --query SecretString --output text)
                DB_USERNAME=$(echo $DB_CREDS | jq -r .username)
                DB_PASSWORD=$(echo $DB_CREDS | jq -r .password)
                SERVICE_TOKEN_SECRET=$(aws secretsmanager get-secret-value --secret-id ${FunctionToolsSecret} --query SecretString --output text | jq -r .key)

                cat <<EOF > /etc/brainstore.env
                # WARNING: Do NOT use quotes around values here. They get passed as literals by docker.
                BRAINSTORE_VERBOSE=1
                BRAINSTORE_PORT=${BrainstorePort}
                BRAINSTORE_INDEX_URI=s3://${BrainstoreS3Bucket}/brainstore/index
                BRAINSTORE_REALTIME_WAL_URI=s3://${BrainstoreS3Bucket}/brainstore/wal
                BRAINSTORE_METADATA_URI=postgres://$DB_USERNAME:$DB_PASSWORD@${DatabaseHost}/postgres
                BRAINSTORE_WAL_URI=postgres://$DB_USERNAME:$DB_PASSWORD@${DatabaseHost}/postgres
                BRAINSTORE_CACHE_DIR=/mnt/tmp/brainstore
                BRAINSTORE_LICENSE_KEY=${BrainstoreLicenseKey}
                BRAINSTORE_DISABLE_OPTIMIZATION_WORKER=${BrainstoreDisableOptimizationWorker}
                BRAINSTORE_OBJECT_STORE_CACHE_FILE_SIZE=${BrainstoreCacheFileSize}
                SERVICE_TOKEN_SECRET_KEY=$SERVICE_TOKEN_SECRET
                NO_COLOR=1
                AWS_DEFAULT_REGION=${AWS::Region}
                AWS_REGION=${AWS::Region}
                EOF

                # Conditional logic for optimization worker
                if [[ "${BrainstoreDisableOptimizationWorker}" == "true" ]]; then
                  # Global parameter takes precedence - disable for all nodes
                  echo "BRAINSTORE_DISABLE_OPTIMIZATION_WORKER=true" >> /etc/brainstore.env
                fi

                echo "BRAINSTORE_TIME_BASED_RETENTION_INTERVAL_SECONDS=${BrainstoreRetentionIntervalSeconds}" >> /etc/brainstore.env

                echo "BRAINSTORE_VACUUM_DELETION_GRACE_PERIOD_SECONDS=${BrainstoreVacuumCleanupDelaySeconds}" >> /etc/brainstore.env
                echo "BRAINSTORE_VACUUM_SEGMENT_WAL_ENTRY_EXPIRATION_SECONDS=${BrainstoreVacuumCleanupDelaySeconds}" >> /etc/brainstore.env

                if [[ "${BrainstoreEnableObjectStoreLocksManager}" == "true" ]]; then
                  echo "BRAINSTORE_LOCKS_URI=s3://${BrainstoreS3Bucket}/brainstore/locks" >> /etc/brainstore.env
                else
                  echo "BRAINSTORE_LOCKS_URI=${RedisUrl}" >> /etc/brainstore.env
                fi

                if [ -n "${InternalObservabilityApiKey}" ]; then
                  if [ -n "${InternalObservabilityEnvName}" ]; then
                    export DD_ENV="${InternalObservabilityEnvName}"
                  fi
                  DD_API_KEY="${InternalObservabilityApiKey}" \
                  DD_SITE="${InternalObservabilityRegion}.datadoghq.com" \
                  DD_APM_INSTRUMENTATION_ENABLED=host \
                  DD_APM_INSTRUMENTATION_LIBRARIES=java:1,python:3,js:5,php:1,dotnet:3 \
                  bash -c "$(curl -L https://install.datadoghq.com/scripts/install_script_agent7.sh)"
                  usermod -a -G docker dd-agent

                  # Set config options as env vars rather than complicated yaml
                  cat <<EOF > /etc/datadog-agent/environment
                DD_OTLP_CONFIG_RECEIVER_PROTOCOLS_HTTP_ENDPOINT=0.0.0.0:4318
                DD_COLLECT_EC2_TAGS=true
                DD_COLLECT_EC2_TAGS_USE_IMDS=true
                EOF
                  # Configure Datadog agent to collect logs from all containers
                  cat <<EOF >> /etc/datadog-agent/datadog.yaml
                logs_enabled: true
                listeners:
                    - name: docker
                config_providers:
                    - name: docker
                      polling: true
                logs_config:
                    container_collect_all: true
                EOF
                  # Configure Datadog agent to collect network metrics
                  cat <<EOF > /etc/datadog-agent/conf.d/network.d/network.yaml

                instances:
                  -
                    collect_connection_state: true
                    collect_connection_queues: true
                    collect_ethtool_metrics: true
                    combine_connection_states: false
                    collect_aws_ena_metrics: true
                EOF
                  systemctl restart datadog-agent

                  # Configure auto-restart for Datadog agent
                  mkdir -p /etc/systemd/system/datadog-agent.service.d
                  cat <<EOF > /etc/systemd/system/datadog-agent.service.d/override.conf
                [Service]
                Restart=always
                RestartSec=10
                EOF
                  systemctl daemon-reload
                  systemctl enable datadog-agent

                  # Configure Brainstore to send traces to Datadog
                  cat <<EOF >> /etc/brainstore.env
                BRAINSTORE_OTLP_HTTP_ENDPOINT=http://localhost:4318
                BRAINSTORE_OTLP_TELEMETRY=metrics,traces
                EOF
                fi

                echo "BRAINSTORE_CONTROL_PLANE_TELEMETRY=${MonitoringTelemetry}" >> /etc/brainstore.env

                # Add any extra environment variables
                if [ -n "${BrainstoreExtraEnvVars}" ]; then
                  echo -e "\n${BrainstoreExtraEnvVars}" | tr ',' '\n' >> /etc/brainstore.env
                fi

                # Set this after the extra env vars, because the extra env vars may set these, and we never
                # want them on for readers.
                if [[ "$INSTANCE_NAME" == *"ReaderNode"* ]]; then
                  echo "BRAINSTORE_READER_ONLY_MODE=true" >> /etc/brainstore.env
                fi

                # Always restart the writer node hourly with a random delay up to 30 minutes
                if [[ "$INSTANCE_NAME" == *"WriterNode"* ]]; then
                  echo 'SHELL=/bin/bash' > /etc/cron.d/restart-brainstore
                  echo '0 * * * * root sleep $((RANDOM % 1800)) && /usr/bin/docker restart brainstore > /var/log/brainstore-restart.log 2>&1' >> /etc/cron.d/restart-brainstore
                fi

                cat <<'EOF' > /root/start-brainstore.sh
                #!/bin/bash

                # Note: This value will be replaced with a specific version when making a release
                BRAINSTORE_RELEASE_VERSION=latest
                BRAINSTORE_VERSION_OVERRIDE=${BrainstoreVersionOverride}
                BRAINSTORE_VERSION=${!BRAINSTORE_VERSION_OVERRIDE:-${!BRAINSTORE_RELEASE_VERSION}}

                docker run -d \
                  --network host \
                  --name brainstore \
                  --env-file /etc/brainstore.env \
                  --restart always \
                  -v /mnt/tmp/brainstore:/mnt/tmp/brainstore \
                  public.ecr.aws/braintrust/brainstore:${!BRAINSTORE_VERSION} \
                  web
                EOF
                chmod +x /root/start-brainstore.sh
                /root/start-brainstore.sh

              - DatabaseHost:
                  Fn::If:
                    - UsePostgresAlternativeHost
                    - Ref: PostgresAlternativeHost
                    - Fn::If:
                        - UseManagedPostgres
                        - !Sub "${Database.Endpoint.Address}:${Database.Endpoint.Port}"
                        - ""
                RedisUrl:
                  Fn::If:
                    - UseRedisAlternativeUrl
                    - Ref: RedisAlternativeUrl
                    - Fn::If:
                        - UseManagedRedis
                        - !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}"
                        - ""
  BrainstoreAutoScalingGroup:
    Type: AWS::AutoScaling::AutoScalingGroup
    Condition: UseBrainstore
    Properties:
      LaunchTemplate:
        LaunchTemplateId:
          Ref: BrainstoreLaunchTemplate
        Version:
          Fn::GetAtt:
            - BrainstoreLaunchTemplate
            - LatestVersionNumber
      MinSize: !Ref BrainstoreAutoscalingMinInstanceCount
      MaxSize: !Ref BrainstoreAutoscalingMaxInstanceCount
      VPCZoneIdentifier:
        - Ref: privateSubnet1
        - Ref: privateSubnet2
        - Ref: privateSubnet3
      HealthCheckType: ELB,EBS
      TargetGroupARNs:
        - Ref: BrainstoreTargetGroup
      Tags:
        - Key: Name
          Value: !If
            - HasBrainstoreWriterInstances
            - !Sub "${AWS::StackName}-BrainstoreReaderNode"
            - !Sub "${AWS::StackName}-BrainstoreNode"
          PropagateAtLaunch: "true"
        - Key: BrainstoreRole
          Value: !If
            - HasBrainstoreWriterInstances
            - Reader
            - ReaderWriter
          PropagateAtLaunch: "true"
      InstanceMaintenancePolicy:
        MinHealthyPercentage: 100
        MaxHealthyPercentage: 200
    UpdatePolicy:
      AutoScalingRollingUpdate:
        # We want to update all instances as fast as possible, while still keeping some instances available on the current version.
        MinInstancesInService: !Ref BrainstoreAutoscalingMinInstanceCount
        MaxBatchSize: !Ref BrainstoreAutoscalingMaxInstanceCount
        # Suspend processes that may cause changes in instance counts and break the update
        SuspendProcesses:
          - AZRebalance
          - AlarmNotification
          - InstanceRefresh
        PauseTime: PT1M
  BrainstoreTargetTrackingPolicy:
    Type: AWS::AutoScaling::ScalingPolicy
    Condition: UseBrainstore
    Properties:
      PolicyName: !Sub "${AWS::StackName}-brainstore-target-tracking"
      AutoScalingGroupName: !Ref BrainstoreAutoScalingGroup
      PolicyType: TargetTrackingScaling
      TargetTrackingConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ASGAverageCPUUtilization
        TargetValue: !Ref BrainstoreAutoscalingCpuTargetValue
  BrainstoreNLB:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Condition: UseBrainstore
    Properties:
      Type: network
      Scheme: internal
      Subnets:
        - Ref: privateSubnet1
        - Ref: privateSubnet2
        - Ref: privateSubnet3
      SecurityGroups:
        - Fn::GetAtt:
            - pubPrivateVPC
            - DefaultSecurityGroup
      Tags:
        - Key: BrainstoreRole
          Value: Reader
        - Key: stack-name
          Value: !Ref AWS::StackName
  BrainstoreTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Condition: UseBrainstore
    Properties:
      VpcId:
        Ref: pubPrivateVPC
      Port: 4000
      Protocol: TCP
      TargetType: instance
      HealthCheckEnabled: true
      HealthCheckProtocol: HTTP
      HealthCheckPath: /
      Matcher:
        HttpCode: "200-399"
      HealthCheckPort: traffic-port
      HealthCheckIntervalSeconds: 15
      HealthCheckTimeoutSeconds: 10
      HealthyThresholdCount: 3
      UnhealthyThresholdCount: 2
      Tags:
        - Key: BrainstoreRole
          Value: Reader
  BrainstoreListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Condition: UseBrainstore
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn:
            Ref: BrainstoreTargetGroup
      LoadBalancerArn:
        Ref: BrainstoreNLB
      Port: 4000
      Protocol: TCP

  # This is copy/pasted from BrainstoreAutoScalingGroup, so make sure to propagate updates here
  BrainstoreWriterAutoScalingGroup:
    Type: AWS::AutoScaling::AutoScalingGroup
    Condition: HasBrainstoreWriterInstances
    Properties:
      MixedInstancesPolicy:
        LaunchTemplate:
          LaunchTemplateSpecification:
            LaunchTemplateId:
              Ref: BrainstoreLaunchTemplate
            Version:
              Fn::GetAtt:
                - BrainstoreLaunchTemplate
                - LatestVersionNumber
          Overrides:
            - InstanceType: !Ref BrainstoreWriterInstanceType
      MinSize: !Ref BrainstoreWriterAutoscalingMinInstanceCount
      MaxSize: !Ref BrainstoreWriterAutoscalingMaxInstanceCount
      VPCZoneIdentifier:
        - Ref: privateSubnet1
        - Ref: privateSubnet2
        - Ref: privateSubnet3
      HealthCheckType: ELB,EBS
      TargetGroupARNs:
        - Ref: BrainstoreWriterTargetGroup
      Tags:
        - Key: Name
          Value:
            Fn::Sub: ${AWS::StackName}-BrainstoreWriterNode
          PropagateAtLaunch: "true"
        - Key: BrainstoreRole
          Value: Writer
          PropagateAtLaunch: "true"
      InstanceMaintenancePolicy:
        MinHealthyPercentage: 100
        MaxHealthyPercentage: 200
    UpdatePolicy:
      AutoScalingRollingUpdate:
        # We want to update all instances as fast as possible, while still keeping some instances available on the current version.
        MinInstancesInService: !Ref BrainstoreWriterAutoscalingMinInstanceCount
        MaxBatchSize: !Ref BrainstoreWriterAutoscalingMaxInstanceCount
        # Suspend processes that may cause changes in instance counts and break the update
        SuspendProcesses:
          - AZRebalance
          - AlarmNotification
          - InstanceRefresh
        PauseTime: PT1M
  BrainstoreWriterNLB:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Condition: HasBrainstoreWriterInstances
    Properties:
      Type: network
      Scheme: internal
      Subnets:
        - Ref: privateSubnet1
        - Ref: privateSubnet2
        - Ref: privateSubnet3
      SecurityGroups:
        - Fn::GetAtt:
            - pubPrivateVPC
            - DefaultSecurityGroup
      Tags:
        - Key: BrainstoreRole
          Value: Writer
        - Key: stack-name
          Value: !Ref AWS::StackName
  BrainstoreWriterTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Condition: HasBrainstoreWriterInstances
    Properties:
      VpcId:
        Ref: pubPrivateVPC
      Port: 4000
      Protocol: TCP
      TargetType: instance
      HealthCheckEnabled: true
      HealthCheckProtocol: HTTP
      HealthCheckPath: /
      Matcher:
        HttpCode: "200-399"
      HealthCheckPort: traffic-port
      HealthCheckIntervalSeconds: 15
      HealthCheckTimeoutSeconds: 10
      HealthyThresholdCount: 3
      UnhealthyThresholdCount: 2
      Tags:
        - Key: BrainstoreRole
          Value: Writer
  BrainstoreWriterTargetTrackingPolicy:
    Type: AWS::AutoScaling::ScalingPolicy
    Condition: HasBrainstoreWriterInstances
    Properties:
      PolicyName: !Sub "${AWS::StackName}-brainstore-writer-target-tracking"
      AutoScalingGroupName: !Ref BrainstoreWriterAutoScalingGroup
      PolicyType: TargetTrackingScaling
      TargetTrackingConfiguration:
        PredefinedMetricSpecification:
          PredefinedMetricType: ASGAverageCPUUtilization
        TargetValue: !Ref BrainstoreWriterAutoscalingCpuTargetValue
  BrainstoreWriterListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Condition: HasBrainstoreWriterInstances
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn:
            Ref: BrainstoreWriterTargetGroup
      LoadBalancerArn:
        Ref: BrainstoreWriterNLB
      Port: 4000
      Protocol: TCP

  # Clickhouse
  ClickhouseSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Description: "This secret stores a password"
      GenerateSecretString:
        SecretStringTemplate: "{}"
        GenerateStringKey: "password"
        PasswordLength: 16
        ExcludeCharacters: "\"@/\\|&"
        ExcludePunctuation: true

  ClickhouseS3Bucket:
    Type: AWS::S3::Bucket
    Condition: UseManagedClickhouse
    Properties:
      BucketName: !If
        - ClickhouseBucketGlobalNameSpecified
        - !Sub "${AWS::StackName}-ch-storage"
        - !Join
          - "-"
          - - !Sub "${AWS::StackName}-ch-storage"
            - !Select
              - 0
              - !Split
                - "-"
                - !Select
                  - 2
                  - !Split
                    - "/"
                    - !Ref "AWS::StackId"
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256

  ClickhouseSecretEC2Role:
    Type: AWS::IAM::Role
    Condition: UseManagedClickhouse
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: AccessSecret
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: secretsmanager:GetSecretValue
                Resource: !Ref ClickhouseSecret
        - PolicyName: AccessS3Bucket
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action: s3:*
                Resource:
                  - !Sub "arn:aws:s3:::${ClickhouseS3Bucket}"
                  - !Sub "arn:aws:s3:::${ClickhouseS3Bucket}/*"

  ClickhouseSecretEC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Condition: UseManagedClickhouse
    Properties:
      Roles:
        - !Ref ClickhouseSecretEC2Role

  ClickhouseLaunchConfiguration:
    Type: AWS::AutoScaling::LaunchConfiguration
    Condition: UseManagedClickhouse
    DependsOn:
      - ClickhouseSecret
    Properties:
      ImageId: !Ref ClickhouseLinuxAMI
      InstanceType: !Ref ClickhouseInstanceType
      IamInstanceProfile: !Ref ClickhouseSecretEC2InstanceProfile
      KeyName: !If
        - ClickhouseKeyPairSpecified
        - !Ref ClickhouseInstanceKeyPairName
        - !Ref "AWS::NoValue"
      SecurityGroups: [!GetAtt pubPrivateVPC.DefaultSecurityGroup]
      BlockDeviceMappings:
        - DeviceName: "/dev/xvda"
          Ebs:
            VolumeSize: 128 # Root volume size in GiB
            VolumeType: "gp2"
        - DeviceName: "/dev/sdb"
          Ebs:
            VolumeSize: !Ref ClickhouseMetadataStorageSize
            VolumeType: "gp2" # TODO: Maybe make this configurable?
            Encrypted: "true"
      MetadataOptions:
        HttpTokens: "required"
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          # Wait for the NVMe device to appear
          while [ ! -e /dev/nvme1n1 ]; do
            echo "Waiting for /dev/nvme1n1 to appear"
            sleep 1
          done
          # Format and mount the EBS volume
          mkdir -p /var/lib/clickhouse
          # Check if the device is already formatted
          if ! blkid /dev/nvme1n1; then
            mkfs -t ext4 /dev/nvme1n1
          fi
          mount /dev/nvme1n1 /var/lib/clickhouse
          # Ensure the volume is remounted automatically after a reboot
          echo '/dev/nvme1n1 /var/lib/clickhouse ext4 defaults,nofail 0 2' >> /etc/fstab

          echo "export S3_BUCKET_NAME=${ClickhouseS3Bucket}" >> /etc/environment

          export AWS_DEFAULT_REGION=${AWS::Region}
          export CLICKHOUSE_PASSWORD=$(aws secretsmanager get-secret-value --secret-id ${ClickhouseSecret} --query SecretString --output text)
          echo ${!CLICKHOUSE_PASSWORD} > /tmp/password

          yum install -y yum-utils jq
          yum-config-manager --add-repo https://packages.clickhouse.com/rpm/clickhouse.repo

          yum install -y clickhouse-server clickhouse-client

          PASSWORD=$(echo $CLICKHOUSE_PASSWORD | jq .password --raw-output | tr -d '\n')

          # For good measure (https://stackoverflow.com/questions/407523/escape-a-string-for-a-sed-replace-pattern)
          ESCAPED_PASSWORD=$(printf '%s\n' "$PASSWORD" | sed -e 's/[]\/$*.^[]/\\&/g')

          # Replace <password></password> in /etc/clickhouse-server/users.xml with  <password>new password</password>
          sed -i "s|<password></password>|<password>${!ESCAPED_PASSWORD}</password>|" /etc/clickhouse-server/users.xml

          # Replace <!-- <listen_host>0.0.0.0</listen_host> --> with <listen_host>0.0.0.0</listen_host>
          sed -i 's|<!-- <listen_host>0\.0\.0\.0</listen_host> -->|<listen_host>0.0.0.0</listen_host>|' /etc/clickhouse-server/config.xml

          # Create the storage config file
          mkdir -p /etc/clickhouse-server/config.d
          cat <<EOF > /etc/clickhouse-server/config.d/storage_config.xml
          <clickhouse>
            <storage_configuration>
              <disks>
                <s3_disk>
                  <type>s3</type>
                  <endpoint>https://${ClickhouseS3Bucket}.s3.${AWS::Region}.amazonaws.com/tables/</endpoint>
                  <metadata_path>/var/lib/clickhouse/disks/s3/</metadata_path>
                </s3_disk>
                <s3_cache>
                  <type>cache</type>
                  <disk>s3_disk</disk>
                  <path>/var/lib/clickhouse/disks/s3_cache/</path>
                  <max_size>100Gi</max_size>
                </s3_cache>
              </disks>
              <policies>
                <s3_main>
                  <volumes>
                    <main>
                      <disk>s3_disk</disk>
                    </main>
                  </volumes>
                </s3_main>
              </policies>
            </storage_configuration>
          </clickhouse>
          EOF

          chown clickhouse:clickhouse /etc/clickhouse-server/config.d/storage_config.xml

          systemctl enable clickhouse-server
          systemctl start clickhouse-server

  ClickhouseAutoScalingGroup:
    Type: AWS::AutoScaling::AutoScalingGroup
    Condition: UseManagedClickhouse
    Properties:
      LaunchConfigurationName: !Ref ClickhouseLaunchConfiguration
      MinSize: "1"
      MaxSize: "1"
      DesiredCapacity: "1"
      VPCZoneIdentifier:
        - !Ref privateSubnet1
        - !Ref privateSubnet2
        - !Ref privateSubnet3
      Tags:
        - Key: Name
          Value: !Sub "${AWS::StackName}-ClickhouseNode"
          PropagateAtLaunch: "true"
      TargetGroupARNs:
        - !Ref ClickhousePGTargetGroup
        - !Ref ClickhouseConnectTargetGroup

  ClickhouseNLB:
    Type: "AWS::ElasticLoadBalancingV2::LoadBalancer"
    Condition: UseManagedClickhouse
    Properties:
      Type: network
      Scheme: internal
      Subnets:
        - !Ref privateSubnet1
        - !Ref privateSubnet2
        - !Ref privateSubnet3
      SecurityGroups: [!GetAtt pubPrivateVPC.DefaultSecurityGroup]

  ClickhousePGTargetGroup:
    Type: "AWS::ElasticLoadBalancingV2::TargetGroup"
    Condition: UseManagedClickhouse
    Properties:
      VpcId: !Ref pubPrivateVPC
      Port: 9005
      Protocol: TCP
      TargetType: instance

  ClickhouseConnectTargetGroup:
    Type: "AWS::ElasticLoadBalancingV2::TargetGroup"
    Condition: UseManagedClickhouse
    Properties:
      VpcId: !Ref pubPrivateVPC
      Port: 8123
      Protocol: TCP
      TargetType: instance

  ClickhousePGListener:
    Type: "AWS::ElasticLoadBalancingV2::Listener"
    Condition: UseManagedClickhouse
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref ClickhousePGTargetGroup
      LoadBalancerArn: !Ref ClickhouseNLB
      Port: 9005
      Protocol: TCP

  ClickhouseConnectListener:
    Type: "AWS::ElasticLoadBalancingV2::Listener"
    Condition: UseManagedClickhouse
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref ClickhouseConnectTargetGroup
      LoadBalancerArn: !Ref ClickhouseNLB
      Port: 8123
      Protocol: TCP

  # Migration functions
  MigrateDatabaseFunction:
    Type: AWS::Serverless::Function
    # Condition: UseManagedPostgres
    DependsOn:
      - DatabaseSecret
      - ClickhouseSecret
    Properties:
      Description: Migrates the schema in the database
      Handler: lambda_function.lambda_handler
      MemorySize: 1024
      CodeUri: ../../api-schema/deployment.zip
      Runtime: python3.13
      Role: !GetAtt DefaultRole.Arn
      Timeout: 900
      AutoPublishAlias: live2
      Environment:
        Variables:
          # Replace the hardcoded value with the parameter
          BRAINTRUST_RUN_DRAFT_MIGRATIONS: !Ref RunDraftMigrations
          INSERT_LOGS2: !Ref InsertLogs2
          LOG_LEVEL: !Ref LogLevel
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
            ]
          CLICKHOUSE_CONNECT_URL:
            !If [
              UseManagedClickhouse,
              !Sub [
                "http://default:{{resolve:secretsmanager:${ClickhouseSecret}::password}}@${DNSName}:8123/default",
                DNSName: !GetAtt ClickhouseNLB.DNSName,
              ],
              "",
            ]
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3

  QuarantineWarmupFunction:
    Type: AWS::Serverless::Function
    Condition: HasQuarantine
    Properties:
      CodeUri: ../../api-ts/dist/lambda-quarantine/warmup-lambda/index.zip
      Runtime: nodejs20.x
      Environment:
        Variables:
          ORG_NAME:
            Ref: OrgName
          PG_URL:
            !If [
              UsePostgresAlternativeHost,
              !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${PostgresAlternativeHost}/postgres",
              !If [
                UseManagedPostgres,
                !Sub "postgres://{{resolve:secretsmanager:${DatabaseSecret}::username}}:{{resolve:secretsmanager:${DatabaseSecret}::password}}@${Database.Endpoint.Address}:${Database.Endpoint.Port}/postgres",
                "",
              ],
            ]
          REDIS_URL:
            !If [
              UseRedisAlternativeUrl,
              !Ref RedisAlternativeUrl,
              !If [
                UseManagedRedis,
                !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}",
                "",
              ],
            ]
          QUARANTINE_INVOKE_ROLE:
            !If [HasQuarantine, !GetAtt QuarantineInvokeRole.Arn, ""]
          QUARANTINE_FUNCTION_ROLE:
            !If [HasQuarantine, !GetAtt QuarantineFunctionRole.Arn, ""]
          QUARANTINE_PRIVATE_SUBNET_1_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet1, ""]
          QUARANTINE_PRIVATE_SUBNET_2_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet2, ""]
          QUARANTINE_PRIVATE_SUBNET_3_ID:
            !If [HasQuarantine, !Ref QuarantineprivateSubnet3, ""]
          QUARANTINE_PUB_PRIVATE_VPC_DEFAULT_SECURITY_GROUP:
            !If [
              HasQuarantine,
              !GetAtt QuarantinepubPrivateVPC.DefaultSecurityGroup,
              "",
            ]
          QUARANTINE_PUB_PRIVATE_VPC_ID:
            !If [HasQuarantine, !Ref QuarantinepubPrivateVPC, ""]
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3
      Handler: index.handler
      MemorySize: 1024
      Role: !GetAtt APIHandlerRole.Arn
      Timeout: 900
      Tracing: PassThrough

  InitializePrimerRole:
    Type: "AWS::IAM::Role"
    # Condition: UseManagedPostgres
    Properties:
      AssumeRolePolicyDocument:
        Statement:
          - Action: sts:AssumeRole
            Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Sid: ""
        Version: "2012-10-17"
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
      # Somehow, Chalice seems to screw this up and give the lambda function permissios
      # to either kafka, or redis, but not both. So we have to manually add the permissions here.
      Policies:
        - PolicyDocument:
            Statement:
              - Action:
                  - lambda:InvokeFunction
                Effect: Allow
                Resource:
                  - !Ref MigrateDatabaseFunction.Version
                  - Fn::If:
                      - HasQuarantine
                      - Fn::GetAtt:
                          - QuarantineWarmupFunction
                          - Arn
                      - Fn::Sub: arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:nonExistentFunctionBraintrust
            Version: "2012-10-17"
          PolicyName: DefaultRolePolicy
  # https://docs.aws.amazon.com/lambda/latest/dg/services-cloudformation.html
  InitializePrimerFunction:
    Type: AWS::Serverless::Function
    # Condition: UseManagedPostgres
    Properties:
      Handler: index.handler
      Runtime: nodejs22.x
      InlineCode: |
        const response = require('cfn-response');

        exports.handler = async function(event, context) {
            // For Delete requests, immediately send a SUCCESS response.
            if (event.RequestType == "Delete") {
                console.log("Delete request, sending SUCCESS response");
                await response.send(event, context, response.SUCCESS, {});
                return;
            }

            var responseStatus = response.FAILED;
            var responseData = {};
            var functionName = event.ResourceProperties.FunctionName

            try {
                const { LambdaClient, InvokeCommand } = await import('@aws-sdk/client-lambda');
                const lambda = new LambdaClient();
                const command = new InvokeCommand({
                    FunctionName: functionName,
                    InvocationType: 'RequestResponse'
                });
                console.log("Invoking lambda function: " + functionName);
                await lambda.send(command);
                responseStatus = response.SUCCESS;
            } catch (err) {
                responseData = {Error: "Invoke call failed"};
                console.log(responseData.Error + ":\n", err);
            }
            console.log("Response status: " + responseStatus);
            await response.send(event, context, responseStatus, responseData);
            console.log("Response sent successfully");
            return;
        };
      MemorySize: 128
      Timeout: 900
      Role: !GetAtt InitializePrimerRole.Arn
      Tracing: Active
      VpcConfig:
        SecurityGroupIds:
          - !GetAtt pubPrivateVPC.DefaultSecurityGroup
        SubnetIds:
          # Only connect to the private subnets because otherwise the lambda function cannot access the internet
          # https://stackoverflow.com/questions/52992085/why-cant-an-aws-lambda-function-inside-a-public-subnet-in-a-vpc-connect-to-the#:~:text=The%20difference%20between%20public%20and,default%20route%20is%20the%20IGW.
          - !Ref privateSubnet1
          - !Ref privateSubnet2
          - !Ref privateSubnet3

  CallMigrateDatabase:
    Type: AWS::CloudFormation::CustomResource
    Condition: UseManagedPostgres
    DependsOn:
      - Database
      - InitializePrimerFunction
      - InitializePrimerRole
      - MigrateDatabaseFunction
    Version: "1.0"
    Properties:
      ServiceToken: !GetAtt InitializePrimerFunction.Arn
      FunctionName: !Ref MigrateDatabaseFunction.Version
  CallMigrateDatabaseAlt:
    Type: AWS::CloudFormation::CustomResource
    Condition: UsePostgresAlternativeHost
    DependsOn:
      - InitializePrimerFunction
      - InitializePrimerRole
      - MigrateDatabaseFunction
    Version: "1.0"
    Properties:
      ServiceToken: !GetAtt InitializePrimerFunction.Arn
      FunctionName: !Ref MigrateDatabaseFunction.Version

  CallWarmupQuarantine:
    Type: AWS::CloudFormation::CustomResource
    Condition: HasQuarantine
    DependsOn:
      - InitializePrimerFunction
      - InitializePrimerRole
      - QuarantineWarmupFunction
    Version: "1.0"
    Properties:
      ServiceToken: !GetAtt InitializePrimerFunction.Arn
      FunctionName: !GetAtt QuarantineWarmupFunction.Arn

  BastionInstanceLaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Condition: HasBraintrustSupportShellAccess
    Properties:
      LaunchTemplateName: !Join ["-", [!Ref "AWS::StackName", "bastion"]]
      LaunchTemplateData:
        ImageId: !Sub "{{resolve:ssm:/aws/service/canonical/ubuntu/server/22.04/stable/current/arm64/hvm/ebs-gp2/ami-id}}"
        InstanceType: "t4g.medium"
        IamInstanceProfile:
          Name: !Ref BastionInstanceProfile
        NetworkInterfaces:
          - AssociatePublicIpAddress: false
            DeviceIndex: "0"
            SubnetId: !Ref privateSubnet1
            Groups:
              - Fn::GetAtt:
                  - pubPrivateVPC
                  - DefaultSecurityGroup
              - !Ref BastionSSHSecurityGroup
        BlockDeviceMappings:
          - DeviceName: /dev/sda1
            Ebs:
              Encrypted: true
              DeleteOnTermination: true
              VolumeSize: 50
              VolumeType: gp3
        MetadataOptions:
          HttpTokens: required
          HttpEndpoint: enabled
          HttpPutResponseHopLimit: 2
        TagSpecifications:
          - ResourceType: instance
            Tags:
              - Key: Name
                Value: !Join ["-", [!Ref "AWS::StackName", "bastion"]]
              - Key: BraintrustRole
                Value: bastion
          - ResourceType: volume
            Tags:
              - Key: Name
                Value: !Join ["-", [!Ref "AWS::StackName", "bastion"]]
              - Key: BraintrustRole
                Value: bastion
          - ResourceType: network-interface
            Tags:
              - Key: Name
                Value: !Join ["-", [!Ref "AWS::StackName", "bastion"]]
              - Key: BraintrustRole
                Value: bastion
        UserData:
          Fn::Base64:
            Fn::Sub:
              - |
                #!/bin/bash
                apt-get update
                apt-get install -y jq unzip earlyoom postgresql-client
                sudo hostnamectl set-hostname bastion

                curl "https://awscli.amazonaws.com/awscli-exe-linux-aarch64.zip" -o "awscliv2.zip"
                unzip awscliv2.zip
                ./aws/install

                export AWS_REGION=${AWS::Region}
                export AWS_DEFAULT_REGION=${AWS::Region}

                # Get database credentials from Secrets Manager
                DB_CREDS=$(aws secretsmanager get-secret-value --secret-id ${DatabaseSecret} --query SecretString --output text)
                DB_USERNAME=$(echo $DB_CREDS | jq -r .username)
                DB_PASSWORD=$(echo $DB_CREDS | jq -r .password)

                CLICKHOUSE_PG_URL=""
                if [ -n "${ClickhouseHost}" ]; then
                  CLICKHOUSE_PASSWORD=$(aws secretsmanager get-secret-value --secret-id ${ClickhouseSecret} --query SecretString --output text | jq -r .password)
                  CLICKHOUSE_PG_URL="http://default:${!CLICKHOUSE_PASSWORD}@${ClickhouseHost}:8123/default"
                fi

                cat <<EOF > /etc/braintrust.env
                export AWS_REGION=${AWS::Region}
                export AWS_DEFAULT_REGION=${AWS::Region}
                export REDIS_URL=${RedisUrl}
                export PG_URL=postgres://$DB_USERNAME:$DB_PASSWORD@${DatabaseHost}/postgres
                export CLICKHOUSE_PG_URL=${!CLICKHOUSE_PG_URL}
                EOF

                echo -e "\nsource /etc/braintrust.env\n" >> /home/<USER>/.bashrc

                cat <<'EOF' > /home/<USER>/list-instances.sh
                #!/bin/bash
                json=$(aws autoscaling describe-auto-scaling-groups --filters "Name=tag:aws:cloudformation:stack-name,Values=${AWS::StackName}")

                if [ "$1" == "--json" ]; then
                  echo "$json"
                else
                  echo "Brainstore Instances:"
                  echo "$json" | jq -r '.AutoScalingGroups[] | select(.AutoScalingGroupName | contains("Brainstore")) | .Instances[] | .InstanceId'
                  echo "Clickhouse Instances:"
                  echo "$json" | jq -r '.AutoScalingGroups[] | select(.AutoScalingGroupName | contains("Clickhouse")) | .Instances[] | .InstanceId'
                fi
                EOF

                cat <<EOF > /home/<USER>/list-functions.sh
                #!/bin/bash
                # Unfortunately we can't support "aws lambda list-functions" because it shows environment variables
                # with secrets for their entire account and not just the Braintrust stack. IAM won't let you restrict it.
                # This output is generated by Cloudformation.
                echo "${MigrateDatabaseFunction}"
                echo "${APIHandlerJS}"
                echo "${AIProxyFn}"
                echo "${InitializePrimerFunction}"
                EOF

                cat <<'EOF' > /home/<USER>/ec2-connect.sh
                #!/bin/bash

                os_user="ubuntu"
                echo "Connecting to instance $os_user@$1"
                aws ec2-instance-connect ssh \
                  --instance-id "$1" \
                  --os-user "$os_user" \
                  --connection-type direct
                EOF

                chmod +x /home/<USER>/*.sh
                chown ubuntu:ubuntu /home/<USER>/*.sh

              - DatabaseHost:
                  Fn::If:
                    - UsePostgresAlternativeHost
                    - Ref: PostgresAlternativeHost
                    - Fn::If:
                        - UseManagedPostgres
                        - !Sub "${Database.Endpoint.Address}:${Database.Endpoint.Port}"
                        - ""
                ClickhouseHost:
                  Fn::If:
                    - UseManagedClickhouse
                    - !GetAtt ClickhouseNLB.DNSName
                    - ""

                RedisUrl:
                  Fn::If:
                    - UseRedisAlternativeUrl
                    - Ref: RedisAlternativeUrl
                    - Fn::If:
                        - UseManagedRedis
                        - !Sub "redis://${ElastiCacheCluster.RedisEndpoint.Address}:${ElastiCacheCluster.RedisEndpoint.Port}"
                        - ""
  BastionSSHSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: HasBraintrustSupportShellAccess
    Properties:
      GroupDescription: Security group for SSH access to Braintrust bastion host
      GroupName: !Join ["-", [!Ref "AWS::StackName", "bastion-ssh"]]
      VpcId: !Ref pubPrivateVPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          SourceSecurityGroupId: !Ref BastionInstanceConnectSecurityGroup
      SecurityGroupEgress:
        - IpProtocol: -1
          FromPort: 0
          ToPort: 0
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value: !Join ["-", [!Ref "AWS::StackName", "bastion-ssh"]]
        - Key: BraintrustRole
          Value: bastion

  BastionInstance:
    Type: AWS::EC2::Instance
    Condition: HasBraintrustSupportShellAccess
    Properties:
      LaunchTemplate:
        LaunchTemplateId: !GetAtt BastionInstanceLaunchTemplate.LaunchTemplateId
        Version: !GetAtt BastionInstanceLaunchTemplate.LatestVersionNumber

  BastionInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Condition: HasBraintrustSupportShellAccess
    Properties:
      Roles:
        - !Ref BastionInstanceRole

  BastionInstanceRole:
    Type: AWS::IAM::Role
    Condition: HasBraintrustSupportShellAccess
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: BastionPermissions
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              # Unfortunately AWS has no way at all to restrict DescribeInstances calls
              - Effect: Allow
                Action:
                  - ec2:DescribeInstances
                  - ec2:DescribeInstanceConnectEndpoints
                  - autoscaling:DescribeAutoScalingGroups
                Resource: "*"

              - Effect: Allow
                Action:
                  - ec2-instance-connect:SendSSHPublicKey
                Resource:
                  - !Sub "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:instance/*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/aws:cloudformation:stack-name": !Ref "AWS::StackName"

              - Effect: Allow
                Action:
                  - ec2-instance-connect:OpenTunnel
                Resource:
                  - !Sub "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:instance-connect-endpoint/${BastionInstanceConnectEndpoint.Id}"

              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                  - lambda:InvokeFunctionUrl
                  - lambda:InvokeAsync
                  - lambda:GetFunction*
                Resource: "*"
                Condition:
                  StringEquals:
                    "aws:ResourceTag/aws:cloudformation:stack-name": !Ref "AWS::StackName"

              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Ref DatabaseSecret
                  - !Ref ClickhouseSecret

  # Cloudfront distribution
  DataplaneCloudfront:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Aliases: !If [HasCustomDomain, [!Ref CustomDomain], []]
        ViewerCertificate: !If
          - HasCustomDomain
          - AcmCertificateArn: !Ref CustomCertificateArn
            MinimumProtocolVersion: TLSv1.2_2021
            SslSupportMethod: sni-only
          - CloudFrontDefaultCertificate: true
        Origins:
          - Id: APIGatewayOrigin
            DomainName:
              Fn::Sub: ${RestAPI}.execute-api.${AWS::Region}.${AWS::URLSuffix}
            OriginPath: /api
            CustomOriginConfig:
              OriginProtocolPolicy: https-only
              OriginReadTimeout: !Ref OriginReadTimeout
              OriginKeepaliveTimeout: 60
            OriginCustomHeaders:
              - HeaderName: X-CloudFront-Domain
                HeaderValue:
                  !If [
                    HasCustomDomain,
                    !Ref CustomDomain,
                    !Sub "${AWS::StackName}-${AWS::AccountId}.cloudfront.net",
                  ]
          - Id: ProxyOrigin
            DomainName:
              Fn::Select:
                - 2
                - Fn::Split:
                    - "/"
                    - !GetAtt AIProxyFnUrl.FunctionUrl
            CustomOriginConfig:
              OriginProtocolPolicy: https-only
              OriginReadTimeout: !Ref OriginReadTimeout
              OriginKeepaliveTimeout: 60
          - Id: CloudflareProxy
            DomainName: braintrustproxy.com
            CustomOriginConfig:
              OriginProtocolPolicy: https-only
              # If this is too short, and the proxy is working through rate limit retries, then we'll propagate a
              # 503 Service Unavailable ERROR to the client. The proxy's rate limit max retry window is now 45
              # seconds, so this should be enough.
              OriginReadTimeout: !Ref OriginReadTimeout
              OriginKeepaliveTimeout: 60
        DefaultCacheBehavior:
          TargetOriginId: APIGatewayOrigin
          # This is the CachingDisabled policy
          # from https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/using-managed-cache-policies.html#managed-cache-policy-caching-disabled
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
          # This is the OriginRequestPolicy
          # from https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudfront-distribution-cachebehavior.html
          OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
          ViewerProtocolPolicy: redirect-to-https
          AllowedMethods:
            - GET
            - HEAD
            - OPTIONS
            - PUT
            - POST
            - PATCH
            - DELETE
        CacheBehaviors:
          - PathPattern: /v1/proxy
            TargetOriginId: !If [UseGlobalProxy, CloudflareProxy, ProxyOrigin]
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
          - PathPattern: /v1/proxy/*
            TargetOriginId: !If [UseGlobalProxy, CloudflareProxy, ProxyOrigin]
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
            # Note that /v1/function and /v1/function/{id} (and
            # /v1/function/{id}/) still need to go to the ApiGatewayOrigin.
          - PathPattern: /v1/function/*/?*
            TargetOriginId: ProxyOrigin
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
          - PathPattern: /v1/eval
            TargetOriginId: ProxyOrigin
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
          - PathPattern: /v1/eval/*
            TargetOriginId: ProxyOrigin
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
          - PathPattern: /function/*
            TargetOriginId: ProxyOrigin
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad
            OriginRequestPolicyId: b689b0a8-53d0-40ab-baf2-68738e2966ac
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - GET
              - HEAD
              - OPTIONS
              - PUT
              - POST
              - PATCH
              - DELETE
        Enabled: true
        HttpVersion: http2

  BraintrustSupportRole:
    Type: AWS::IAM::Role
    Condition: HasBraintrustSupportAccess
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              AWS: "arn:aws:iam::872608195481:root"
            Action: "sts:AssumeRole"

  BraintrustSupportLogsAccessPolicy:
    Type: AWS::IAM::RolePolicy
    Condition: HasBraintrustSupportAccess
    Properties:
      PolicyName: BraintrustSupportLogsAccess
      RoleName: !Ref BraintrustSupportRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action:
              - logs:DescribeLogStreams
              - logs:GetLogEvents
              - logs:FilterLogEvents
              - logs:StartLiveTail
              - logs:StopLiveTail
            Resource:
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/braintrust/${AWS::StackName}/*"
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/braintrust/${AWS::StackName}*:*"
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${AWS::StackName}*:*"
              - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${AWS::StackName}*"
          - Effect: Allow
            Action:
              - logs:DescribeLogGroups
            Resource: "*"

  # Create a separate Policy resource to attach session-manager permissions
  BraintrustSupportEC2InstanceConnectPolicy:
    Condition: HasBraintrustSupportShellAccess
    Type: AWS::IAM::RolePolicy
    Properties:
      PolicyName: EC2InstanceConnectBastion
      RoleName: !Ref BraintrustSupportRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action:
              - ec2:DescribeInstances
              - ec2:DescribeInstanceConnectEndpoints
            Resource: "*"

          - Effect: Allow
            Action:
              - ec2-instance-connect:SendSSHPublicKey
            Resource:
              - !Sub "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:instance/${BastionInstance.InstanceId}"

          - Effect: Allow
            Action:
              - ec2-instance-connect:OpenTunnel
            Resource:
              - !Sub "arn:aws:ec2:${AWS::Region}:${AWS::AccountId}:instance-connect-endpoint/${BastionInstanceConnectEndpoint.Id}"

  BastionInstanceConnectSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Condition: HasBraintrustSupportShellAccess
    Properties:
      GroupDescription: Security group for EC2 Instance Connect Endpoint
      GroupName:
        !Join ["-", [!Ref "AWS::StackName", "instance-connect-endpoint"]]
      VpcId: !Ref pubPrivateVPC
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: 0.0.0.0/0
      Tags:
        - Key: Name
          Value:
            !Join ["-", [!Ref "AWS::StackName", "instance-connect-endpoint"]]
        - Key: BraintrustRole
          Value: instance-connect-endpoint

  BastionInstanceConnectEndpoint:
    Type: AWS::EC2::InstanceConnectEndpoint
    Condition: HasBraintrustSupportShellAccess
    Properties:
      SubnetId: !Ref publicSubnet1
      SecurityGroupIds:
        - !Ref BastionInstanceConnectSecurityGroup
      PreserveClientIp: true
      Tags:
        - Key: Name
          Value:
            !Join ["-", [!Ref "AWS::StackName", "instance-connect-endpoint"]]
        - Key: BraintrustRole
          Value: instance-connect-endpoint

Outputs:
  EndpointURL:
    Description: (Deprecated) the API endpoint URL. You do not need to use this URL unless you are debugging something
  UniversalURL:
    Description: Universal API URL
    Value: !Sub "https://${DataplaneCloudfront.DomainName}"
  ProxyURL:
    Description: Custom Proxy URL
    Value: !Join ["", [!GetAtt AIProxyFnUrl.FunctionUrl]]
  pubPrivateVPCID:
    Description: VPC ID
    Value: !Ref pubPrivateVPC
    Export:
      Name: !Join ["-", [!Ref "AWS::StackName", "vpc"]]
  publicSubnet1ID:
    Description: Public Subnet A ID
    Value: !Ref publicSubnet1
    Export:
      Name: !Join ["-", [!Ref "AWS::StackName", "public-subnet-a"]]
  privateSubnet1ID:
    Description: Private Subnet A ID
    Value: !Ref privateSubnet1
    Export:
      Name: !Join ["-", [!Ref "AWS::StackName", "private-subnet-a"]]
  privateSubnet2ID:
    Description: Private Subnet B ID
    Value: !Ref privateSubnet2
    Export:
      Name: !Join ["-", [!Ref "AWS::StackName", "private-subnet-b"]]
  privateSubnet3ID:
    Description: Private Subnet C ID
    Value: !Ref privateSubnet3
    Export:
      Name: !Join ["-", [!Ref "AWS::StackName", "private-subnet-c"]]
  privateVPCSecurityGroup:
    Description: Default security for Lambda VPC
    Value: !GetAtt pubPrivateVPC.DefaultSecurityGroup
    Export:
      Name: !Join ["-", [!Ref "AWS::StackName", "vpc-sg"]]
  APIHandlerJSArn:
    Value:
      Fn::GetAtt:
        - APIHandlerJS
        - Arn
  APIHandlerJSName:
    Value:
      Ref: APIHandlerJS
  AIProxyFnName:
    Value:
      Ref: AIProxyFn
  CustomDomain:
    Value:
      Ref: CustomDomain
  BraintrustSupportRole:
    Condition: HasBraintrustSupportAccess
    Description: "If enabled, ARN of the Role that grants Braintrust team remote support. Share this with the Braintrust team."
    Value: !GetAtt BraintrustSupportRole.Arn
  BraintrustSupportBastionInstanceID:
    Condition: HasBraintrustSupportShellAccess
    Description: "Instance ID of the bastion host that Braintrust support staff can connect to using EC2 Instance Connect. Share this with the Braintrust team."
    Value: !GetAtt BastionInstance.InstanceId

unpivot: scores AS (score, value) | select: id, score, value;
unpivot: scores AS (score, value) | select: score;
unpivot: scores AS (score, value) | select: value;
unpivot: scores AS (score, value) | select: id;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: scores AS (score, value) | dimensions: score | measures: sum(value);

unpivot: tags AS tag | select: tag;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: tags AS tag | dimensions: tag | measures: count(1);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 0
*/
unpivot: scores AS (score, value), tags AS tag | select: score, tag;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 0
*/
unpivot: scores AS (score, value), tags AS tag | select: score;

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 0
*/
unpivot: scores AS (score, value), tags AS tag | dimensions: score, tag | measures: sum(value), count(1);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: scores AS (score, value) | measures: count(value);

-- Test exists optimization
/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: scores AS (score, value) | dimensions: score | measures: count(value);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: scores as (score, value) | dimensions: id, score | measures: avg(value);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: scores as (score, value) | dimensions: id, score | measures: avg(value + metadata.float);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 1
*/
unpivot: scores as (score, value) | dimensions: score, value | measures: sum(counts.foo);

/*!execution -- Expect no Dynamic segment collector nodes
  [.. | objects | select(.name == "Generating unpivot rows")] | length == 0
*/
unpivot: scores as (score, value) | dimensions: score, value, id | measures: sum(counts.foo);

unpivot: scores.noexist AS (score, value) | dimensions: score | measures: count(value);
unpivot: scores.foo as (score, value) | dimensions: score | measures: count(value);
unpivot: tags as (score, value) | dimensions: score;

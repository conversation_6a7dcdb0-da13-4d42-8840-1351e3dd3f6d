"use client";

import { BillingForm } from "./billing-form";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, type Stripe } from "@stripe/stripe-js";
import { useSearchParams } from "next/navigation";
import { type Permission } from "@braintrust/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { useDarkMode } from "#/utils/useDarkMode";
import { useMemo, useEffect } from "react";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useOrg } from "#/utils/user";
import { type EventProps } from "#/analytics/events";
import { getPageName } from "#/utils/analytics";
import { useAnalytics } from "#/ui/use-analytics";

// Import the enum arrays for validation
const upgradeClickEntryPoints = [
  "header",
  "usageWarning",
  "pricingTable",
  "emailNotification",
  "customerPortal",
  "billingPage",
  "other",
] as const;

const upgradeClickDestinations = [
  "billingPage",
  "upgradeModal",
  "paymentPage",
] as const;

let stripePromise: Promise<Stripe | null> | null = null;
if (process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
}

function ClientPage({
  orgPermissions,
  flowId,
}: {
  orgPermissions: Permission[];
  flowId: string;
}) {
  const searchParams = useSearchParams();
  const purchasePlanSlug = searchParams?.get("purchasePlanSlug");
  const { track } = useAppAnalytics();
  const org = useOrg();

  // Track page view
  useAnalytics({ page: { category: "checkout" } });

  // Track email clicks when users land on payment page
  useEffect(() => {
    const source = searchParams?.get("source");
    const entryPoint = searchParams?.get("entryPoint");
    const destination = searchParams?.get("destination");

    if (source === "email" && entryPoint && destination) {
      // Validate against allowed enum values
      const isValidEntryPoint = upgradeClickEntryPoints.includes(
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
        entryPoint as any,
      );
      const isValidDestination = upgradeClickDestinations.includes(
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
        destination as any,
      );

      if (isValidEntryPoint && isValidDestination) {
        const eventData: EventProps<"upgradeClick"> = {
          orgName: org?.name ?? "",
          orgId: org?.id ?? "",
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          entryPoint: entryPoint as EventProps<"upgradeClick">["entryPoint"],
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          destination: destination as EventProps<"upgradeClick">["destination"],
          destinationUrl: window.location.href,
          context: "email_click",
          sourcePage:
            typeof window !== "undefined"
              ? getPageName(window.location.pathname)
              : "",
        };
        track("upgradeClick", eventData);
      } else {
        console.warn("Invalid upgrade click parameters:", {
          entryPoint,
          destination,
        });
      }
    }
  }, [track, org?.name, org?.id, searchParams]);

  const stripeTheme = useStripeTheme();

  if (!stripePromise) {
    return (
      <div>
        Stripe not initialized, set the NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
        environment variable
      </div>
    );
  }

  const isAllowedToPay = orgPermissions.includes("update");
  if (!isAllowedToPay) {
    return (
      <TableEmptyState label='To enter a payment method, ask your administrator to grant the "Manage settings" permission for this organization.' />
    );
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        mode: "setup",
        currency: "usd",
        setupFutureUsage: "off_session",
        paymentMethodTypes: ["card"],
        appearance: {
          theme: "stripe",
          variables: stripeTheme,
          rules: {
            ".Label": {
              marginBottom: "8px",
              marginTop: "8px",
            },
          },
        },
      }}
    >
      <BillingForm
        purchasePlanSlug={purchasePlanSlug ?? undefined}
        flowId={flowId}
      />
    </Elements>
  );
}

export interface Params {
  org: string;
}

export { ClientPage };

const useStripeTheme = () => {
  const isDarkMode = useDarkMode();

  const stripeTheme = useMemo(() => {
    // Get computed values from CSS variables
    const root = document.documentElement;
    const computedStyle = getComputedStyle(root);
    return {
      colorBackground: `rgb(${computedStyle.getPropertyValue("--primary-100").trim()})`,
      colorText: `rgb(${computedStyle.getPropertyValue("--primary-900").trim()})`,
      colorPrimary: `rgb(${computedStyle.getPropertyValue("--accent-500").trim()})`,
      colorDanger: `rgb(${computedStyle.getPropertyValue("--bad-700").trim()})`,
      fontSizeSm: computedStyle.getPropertyValue("--text-xs").trim(),
      fontFamily: "'Inter', sans-serif",
      spacingUnit: "3px",
    };
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDarkMode]);

  return stripeTheme;
};

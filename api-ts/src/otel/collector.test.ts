import { describe, expect, test } from "vitest";
import { otelTraceToRows } from "./collector";

describe("otel collector metrics", () => {
  test("should process spans and emit metrics without errors", () => {
    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                {
                  traceId: new Uint8Array([
                    0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22,
                    0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
                  ]),
                  spanId: new Uint8Array([
                    0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11,
                  ]),
                  name: "test_span",
                  startTimeUnixNano: "1640995200000000000",
                  endTimeUnixNano: "1640995201500000000",
                  attributes: [
                    {
                      key: "braintrust.input_json",
                      value: { stringValue: JSON.stringify("test input") },
                    },
                    {
                      key: "braintrust.output_json",
                      value: { stringValue: JSON.stringify("test output") },
                    },
                  ],
                  status: { code: 1 },
                },
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project");

    // Verify the basic processing worked
    expect(result.rowsByParent.size).toBe(1);
    expect(result.rejectedSpans).toHaveLength(0);

    // Verify we have processed a row with braintrust data
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(1);
    expect(rows[0]).toMatchObject({
      input: [{ role: "user", content: "test input" }],
      output: [{ role: "assistant", content: "test output" }],
    });
  });

  test("should process spans without default parent and reject spans appropriately", () => {
    const traceWithoutParent = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                {
                  traceId: new Uint8Array([
                    0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22,
                    0x33, 0x44, 0x55, 0x66, 0x77, 0x88,
                  ]),
                  spanId: new Uint8Array([
                    0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11,
                  ]),
                  name: "test_span",
                  startTimeUnixNano: "1640995200000000000",
                  endTimeUnixNano: "1640995201500000000",
                  attributes: [
                    {
                      key: "braintrust.input_json",
                      value: { stringValue: JSON.stringify("test input") },
                    },
                  ],
                  status: { code: 1 },
                },
              ],
            },
          ],
        },
      ],
    };

    // Without a default parent, spans should be rejected
    const result = otelTraceToRows(traceWithoutParent); // no default parent

    expect(result.rowsByParent.size).toBe(0);
    expect(result.rejectedSpans).toHaveLength(1);
    expect(result.rejectedSpans[0].error).toContain("No parent specified");
  });
});

describe("otel collector filtering", () => {
  // Helper to create a test span with optional attributes
  const createSpan = (
    name: string,
    attributes: Array<{ key: string; value: any }> = [],
    parentSpanId?: Uint8Array,
  ) => {
    return {
      traceId: new Uint8Array([
        0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22, 0x33, 0x44,
        0x55, 0x66, 0x77, 0x88,
      ]),
      spanId: new Uint8Array([
        Math.random() * 255,
        Math.random() * 255,
        Math.random() * 255,
        Math.random() * 255,
        0xee,
        0xff,
        0x00,
        0x11,
      ]),
      parentSpanId,
      name,
      startTimeUnixNano: "1640995200000000000",
      endTimeUnixNano: "1640995201500000000",
      attributes,
      status: { code: 1 },
    };
  };

  test("should filter non-AI spans when filterAISpans is enabled", () => {
    const parentSpanId = new Uint8Array([
      0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    ]);

    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                createSpan("root_span"), // Root span (no parent)
                createSpan("gen_ai.completion", [], parentSpanId), // AI span by name
                createSpan("http.request", [], parentSpanId), // Non-AI span
                createSpan("database.query", [], parentSpanId), // Non-AI span
                createSpan("llm.generate", [], parentSpanId), // AI span by name
                createSpan(
                  "regular_operation",
                  [{ key: "ai.model", value: { stringValue: "gpt-4" } }],
                  parentSpanId,
                ), // AI span by attribute
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project", true);

    // Verify filtering worked
    expect(result.filteredSpans).toBe(2); // http.request and database.query
    expect(result.rowsByParent.size).toBe(1);
    expect(result.rejectedSpans).toHaveLength(0);

    // Verify we kept the right spans (root + 3 AI spans)
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(4);

    const spanNames = rows.map((r) => r.span_attributes?.name);
    expect(spanNames).toContain("root_span");
    expect(spanNames).toContain("gen_ai.completion");
    expect(spanNames).toContain("llm.generate");
    expect(spanNames).toContain("regular_operation");
    expect(spanNames).not.toContain("http.request");
    expect(spanNames).not.toContain("database.query");
  });

  test("should not filter any spans when filterAISpans is disabled", () => {
    const parentSpanId = new Uint8Array([
      0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    ]);

    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                createSpan("root_span"), // Root span
                createSpan("gen_ai.completion", [], parentSpanId),
                createSpan("http.request", [], parentSpanId),
                createSpan("database.query", [], parentSpanId),
                createSpan("llm.generate", [], parentSpanId),
              ],
            },
          ],
        },
      ],
    };

    // Call without filtering option
    const result = otelTraceToRows(sampleTrace, "test-project");

    // Verify no filtering occurred
    expect(result.filteredSpans).toBe(0);
    expect(result.rowsByParent.size).toBe(1);
    expect(result.rejectedSpans).toHaveLength(0);

    // Verify all spans are present
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(5);

    const spanNames = rows.map((r) => r.span_attributes?.name);
    expect(spanNames).toContain("http.request");
    expect(spanNames).toContain("database.query");
  });

  test("should always keep root spans when filtering", () => {
    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                createSpan("http.root"), // Non-AI root span (no parent)
                createSpan("database.root"), // Another non-AI root span
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project", true);

    // Root spans should be kept despite not matching AI criteria
    expect(result.filteredSpans).toBe(0);
    expect(result.rowsByParent.size).toBe(1);

    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(2);
    expect(rows[0].span_attributes?.name).toBe("http.root");
    expect(rows[1].span_attributes?.name).toBe("database.root");
  });

  test("should filter by span name prefixes", () => {
    const parentSpanId = new Uint8Array([
      0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    ]);

    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                createSpan("root", []), // Root
                createSpan("gen_ai.completion", [], parentSpanId),
                createSpan("gen_ai.chat", [], parentSpanId),
                createSpan("braintrust.eval", [], parentSpanId),
                createSpan("braintrust.score", [], parentSpanId),
                createSpan("llm.generate", [], parentSpanId),
                createSpan("llm.tokens", [], parentSpanId),
                createSpan("ai.model_call", [], parentSpanId),
                createSpan("ai.embedding", [], parentSpanId),
                createSpan("traceloop.agent", [], parentSpanId),
                createSpan("traceloop.workflow", [], parentSpanId),
                // Non-AI spans
                createSpan("http.request", [], parentSpanId),
                createSpan("redis.get", [], parentSpanId),
                createSpan("postgres.query", [], parentSpanId),
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project", true);

    expect(result.filteredSpans).toBe(3); // http, redis, postgres
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(11); // root + 10 AI spans

    const spanNames = rows.map((r) => r.span_attributes?.name);
    // All AI prefixed spans should be present
    expect(spanNames).toContain("gen_ai.completion");
    expect(spanNames).toContain("braintrust.eval");
    expect(spanNames).toContain("llm.generate");
    expect(spanNames).toContain("ai.model_call");
    expect(spanNames).toContain("traceloop.agent");
    // Non-AI spans should be filtered
    expect(spanNames).not.toContain("http.request");
    expect(spanNames).not.toContain("redis.get");
    expect(spanNames).not.toContain("postgres.query");
  });

  test("should filter by attribute key prefixes", () => {
    const parentSpanId = new Uint8Array([
      0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    ]);

    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                createSpan("root", []), // Root
                // Spans with AI attributes
                createSpan(
                  "operation1",
                  [
                    { key: "gen_ai.model", value: { stringValue: "gpt-4" } },
                    { key: "http.method", value: { stringValue: "POST" } },
                  ],
                  parentSpanId,
                ),
                createSpan(
                  "operation2",
                  [
                    {
                      key: "braintrust.dataset",
                      value: { stringValue: "test-data" },
                    },
                  ],
                  parentSpanId,
                ),
                createSpan(
                  "operation3",
                  [{ key: "llm.tokens", value: { intValue: "100" } }],
                  parentSpanId,
                ),
                createSpan(
                  "operation4",
                  [{ key: "ai.temperature", value: { doubleValue: 0.7 } }],
                  parentSpanId,
                ),
                createSpan(
                  "operation5",
                  [
                    {
                      key: "traceloop.agent_id",
                      value: { stringValue: "agent-123" },
                    },
                  ],
                  parentSpanId,
                ),
                // Spans without AI attributes
                createSpan(
                  "operation6",
                  [{ key: "http.status_code", value: { intValue: "200" } }],
                  parentSpanId,
                ),
                createSpan(
                  "operation7",
                  [
                    {
                      key: "database.name",
                      value: { stringValue: "postgres" },
                    },
                  ],
                  parentSpanId,
                ),
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project", true);

    expect(result.filteredSpans).toBe(2); // operation6 and operation7
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(6); // root + 5 AI-attributed spans

    const spanNames = rows.map((r) => r.span_attributes?.name);
    expect(spanNames).toContain("operation1");
    expect(spanNames).toContain("operation2");
    expect(spanNames).toContain("operation3");
    expect(spanNames).toContain("operation4");
    expect(spanNames).toContain("operation5");
    expect(spanNames).not.toContain("operation6");
    expect(spanNames).not.toContain("operation7");
  });

  test("should track filtered span count correctly", () => {
    const parentSpanId = new Uint8Array([
      0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    ]);

    const sampleTrace = {
      resourceSpans: [
        {
          scopeSpans: [
            {
              spans: [
                createSpan("root", []), // Root - kept
                createSpan("gen_ai.model", [], parentSpanId), // AI - kept
                createSpan("http.request", [], parentSpanId), // Non-AI - filtered
                createSpan("database.query", [], parentSpanId), // Non-AI - filtered
                createSpan("redis.get", [], parentSpanId), // Non-AI - filtered
                createSpan("llm.chat", [], parentSpanId), // AI - kept
                createSpan("service.call", [], parentSpanId), // Non-AI - filtered
              ],
            },
          ],
        },
      ],
    };

    const result = otelTraceToRows(sampleTrace, "test-project", true);

    // Should have filtered exactly 4 non-AI child spans
    expect(result.filteredSpans).toBe(4);

    // Should have kept root + 2 AI spans = 3 total
    const rows = Array.from(result.rowsByParent.values())[0];
    expect(rows).toHaveLength(3);
  });
});

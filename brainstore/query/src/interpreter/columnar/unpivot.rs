use super::hash_map::FastHashMap;

use tantivy;
use util::tracer::trace_if;

use crate::interpreter::{
    columnar::{
        expr::{BatchColumns, Cast, ColumnarMultiValueField},
        value::{
            columnar_value_type_rank, BytesOrdinal, ColumnarExprContext, ColumnarValueType, Null,
            PtrOffset, StringOrdinal,
        },
        ColumnarExpr, OpaqueBuffer, PrimitiveColumnarType,
    },
    tantivy::aggregate::{UnpivotColumnarInfo, UnpivotProjectedFieldWithName},
};
use tantivy::columnar::DynamicColumn;
use util::ptree::MakePTree;
use util::xact::PaginationKey;

use crate::interpreter::tantivy::columnstore::column_to_type;

// Derives the consolidated type from a set of unpivot columns
// Uses the "most general" type approach - if we have I64 and F64, use F64
pub fn derive_unpivot_field_type(
    columns: &[(UnpivotProjectedFieldWithName, DynamicColumn)],
) -> ColumnarValueType {
    if columns.len() == 0 {
        return ColumnarValueType::StringPtr;
    }

    let columnar_types: Vec<ColumnarValueType> = columns
        .iter()
        .map(|(_, column)| match column_to_type(column) {
            tantivy::schema::Type::Bool => ColumnarValueType::Bool,
            tantivy::schema::Type::I64 => ColumnarValueType::I64,
            tantivy::schema::Type::U64 => ColumnarValueType::U64,
            tantivy::schema::Type::F64 => ColumnarValueType::F64,
            tantivy::schema::Type::Date => ColumnarValueType::DateTime,
            tantivy::schema::Type::Str => ColumnarValueType::StringOrdinal,
            tantivy::schema::Type::Bytes => ColumnarValueType::BytesOrdinal,
            _ => ColumnarValueType::StringPtr,
        })
        .collect();

    let mut best_type = columnar_types[0];
    let mut best_rank = columnar_value_type_rank(best_type);
    let mut all_types_match = true;

    for &t in &columnar_types[1..] {
        if t != best_type {
            all_types_match = false;
        }
        let rank = columnar_value_type_rank(t);
        if rank < best_rank {
            best_type = t;
            best_rank = rank;
        }
    }

    // We can't cast non ordinal types to ordinal types, so if there is one ordinal type, we need them all to be, otherwise
    // we need to use StringPtr, as all types can be cast to StringPtr.
    if !all_types_match
        && (best_type == ColumnarValueType::BytesOrdinal
            || best_type == ColumnarValueType::StringOrdinal)
    {
        return ColumnarValueType::StringPtr;
    }

    best_type
}

// This function populates all unpivot fields into columnar context, and returns
// a vec of docids which corresponds to the doc each value was taken from. The docids
// are sorted so that all columnar accessors will be accessing the docs in order.
pub fn generate_unpivot_rows(
    columns: &Vec<Option<DynamicColumn>>,
    unpivots: &Vec<Vec<UnpivotColumnarInfo>>,
    ctx: &mut ColumnarExprContext,
    docs: &[tantivy::DocId],
    tracer: Option<std::sync::Arc<util::tracer::TracedNode>>,
) -> Result<Vec<tantivy::DocId>, tantivy::TantivyError> {
    let combined_docid_mapping = trace_if(
        log::Level::Info,
        &tracer,
        "Generating unpivot rows",
        |_| -> Result<Vec<tantivy::DocId>, tantivy::TantivyError> {
            // TODO: Support multiple unpivot expressions
            assert_eq!(unpivots.len(), 1, "Expected exactly one unpivot expression");
            let unpivot_fields = &unpivots[0];

            if unpivot_fields.is_empty() {
                return Ok(docs.to_vec());
            }

            // All unpivot fields will have the same derived type
            let consolidated_type = unpivot_fields[0].derived_type;

            if consolidated_type == ColumnarValueType::Null {
                return Ok(docs.to_vec());
            }

            let mut individual_fields = Vec::new();
            let mut total_size = 0;
            let mut field_name = String::new();
            let mut key_name = String::new();
            let mut object_unpivot = false;
            let mut seen_docs = vec![false; docs.len()];

            // Step 1: Collect the values for each unpivot field into the consolidated type
            for unpivot_info in unpivot_fields.iter() {
                debug_assert_eq!(unpivot_info.derived_type, consolidated_type);
                let dynamic_column = columns[unpivot_info.column_index]
                    .as_ref()
                    .expect("Unpivot column should exist in columns array");

                let column_type = match dynamic_column {
                    DynamicColumn::Bool(_) => ColumnarValueType::Bool,
                    DynamicColumn::I64(_) => ColumnarValueType::I64,
                    DynamicColumn::U64(_) => ColumnarValueType::U64,
                    DynamicColumn::F64(_) => ColumnarValueType::F64,
                    DynamicColumn::DateTime(_) => ColumnarValueType::DateTime,
                    DynamicColumn::Str(_) => ColumnarValueType::StringOrdinal,
                    DynamicColumn::Bytes(_) => ColumnarValueType::BytesOrdinal,
                    DynamicColumn::IpAddr(_) => {
                        return Err(tantivy::TantivyError::InternalError(
                            "IP address columns not supported in unpivot".to_string(),
                        ))
                    }
                };

                let multi_field: ColumnarMultiValueField =
                    ColumnarMultiValueField::new(column_type, unpivot_info.column_index);

                let mut columnar_expr: ColumnarExpr = ColumnarExpr::MultiValueColumn(multi_field);
                // If the consolidated type is different from the column type, we need to cast it.
                // We could optimize this a bit by doing the cast directly when we extract the values,
                // but it makes things a bit uglier.
                if consolidated_type != column_type {
                    columnar_expr = ColumnarExpr::Cast(Cast {
                        expr: Box::new(columnar_expr),
                        from_type: column_type,
                        cast_type: consolidated_type,
                        buf: OpaqueBuffer::new(consolidated_type),
                    });
                }

                columnar_expr.interpret_batch(ctx, columns, docs)?;

                let docidx_mapping = match &columnar_expr {
                    ColumnarExpr::MultiValueColumn(mv) => &mv.docidx_mapping,
                    ColumnarExpr::Cast(cast) => match &*cast.expr {
                        ColumnarExpr::MultiValueColumn(mv) => &mv.docidx_mapping,
                        _ => panic!("Expected MultiValueColumn inside Cast"),
                    },
                    _ => panic!("Expected MultiValueColumn or Cast"),
                };

                for docidx in docidx_mapping {
                    seen_docs[*docidx] = true;
                }

                total_size += docidx_mapping.len();

                let key_value = match &unpivot_info.unpivot_field {
                    UnpivotProjectedFieldWithName::Array { item_name } => {
                        field_name = item_name.clone();
                        None
                    }
                    UnpivotProjectedFieldWithName::Object {
                        key_name: k,
                        value_name,
                        key_value,
                    } => {
                        if !object_unpivot {
                            key_name = k.clone();
                            field_name = value_name.clone();
                            object_unpivot = true;
                        }
                        Some(key_value.clone())
                    }
                };
                individual_fields.push((key_value, columnar_expr));
            }

            // Step 2: Combine all individual fields into a single buffer. The result will
            // have their rows sorted by docid. Each doc will have at least one entry.
            macro_rules! consolidate_to_match {
                ($($variant:ident => $type:ty),* $(,)?) => {
                    match consolidated_type {
                        $(
                            ColumnarValueType::$variant => {
                                let combined_docid_mapping = consolidate_unpivot_rows_to_type::<$type>(
                                    individual_fields,
                                    field_name,
                                    key_name,
                                    object_unpivot,
                                    seen_docs,
                                    docs,
                                    total_size,
                                    ctx,
                                )?;
                                return Ok(combined_docid_mapping);
                            },
                        )*
                        _ => {
                            return Err(tantivy::TantivyError::InternalError(format!(
                                "Unsupported consolidated type for unpivot: {:?}",
                                consolidated_type
                            )))
                        }
                    }
                };
            }

            consolidate_to_match! {
                Bool => bool,
                I64 => i64,
                U64 => u64,
                PaginationKey => PaginationKey,
                F64 => f64,
                DateTime => tantivy::DateTime,
                StringPtr => PtrOffset,
                StringOrdinal => StringOrdinal,
                BytesOrdinal => BytesOrdinal,
            };
        },
    );
    combined_docid_mapping
}

// Perform a merge sort of all unpivotted fields into a single output buffer.
fn consolidate_unpivot_rows_to_type<T: PrimitiveColumnarType + 'static + Copy>(
    individual_fields: Vec<(Option<String>, ColumnarExpr)>,
    field_name: String,
    key_name: String,
    object_unpivot: bool,
    seen_docs: Vec<bool>,
    docs: &[tantivy::DocId],
    total_size: usize,
    ctx: &mut ColumnarExprContext,
) -> Result<Vec<tantivy::DocId>, tantivy::TantivyError> {
    let empty_docs = seen_docs.iter().filter(|&&seen| !seen).count();
    let final_buffer_len = total_size + empty_docs;

    let mut final_buffer =
        OpaqueBuffer::with_capacity::<Option<T>>(T::columnar_type(), final_buffer_len);
    final_buffer.resize::<Option<T>>(final_buffer_len);

    let (mut key_buffer, key_str_ptrs) = if object_unpivot {
        let mut buf = OpaqueBuffer::with_capacity::<Option<PtrOffset>>(
            ColumnarValueType::StringPtr,
            final_buffer_len,
        );
        buf.resize::<Option<PtrOffset>>(final_buffer_len);

        // Pre-compute string pointers for each field to avoid repeated expensive insert_expr_string calls
        let mut key_str_ptrs = Vec::with_capacity(individual_fields.len());
        for (key_value, _) in &individual_fields {
            let key_ptr = if let Some(ref key_value) = key_value {
                Some(ctx.insert_expr_string(key_value))
            } else {
                None
            };
            key_str_ptrs.push(key_ptr);
        }
        (Some(buf), key_str_ptrs)
    } else {
        (None, Vec::new())
    };

    let mut combined_docid_mapping = Vec::with_capacity(final_buffer_len);

    let final_slice = final_buffer.as_mut_slice::<Option<T>>();
    let mut key_slice = key_buffer
        .as_mut()
        .map(|buf| buf.as_mut_slice::<Option<PtrOffset>>());

    let mut output_idx = 0;
    let mut field_positions: Vec<usize> = vec![0; individual_fields.len()];

    // Each of the fields is sorted by docid, and we need the output to also be sorted by docid,
    // so we can just do a merge sort. The one nuance here is any doc that has no values still
    // needs to be present, so we add None for those docs.
    for i in 0..seen_docs.len() {
        let mut found_any = false;
        for (field_idx, (_, field)) in individual_fields.iter().enumerate() {
            let pos = &mut field_positions[field_idx];

            let docidx_mapping = match field {
                ColumnarExpr::MultiValueColumn(mv) => &mv.docidx_mapping,
                ColumnarExpr::Cast(cast) => match &*cast.expr {
                    ColumnarExpr::MultiValueColumn(mv) => &mv.docidx_mapping,
                    _ => panic!("Expected MultiValueColumn inside Cast"),
                },
                _ => panic!("Expected Cast or MultiValueColumn"),
            };

            while *pos < docidx_mapping.len() && docidx_mapping[*pos] == i {
                found_any = true;
                combined_docid_mapping.push(docs[i]);

                let src_slice = match field {
                    ColumnarExpr::Cast(cast) => cast.buf.as_slice::<Option<T>>(),
                    ColumnarExpr::MultiValueColumn(mv) => mv.buf.as_slice::<Option<T>>(),
                    _ => panic!("Expected Cast or MultiValueColumn"),
                };
                final_slice[output_idx] = src_slice[*pos];

                if let Some(ref mut key_slice) = key_slice {
                    key_slice[output_idx] = key_str_ptrs[field_idx];
                }

                output_idx += 1;
                *pos += 1;
            }
        }

        // If no fields had values for this docidx and it's unseen, add a null entry
        if !found_any && !seen_docs[i] {
            combined_docid_mapping.push(docs[i]);
            final_slice[output_idx] = None;
            if let Some(ref mut key_slice) = key_slice {
                key_slice[output_idx] = None;
            }
            output_idx += 1;
        }
    }

    let mut field_buffers = FastHashMap::default();
    field_buffers.insert(field_name, final_buffer);

    if let Some(key_buf) = key_buffer {
        field_buffers.insert(key_name, key_buf);
    }

    ctx.set_unpivot_data(field_buffers);
    Ok(combined_docid_mapping)
}

#[derive(Debug, Clone, MakePTree)]
pub struct UnpivotField {
    pub field_name: String,
    // This value type can initially be dynamic as we don't know the type until we've opened the unpivot readers
    // for each field. It will be properly inferred in make_static.
    pub value_type: ColumnarValueType,
    pub unpivot_index: usize,
    #[ptree(skip)]
    pub buf: OpaqueBuffer,
}

// Values for UnpivotFields are populated in the generate_unpivot_rows function. This class just extracts
// the values from the ColumnarExprContext for use in the ColumnarExpr system.
impl UnpivotField {
    pub fn new(field_name: String, value_type: ColumnarValueType, unpivot_index: usize) -> Self {
        Self {
            field_name,
            value_type,
            unpivot_index,
            buf: OpaqueBuffer::new(value_type),
        }
    }

    pub fn interpret_batch(
        &mut self,
        ctx: &mut ColumnarExprContext,
        _columns: &BatchColumns,
        docs: &[tantivy::DocId],
    ) -> Result<&mut OpaqueBuffer, tantivy::TantivyError> {
        macro_rules! value_type_match {
            ($($variant:ident => $type:ty),* $(,)?) => {
                match self.value_type {
                    $(
                        ColumnarValueType::$variant => {
                            Self::interpret_unpivot_field_batch::<$type>(&self.field_name, &mut self.buf, docs, ctx)?;
                        }
                    )*
                    _ => {
                        return Err(tantivy::TantivyError::InternalError(format!(
                            "Unsupported value type for UnpivotField: {:?}", self.value_type
                        )));
                    }
                }
            };
        }

        value_type_match! {
            Bool => bool,
            I64 => i64,
            U64 => u64,
            F64 => f64,
            DateTime => tantivy::DateTime,
            StringPtr => PtrOffset,
            StringOrdinal => StringOrdinal,
            BytesOrdinal => BytesOrdinal,
            Null => Null,
        }

        Ok(&mut self.buf)
    }

    fn interpret_unpivot_field_batch<T: PrimitiveColumnarType + 'static + Copy>(
        field_name: &str,
        buf: &mut OpaqueBuffer,
        docs: &[tantivy::DocId],
        ctx: &mut ColumnarExprContext,
    ) -> Result<(), tantivy::TantivyError> {
        buf.resize::<Option<T>>(docs.len());
        if let Some(field_buffer) = ctx.get_unpivot_field_rows(field_name) {
            let field_slice = field_buffer.as_slice::<Option<T>>();
            let our_slice = buf.as_mut_slice::<Option<T>>();
            our_slice.copy_from_slice(field_slice);
        } else {
            // This can happen if the field is present in the schema, but no rows have the field in the segment.
            // In that case we return a buffer with all None values, the same as how columnar fields are handled.
            let our_slice = buf.as_mut_slice::<Option<T>>();
            for v in our_slice.iter_mut() {
                *v = None;
            }
        }

        Ok(())
    }
}

import { z } from "zod";

const upgrade_modal_opened = z.object({ orgName: z.string() });

const upgradeClickEntryPoints = [
  "header",
  "usageWarning",
  "pricingTable",
  "emailNotification",
  "customerPortal",
  "billingPage",
  "other",
] as const;

const upgradeClickDestinations = [
  "billingPage",
  "upgradeModal",
  "paymentPage",
] as const;

const upgradeClick = z.object({
  orgName: z.string(),
  orgId: z.string(),
  entryPoint: z.enum(upgradeClickEntryPoints),
  destination: z.enum(upgradeClickDestinations),
  destinationUrl: z.string(),
  context: z.string().optional(), // Additional context like "usage_exceeded", "pro_plan", etc.
  sourcePage: z.string().optional(), // Track which page the user is on when they click
});

const viewUsageClickEntryPoints = [
  "billingPage",
  "emailNotification",
  "sidebar",
  "other",
] as const;

const viewUsageClick = z.object({
  orgName: z.string(),
  orgId: z.string(),
  entryPoint: z.enum(viewUsageClickEntryPoints),
  destinationUrl: z.string(),
  sourcePage: z.string().optional(), // Track which page the user is on when they click
});

const viewInvoiceClickEntryPoints = [
  "billingPage",
  "emailNotification",
  "other",
] as const;

const viewInvoiceClick = z.object({
  orgName: z.string(),
  orgId: z.string(),
  entryPoint: z.enum(viewInvoiceClickEntryPoints),
  destinationUrl: z.string(),
  invoiceId: z.string(),
  invoiceNumber: z.string().optional(),
  invoiceAmount: z.string().optional(),
  sourcePage: z.string().optional(), // Track which page the user is on when they click
});
const signup = z.object({
  source: z.enum([
    "in_app",
    "cta_button",
    "logged_out_playground",
    "clerk_signup",
  ]),
});
const signin = z.object({
  source: z.enum(["in_app", "cta_button", "logged_out_playground"]),
});

const projectCreateEntryPoints = [
  "projectsSidebarDropdown",
  "projectsHomePageNewProjectButton",
  "projectsHomePageEmptyState",
  "onboardingFlow",
] as const;

export type ProjectCreateEntryPoint = (typeof projectCreateEntryPoints)[number];

const projectCreateAttempt = z.object({
  projectName: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const projectCreateAbandon = z.object({
  projectName: z.string(),
  reason: z.enum(["error", "closed"]),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

const projectCreate = z.object({
  projectName: z.string(),
  projectId: z.string(),
  entryPoint: z.enum(projectCreateEntryPoints).optional(),
  flowId: z.string(),
});

// Playground analytics schemas
const playgroundCreateEntryPoints = [
  "playgroundsPageEmptyState",
  "playgroundsPageCreateButton",
  "promptDialogCreateButton",
  "promptDialogDuplicateButton",
  "functionEditorCreateButton",
  "datasetPageCreateButton",
  "onboardingFlow",
  "projectOverviewCreateButton",
] as const;

export type PlaygroundCreateEntryPoint =
  (typeof playgroundCreateEntryPoints)[number];

const playgroundCreateAttempt = z.object({
  playgroundName: z.string(),
  entryPoint: z.enum(playgroundCreateEntryPoints).optional(),
  flowId: z.string(),
  projectId: z.string(),
  projectName: z.string().optional(),
  initialDataType: z
    .enum(["prompt", "function", "scorer", "dataset", "none"])
    .optional(),
  initialObjectId: z.string().optional(), // ID of the initial object (prompt, function, scorer, dataset)
});

const playgroundCreate = z.object({
  playgroundName: z.string(),
  playgroundId: z.string(),
  entryPoint: z.enum(playgroundCreateEntryPoints).optional(),
  flowId: z.string(),
  projectId: z.string(),
  projectName: z.string(),
  initialDataType: z.enum(["prompt", "function", "scorer", "dataset", "none"]),
  initialObjectId: z.string().optional(), // ID of the initial object (prompt, function, scorer, dataset)
});

// Onboarding analytics events
const signupAttempt = z.object({
  flowId: z.string(),
  source: z.literal("web"),
  entryPoint: z
    .enum(["cta_button", "in_app", "logged_out_playground", "other"])
    .optional(),
});

const onboardingStepComplete = z.object({
  flowId: z.string(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  stepDetails: z.object({
    stepName: z.string(),
    stepIndex: z.number(), // Dynamic step index (1-based, excluding hidden steps)
    skippable: z.boolean(),
    previousStep: z.string().optional(),
  }),
  isNewUser: z.boolean().optional(),
  path: z.enum(["trace", "ui"]).optional(),
  org_id: z.string().optional(),
  org_name: z.string().optional(),
  source: z.literal("web"),
});

const onboardingComplete = z.object({
  flowId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  completionDetails: z.object({
    path: z.enum(["trace", "ui"]),
    stepName: z.string(),
    stepIndex: z.number(),
    providerKeyProvided: z.boolean().optional(),
    providerChoice: z.string().optional(),
    skipped: z.boolean().optional(),
    sdkLanguage: z.string().optional(),
  }),
  isNewUser: z.boolean().optional(),
  org_id: z.string().optional(),
  org_name: z.string().optional(),
  source: z.literal("web"),
});

const onboardingPageViewed = z.object({
  flowId: z.string().optional(),
  orgName: z.string().optional(),
  orgId: z.string().optional(),
  pageName: z.string(),
  pageIndex: z.number().optional(),
  isNewUser: z.boolean().optional(),
  source: z.literal("web"),
});

// Playground edit analytics schemas
const playgroundEditEntryPoints = [
  "runButton",
  "runRow",
  "keyboardShortcut",
  "autoRun",
  "headerPlus",
  "taskPlus",
  "duplication",
  "promptEditor",
  "modelSelector",
  "datasetSelector",
  "scorerSelector",
  "toolSelector",
  "configPanel",
  "globalControls",
  "taskActions",
  "other",
] as const;

export type PlaygroundEditEntryPoint =
  (typeof playgroundEditEntryPoints)[number];

const playgroundEditTypes = [
  "promptChanged",
  "modelChanged",
  "datasetChanged",
  "scorersChanged",
  "toolsChanged",
  "structuredOutputChanged",
  "responseTypeChanged",
  "parserChanged",
  "configChanged", // temperature, maxTokens, etc.
  "nameChanged",
  "descriptionChanged",
  "taskAdded",
  "taskDeleted",
  "messageAdded",
  "messageDeleted",
  "messageRoleChanged",
  "messagePartAdded",
] as const;

export type PlaygroundEditType = (typeof playgroundEditTypes)[number];

const playgroundEdit = z.object({
  playgroundId: z.string(),
  projectId: z.string(),
  // Task context - which task the user is editing
  taskId: z.string().optional(),
  taskIndex: z.number().optional(),
  taskType: z.string().optional(),
  // Trigger entity (human vs AI)
  triggerEntity: z.enum(["human", "ai"]),
  // Human-specific action (when triggerEntity === "human")
  entryPoint: z.enum(playgroundEditEntryPoints).optional(),
  // AI-specific action (when triggerEntity === "ai")
  aiAction: z.enum(["loopEdit", "loopCreate", "loopExecute"]).optional(),
  // AI edit state (when triggerEntity === "ai" and aiAction includes "edit")
  aiEditState: z.enum(["accepted", "rejected"]).optional(),
  // What was edited
  editType: z.enum(playgroundEditTypes),
  // Configuration type (only applicable when editType === "configChanged")
  configType: z.enum(["global", "task"]).optional(),
  // Previous vs new values (not applicable for taskAdded and messageAdded)
  previousValue: z.string().optional(), // What it was before
  newValue: z.string().optional(), // What it is now
  // Edit-specific details and metadata (varies by edit type)
  details: z.record(z.unknown()).optional(),
  // Source tracking
  source: z.literal("web"),
});

// Playground run analytics schemas
const playgroundRunEntryPoints = [
  "runButton",
  "runRow",
  "keyboardShortcut",
  "autoRun",
  "headerRun",
  "taskRun",
  "experimentButton",
  "other",
] as const;

export type PlaygroundRunEntryPoint = (typeof playgroundRunEntryPoints)[number];

const playgroundRunOutcomes = ["success", "error", "cancelled"] as const;

export type PlaygroundRunOutcome = (typeof playgroundRunOutcomes)[number];

const playgroundRun = z.object({
  playgroundId: z.string(),
  projectId: z.string(),
  runId: z.string(), // Unique identifier for each playground run
  // Run outcome
  outcome: z.enum(playgroundRunOutcomes),
  errorMessage: z.string().optional(),
  // Trigger entity (human vs AI)
  triggerEntity: z.enum(["human", "ai"]),
  // Human-specific action (when triggerEntity === "human")
  entryPoint: z.enum(playgroundRunEntryPoints).optional(),
  // AI-specific action (when triggerEntity === "ai")
  aiAction: z.enum(["loopEdit", "loopCreate", "loopExecute"]).optional(),
  // AI edit state (when triggerEntity === "ai" and aiAction includes "edit")
  aiEditState: z.enum(["accepted", "rejected"]).optional(),
  // Run configuration and metadata
  details: z.record(z.unknown()).optional(),
  // Task-specific details (nested within details)
  taskDetails: z
    .array(
      z.object({
        id: z.string().optional(),
        name: z.string().optional(),
        taskIndex: z.number().optional(),
        type: z.enum(["prompt", "function", "remote_eval"]).optional(),
        model: z.string().optional(),
        modelProvider: z.string().optional(),
        sourceType: z
          .enum(["inline", "saved_prompt", "function", "remote"])
          .optional(),
        toolTypes: z.array(z.string()).optional(),
        executionTime: z.number().optional(),
        parentTaskId: z.string().optional(),
        temperature: z.number().optional(),
        topP: z.number().optional(),
        maxTokens: z.number().optional(),
        streamingEnabled: z.boolean().optional(),
        promptType: z.enum(["chat", "completion"]).optional(),
      }),
    )
    .optional(),
  // Source tracking
  source: z.literal("web"),
});

const playgroundDelete = z.object({
  projectId: z.string(),
  playgroundId: z.string(),
  projectName: z.string().optional(),
  deletionMethod: z
    .enum(["delete_button", "keyboard_shortcut", "batch_delete", "other"])
    .optional(),
  numTasks: z.number().optional(),
  hasDataset: z.boolean().optional(),
  hasScorers: z.boolean().optional(),
  lastModified: z.string().optional(),
  createdTime: z.string().optional(),
  // Source tracking
  source: z.literal("web"),
});

const downgradeProSubscription = z.object({
  reason: z.string(),
  otherReason: z.string().optional(),
});

// Server-side signup success event
const signupSuccess = z.object({
  flowId: z.string().optional(),
  flowName: z.enum(["new_user_signup", "invitee_signup"]).optional(),
  userId: z.string(),
  signupMethod: z.enum(["email", "google"]).optional(),
  // Common server-side fields
  source: z.literal("server"),
  timestamp: z.string().optional(),
});

// Server-side onboarding start event
const onboardingStartServer = z.object({
  userId: z.string(),
  orgId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  // Common server-side fields
  source: z.literal("server"),
});

// Server-side onboarding complete event
const onboardingCompleteServer = z.object({
  userId: z.string(),
  orgId: z.string().optional(),
  flowName: z
    .enum(["new_user_signup", "existing_user_org_creation", "invitee_signup"])
    .optional(),
  pathChoice: z.enum(["sdk", "ui"]).optional(),
  // Common server-side fields
  source: z.literal("server"),
});

// Checkout form step analytics schemas
const checkoutFormStepTypes = [
  "address_element_started",
  "address_element_complete",
  "payment_element_started",
  "payment_element_complete",
  "coupon_modal_opened",
  "coupon_modal_closed",
  "coupon_validation_start",
  "coupon_validation_success",
  "coupon_validation_error",
  "coupon_applied",
  "coupon_removed",
] as const;

const checkoutFormStep = z.object({
  orgName: z.string(),
  orgId: z.string(),
  flowId: z.string(),
  stepType: z.enum(checkoutFormStepTypes),
  purchasePlanSlug: z.string().optional(),
  purchasePlanId: z.string().optional(),
  couponCode: z.string().optional(),
  sourcePage: z.string().optional(),
  // Additional context for specific steps
  fieldName: z.string().optional(),
  errorMessage: z.string().optional(),
});

// Standalone checkout start event (separate from checkoutFormStep)
const checkoutStart = z.object({
  orgName: z.string(),
  orgId: z.string(),
  flowId: z.string(),
  purchasePlanSlug: z.string().optional(),
  source: z.string().optional(),
  entryPoint: z.string().optional(),
  destination: z.string().optional(),
  // Source tracking
  sourceType: z.literal("web"),
});

export const eventSchemas = {
  upgrade_modal_opened,
  upgradeClick,
  viewUsageClick,
  viewInvoiceClick,
  signup,
  signin,
  projectCreateAttempt,
  projectCreate,
  projectCreateAbandon,
  playgroundCreateAttempt,
  playgroundCreate,
  playgroundEdit,
  downgradeProSubscription,
  signupAttempt,
  onboardingStepComplete,
  onboardingComplete,
  onboardingPageViewed,
  playgroundRun,
  playgroundDelete,
  signupSuccess,
  onboardingStartServer,
  onboardingCompleteServer,
  checkoutFormStep,
  checkoutStart,
} as const;

type EventSchemas = typeof eventSchemas;
export type EventName = keyof EventSchemas;
export type EventProps<K extends EventName> = z.infer<EventSchemas[K]>;

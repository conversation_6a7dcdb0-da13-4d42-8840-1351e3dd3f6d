---
title: "Product updates (9/27)"
description: "Score existing logs, create scorers and datasets from anywhere with <PERSON>, easily start iterating from logs, and more product updates."
author: "<PERSON><PERSON><PERSON>"
date: "27 September 2025"
draft: true
---

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";

# Product updates (9/27)

<BlogAuthor author="Ornella Altunyan" date="27 September 2025" />


## Score existing logs

![Score logs](/blog/meta/product-updates-9-27/score-logs.png)

You can now retroactively apply scorers to existing logs to find issues you missed. Manually select from your logs, apply a scorer while in a single log view, or bulk apply to the last 50 logs in your current view.

[Learn more →](/docs/guides/logs/score#manual-scoring?utm_source=product_update&utm_medium=blog&utm_campaign=update_sept27)

## Create scorers and datasets from anywhere with Loop

![Dataset from logs](/blog/meta/product-updates-9-27/dataset-from-logs.png)

Loop can now create scorers and datasets from any view in Braintrust. This means you can ask <PERSON> to find some common errors from the logs and create a dataset with them, and other integrated workflows.

[Learn more →](/docs/guides/loop?utm_source=product_update&utm_medium=blog&utm_campaign=update_sept27)

## Easily start iterating from logs

![Iterate from log](/blog/meta/product-updates-9-27/iterate-from-log.png)

You can now fully bootstrap a playground that mimics your app's setup right from your logs. Choose any log(s) and select **Iterate in playground**. Prompts and data are auto-extracted so you can quickly start iterating.

[Learn more →](/docs/guides/logs/view#iterate-on-logged-prompts-in-playgrounds?utm_source=product_update&utm_medium=blog&utm_campaign=update_sept27)

## Other product updates

### Core product

- Support for updating the email associated with billing data

### Python SDK v2.1.0 (upcoming)

- Added `strict` parameter to Prompt build

### Google ADK (version v0.2.0)

- Enhanced automatic tracing for runners, agents, and flows - captures complete input/output data and metadata at every step without configuring OpenTelemetry

## Upcoming events

- **Measure What Matters: Intro to AI Evals** - [October 7 (Online)](https://luma.com/evals101?utm_source=weekly-update-blog)
- **AI Evals on Tap** - [October 8 in San Francisco](https://partiful.com/e/cp3SKAKm8qs8T6S95H0Q)
- **Developer Marketing Happy Hour** - [October 9 in San Francisco](https://partiful.com/e/Ir5DcKLzJsU82NuURcHL)

## We're hiring

We're growing fast and looking for exceptional people to join our team. We have exciting opportunities across sales, design, engineering, and product, including:

- [Software engineer, backend](/careers?ashby_jid=9728653e-49b9-4f5c-b6cd-7dbc6a6d5fcc)
- [Software engineer, product](/careers?ashby_jid=7d0a5b62-a554-48f0-b2f7-8cbac0e98ae6)
- [Software engineer, systems](/careers?ashby_jid=8b9cfa26-627f-442c-a358-783b0e4ef930)
- [Education engineer](/careers?ashby_jid=fd6f4e64-ffcb-4538-93fc-d232427c24cb)
- [Developer advocate](/careers?ashby_jid=cae8e5b2-14fe-4487-9da0-123c6733c70e)
- [Open source engineer](/careers?ashby_jid=2a6c4ee9-063f-45d4-83ba-faf64b1f1a60)
- [Solutions engineer](/careers?ashby_jid=f877a92d-e7ab-4664-abf5-c60fafa789ef)
- [Account executives and sales development representatives](/careers?ashby_jid=d0ba915c-85c4-4ac5-bedf-7dd7459ec14d)

If you're passionate about AI and want to work with some of the brightest minds in the industry, we'd love to hear from you.

---

Stay tuned for more updates from the team. We're excited to continue building and improving Braintrust with your support and feedback. If you'd like to learn more about Braintrust, [sign up](/signup) or [book a demo](/contact).

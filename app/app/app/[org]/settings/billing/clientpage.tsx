"use client";

import { PricingTable } from "./pricing-table";
import { CurrentSubscriptionSummary } from "./current-subscription-summary";
import { type Permission } from "@braintrust/typespecs";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { isFreeSubscription, isUnlimitedOrg } from "./plans";
import { useOrg } from "#/utils/user";
import { isFriendOrFamily } from "#/ui/unlimited-free-orgs";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useEffect } from "react";
import { type EventProps } from "#/analytics/events";
import { getPageName } from "#/utils/analytics";
import { useQueryFunc } from "#/utils/react-query";
import {
  type getOrbSubscription,
  type getDefaultPaymentMethod,
  type getOrbInvoices,
} from "#/utils/billing/utils";
import { Spinner } from "#/ui/icons/spinner";
import type Orb from "orb-billing";

// Import the enum arrays for validation
const upgradeClickEntryPoints = [
  "header",
  "usageWarning",
  "pricingTable",
  "emailNotification",
  "customerPortal",
  "billingPage",
  "other",
] as const;

const upgradeClickDestinations = [
  "billingPage",
  "upgradeModal",
  "paymentPage",
] as const;

function ClientPage({
  orgName,
  orbSubscription,
  orbPaymentMethodSummary,
  orbInvoices,
  orgPermissions,
  billingReportingNotice,
}: {
  orgName: string;
  orbSubscription: Orb.Subscription | null;
  orbPaymentMethodSummary: {
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  } | null;
  orbInvoices: Orb.Invoice[] | null;
  orgPermissions: Permission[];
  billingReportingNotice: string;
}) {
  const org = useOrg();
  const { track } = useAppAnalytics();
  const orgId = org?.id ?? "";
  const {
    data: currentSubscription,
    isLoading: isLoadingSubscription,
    invalidate: refreshSubscription,
  } = useQueryFunc<typeof getOrbSubscription>({
    fName: "getOrbSubscription",
    args: { orgId: orgId },
    serverData: orbSubscription,
  });

  const { data: paymentMethodSummary, isLoading: isLoadingPaymentMethod } =
    useQueryFunc<typeof getDefaultPaymentMethod>({
      fName: "getDefaultPaymentMethod",
      args: {
        stripeCustomerId:
          currentSubscription?.customer.payment_provider_id ?? null,
        orgId: orgId,
      },
      serverData: orbPaymentMethodSummary,
    });

  const { data: invoices, isLoading: isLoadingInvoices } = useQueryFunc<
    typeof getOrbInvoices
  >({
    fName: "getOrbInvoices",
    args: { orgId: orgId },
    serverData: orbInvoices,
  });

  // Track email clicks when users land on billing page
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const source = urlParams.get("source");
    const entryPoint = urlParams.get("entryPoint");
    const destination = urlParams.get("destination");

    if (source === "email" && entryPoint && destination) {
      // Validate against allowed enum values
      const isValidEntryPoint = upgradeClickEntryPoints.includes(
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
        entryPoint as any,
      );
      const isValidDestination = upgradeClickDestinations.includes(
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
        destination as any,
      );

      if (isValidEntryPoint && isValidDestination) {
        const eventData: EventProps<"upgradeClick"> = {
          orgName: orgName,
          orgId: org?.id ?? "",
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          entryPoint: entryPoint as EventProps<"upgradeClick">["entryPoint"],
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          destination: destination as EventProps<"upgradeClick">["destination"],
          destinationUrl: window.location.href,
          context: "email_click",
          sourcePage:
            typeof window !== "undefined"
              ? getPageName(window.location.pathname)
              : "",
        };
        track("upgradeClick", eventData);
      } else {
        console.warn("Invalid upgrade click parameters:", {
          entryPoint,
          destination,
        });
      }
    }
  }, [track, orgName, org?.id]);

  const isAllowedToPay = orgPermissions.includes("update");
  if (!isAllowedToPay) {
    return (
      <TableEmptyState label='You do not have permission to manage billing for this organization. Ask your administrator to grant the "Manage settings" permission for this organization.' />
    );
  }

  if (isLoadingPaymentMethod || isLoadingInvoices || isLoadingSubscription) {
    return <Spinner />;
  }
  // Until we have all orgs in Orb, we hide the billing UI if the org has unlimited spans and doesn't have an Orb plan
  const isUnlimitedWithNoPlan =
    org?.api_url && isUnlimitedOrg({ org }) && org?.plan_id == null;

  const isFriend = isFriendOrFamily(org?.id ?? "");
  const shouldHideUsage = isUnlimitedWithNoPlan && !isFriend;

  if (shouldHideUsage) {
    return (
      <div className="mb-12">
        <h2 className="mb-6 text-lg font-semibold">Current plan</h2>
        <div className="rounded-md border p-6">
          Usage for this organization is coming soon
        </div>
      </div>
    );
  }

  const isSubscribedToFreePlan =
    currentSubscription && isFreeSubscription(currentSubscription);

  if (currentSubscription && !isSubscribedToFreePlan) {
    return (
      <CurrentSubscriptionSummary
        currentSubscription={currentSubscription}
        paymentMethodSummary={paymentMethodSummary}
        invoices={invoices}
        billingReportingNotice={billingReportingNotice}
        refreshSubscription={refreshSubscription}
      />
    );
  }

  return (
    <>
      {isSubscribedToFreePlan && (
        <CurrentSubscriptionSummary
          currentSubscription={currentSubscription}
          paymentMethodSummary={paymentMethodSummary}
          invoices={invoices}
          billingReportingNotice={billingReportingNotice}
          refreshSubscription={refreshSubscription}
        />
      )}
      <h2 className="text-lg font-semibold">All plans</h2>
      <p className="mb-6 text-sm text-primary-600">
        Select a billing plan for this organization
      </p>
      <PricingTable orgName={orgName} />
    </>
  );
}

export interface Params {
  org: string;
}

export { ClientPage };

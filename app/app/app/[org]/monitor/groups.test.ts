import { describe, expect, test } from "vitest";
import {
  getDataSeries,
  getGroupedDataSeries,
  groupData,
  isEmptyMetricsData,
  getGroupedSeriesNames,
  getSeriesColorMap,
} from "./groups";
import type { MetricsData } from "./groups.types";

const groupedCostData: MetricsData[] = [
  {
    inputTokensCost: 0.000348,
    outputTokensCost: 0.000441,
    totalCost: 0.000789,
    time: 1731571200000,
    count: BigInt(1),
    group: "gpt-3.5-turbo",
  },
  {
    inputTokensCost: 2.6493450000000003,
    outputTokensCost: 3.01085,
    totalCost: 5.660195,
    time: 1731571200000,
    count: BigInt(1206),
    group: "gpt-4o",
  },
  {
    inputTokensCost: 1.91172,
    outputTokensCost: 1.64418,
    totalCost: 3.5559000000000003,
    time: 1731571200000,
    count: BigInt(91),
    group: "gpt-4",
  },
];

const tokenCountData: MetricsData[] = [
  {
    time: 1727852400000,
    count: BigInt(519),
    sum_tokens: 61031,
    sum_prompt_tokens: 42717,
    sum_completion_tokens: 18314,
  },
  {
    time: 1727852500000,
    count: BigInt(1519),
    sum_tokens: null,
    sum_prompt_tokens: null,
    sum_completion_tokens: null,
  },
  {
    time: 1727852600000,
    count: BigInt(2419),
    sum_tokens: 408188,
    sum_prompt_tokens: 285757,
    sum_completion_tokens: 122431,
  },
];

const scoresData: MetricsData[] = [
  {
    time: 1727852400000,
    last_updated: 1730504565000,
    count: BigInt(5),
    avg: 0.3238717590732863,
    scores: {
      Factuality: {
        last_updated: 1730504565000,
        count: BigInt(1),
        avg: 0.1804978620159966,
      },
      Preference: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.6565815114004502,
      },
      Quality: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.0628489552747673,
      },
    },
  },
  {
    time: 1727852500000,
    last_updated: 1730508352000,
    count: BigInt(7),
    avg: 0.42178094741025846,
    scores: {
      Factuality: {
        last_updated: 1730508352000,
        count: BigInt(1),
        avg: 0.7663173144344797,
      },
      Preference: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: 0.2702463587421667,
      },
      Quality: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: 0.6652036838734504,
      },
      Toxicity: {
        last_updated: 1730506512000,
        count: BigInt(2),
        avg: 0.15762461610304773,
      },
    },
  },
  {
    time: 1727852600000,
    last_updated: 1730512450000,
    count: BigInt(4),
    avg: 0.38663836419783043,
    scores: {
      Factuality: {
        last_updated: 1730512450000,
        count: BigInt(3),
        avg: 0.491672740635523,
      },
      Toxicity: {
        last_updated: 1730512011000,
        count: BigInt(1),
        avg: 0.0715352348847526,
      },
    },
  },
];

const scoresDataWithNullAvg: MetricsData[] = [
  {
    time: 1727852400000,
    last_updated: 1730504565000,
    count: BigInt(5),
    avg: 0.3238717590732863,
    scores: {
      Factuality: {
        last_updated: 1730504565000,
        count: BigInt(1),
        avg: null,
      },
      Preference: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.6565815114004502,
      },
      Quality: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.0628489552747673,
      },
    },
  },
  {
    time: 1727852500000,
    last_updated: 1730508352000,
    count: BigInt(7),
    avg: 0.42178094741025846,
    scores: {
      Factuality: {
        last_updated: 1730508352000,
        count: BigInt(1),
        avg: 0.7663173144344797,
      },
      Preference: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: null,
      },
      Quality: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: 0.6652036838734504,
      },
      Toxicity: {
        last_updated: 1730506512000,
        count: BigInt(2),
        avg: 0.15762461610304773,
      },
    },
  },
  {
    time: 1727852600000,
    last_updated: 1730512450000,
    count: BigInt(4),
    avg: 0.38663836419783043,
    scores: {
      Factuality: {
        last_updated: 1730512450000,
        count: BigInt(3),
        avg: 0.491672740635523,
      },
      Toxicity: {
        last_updated: 1730512011000,
        count: BigInt(1),
        avg: null,
      },
    },
  },
];

const groupedTokenCountData: MetricsData[] = [
  {
    time: 1727852400000,
    group: "gpt-3.5-turbo",
    count: BigInt(519),
    sum_tokens: 61031,
    sum_prompt_tokens: 42717,
    sum_completion_tokens: 18314,
  },
  {
    time: 1727852400000,
    group: null,
    count: BigInt(519),
    sum_tokens: null,
    sum_prompt_tokens: null,
    sum_completion_tokens: null,
  },
  {
    time: 1727852400000,
    group: "gpt-4",
    count: BigInt(0),
    sum_tokens: 408188,
    sum_prompt_tokens: 285757,
    sum_completion_tokens: 122431,
  },
  {
    time: 1727852400000,
    group: "gpt-3.5",
    count: BigInt(0),
    sum_tokens: 49934,
    sum_prompt_tokens: 34968,
    sum_completion_tokens: 14966,
  },
  {
    time: 1727938800000,
    group: "gpt-3.5-turbo",
    count: BigInt(0),
    sum_tokens: 179225,
    sum_prompt_tokens: 125328,
    sum_completion_tokens: 53897,
  },
  {
    time: 1727938800000,
    group: "gpt-3.5",
    count: BigInt(0),
    sum_tokens: 174107,
    sum_prompt_tokens: 121854,
    sum_completion_tokens: 52253,
  },
  {
    time: 1727938800000,
    group: null,
    count: BigInt(1566),
    sum_tokens: null,
    sum_prompt_tokens: null,
    sum_completion_tokens: null,
  },
  {
    time: 1727938800000,
    group: "gpt-4",
    count: BigInt(0),
    sum_tokens: 1214239,
    sum_prompt_tokens: 849858,
    sum_completion_tokens: 364381,
  },
  {
    time: 1728025200000,
    group: "gpt-3.5",
    count: BigInt(0),
    sum_tokens: 166151,
    sum_prompt_tokens: 116260,
    sum_completion_tokens: 49891,
  },
  {
    time: 1728025200000,
    group: "gpt-3.5-turbo",
    count: BigInt(0),
    sum_tokens: 158142,
    sum_prompt_tokens: 110777,
    sum_completion_tokens: 47365,
  },
  {
    time: 1728025200000,
    group: null,
    count: BigInt(1543),
    sum_tokens: null,
    sum_prompt_tokens: null,
    sum_completion_tokens: null,
  },
  {
    time: 1728025200000,
    group: "gpt-4",
    count: BigInt(0),
    sum_tokens: 1220508,
    sum_prompt_tokens: 854102,
    sum_completion_tokens: 366406,
  },
];

const groupedScoresData: MetricsData[] = [
  {
    time: 1730502000000,
    group: "gpt-3.5-turbo",
    last_updated: 1730504565000,
    count: BigInt(5),
    avg: 0.3238717590732863,
    scores: {
      Factuality: {
        last_updated: 1730504565000,
        count: BigInt(1),
        avg: 0.1804978620159966,
      },
      Preference: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.6565815114004502,
      },
      Quality: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.0628489552747673,
      },
    },
  },
  {
    time: 1730502000000,
    group: "gpt-3.5",
    last_updated: 1730508352000,
    count: BigInt(7),
    avg: 0.42178094741025846,
    scores: {
      Factuality: {
        last_updated: 1730508352000,
        count: BigInt(1),
        avg: 0.7663173144344797,
      },
      Preference: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: 0.2702463587421667,
      },
      Quality: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: 0.6652036838734504,
      },
      Toxicity: {
        last_updated: 1730506512000,
        count: BigInt(2),
        avg: 0.15762461610304773,
      },
    },
  },
  {
    time: 1730502000000,
    group: null,
    last_updated: 1730512450000,
    count: BigInt(4),
    avg: 0.38663836419783043,
    scores: {
      Factuality: {
        last_updated: 1730512450000,
        count: BigInt(3),
        avg: 0.491672740635523,
      },
      Toxicity: {
        last_updated: 1730512011000,
        count: BigInt(1),
        avg: 0.0715352348847526,
      },
    },
  },
  {
    time: 1730512800000,
    group: "gpt-3.5-turbo",
    last_updated: 1730514727000,
    count: BigInt(1),
    avg: 0.7555176513578717,
    scores: {
      Factuality: {
        last_updated: 1730514727000,
        count: BigInt(1),
        avg: 0.7555176513578717,
      },
    },
  },
  {
    time: 1730512800000,
    group: "gpt-3.5",
    last_updated: 1730517797000,
    count: BigInt(1),
    avg: 0.2844902652759832,
    scores: {
      Factuality: {
        last_updated: 1730517797000,
        count: BigInt(1),
        avg: 0.2844902652759832,
      },
    },
  },
  {
    time: 1730512800000,
    group: null,
    last_updated: 1730522166000,
    count: BigInt(2),
    avg: 0.46234073831144706,
    scores: {
      Factuality: {
        last_updated: 1730520505000,
        count: BigInt(1),
        avg: 0.8474943663474598,
      },
      Toxicity: {
        last_updated: 1730522166000,
        count: BigInt(1),
        avg: 0.07718711027543437,
      },
    },
  },
  {
    time: 1730523600000,
    group: null,
    last_updated: 1730527019000,
    count: BigInt(4),
    avg: 0.26879117167993216,
    scores: {
      Factuality: {
        last_updated: 1730526550000,
        count: BigInt(2),
        avg: 0.24076378069621257,
      },
      Preference: {
        last_updated: 1730527019000,
        count: BigInt(1),
        avg: 0.36274172943898964,
      },
      Quality: {
        last_updated: 1730527019000,
        count: BigInt(1),
        avg: 0.23089539588831398,
      },
    },
  },
  {
    time: 1730523600000,
    group: "gpt-3.5",
    last_updated: 1730527651000,
    count: BigInt(5),
    avg: 0.4137201093402398,
    scores: {
      Factuality: {
        last_updated: 1730527265000,
        count: BigInt(1),
        avg: 0.7299530300500745,
      },
      Preference: {
        last_updated: 1730527651000,
        count: BigInt(1),
        avg: 0.07925536129812794,
      },
      Quality: {
        last_updated: 1730527651000,
        count: BigInt(1),
        avg: 0.9320100937422017,
      },
      Toxicity: {
        last_updated: 1730527651000,
        count: BigInt(2),
        avg: 0.16369103080539732,
      },
    },
  },
  {
    time: 1730523600000,
    group: "gpt-3.5-turbo",
    last_updated: 1730537106000,
    count: BigInt(1),
    avg: 0.19330717143673967,
    scores: {
      Toxicity: {
        last_updated: 1730537106000,
        count: BigInt(1),
        avg: 0.19330717143673967,
      },
    },
  },
];

const groupedScoresDataWithNullAvg: MetricsData[] = [
  {
    time: 1730502000000,
    group: "gpt-3.5-turbo",
    last_updated: 1730504565000,
    count: BigInt(5),
    avg: 0.3238717590732863,
    scores: {
      Factuality: {
        last_updated: 1730504565000,
        count: BigInt(1),
        avg: null,
      },
      Preference: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.6565815114004502,
      },
      Quality: {
        last_updated: 1730503185000,
        count: BigInt(2),
        avg: 0.0628489552747673,
      },
    },
  },
  {
    time: 1730502000000,
    group: "gpt-3.5",
    last_updated: 1730508352000,
    count: BigInt(7),
    avg: 0.42178094741025846,
    scores: {
      Factuality: {
        last_updated: 1730508352000,
        count: BigInt(1),
        avg: 0.7663173144344797,
      },
      Preference: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: null,
      },
      Quality: {
        last_updated: 1730506919000,
        count: BigInt(2),
        avg: 0.6652036838734504,
      },
      Toxicity: {
        last_updated: 1730506512000,
        count: BigInt(2),
        avg: 0.15762461610304773,
      },
    },
  },
  {
    time: 1730502000000,
    group: null,
    last_updated: 1730512450000,
    count: BigInt(4),
    avg: 0.38663836419783043,
    scores: {
      Factuality: {
        last_updated: 1730512450000,
        count: BigInt(3),
        avg: 0.491672740635523,
      },
      Toxicity: {
        last_updated: 1730512011000,
        count: BigInt(1),
        avg: null,
      },
    },
  },
  {
    time: 1730512800000,
    group: "gpt-3.5-turbo",
    last_updated: 1730514727000,
    count: BigInt(1),
    avg: 0.7555176513578717,
    scores: {
      Factuality: {
        last_updated: 1730514727000,
        count: BigInt(1),
        avg: null,
      },
    },
  },
  {
    time: 1730512800000,
    group: "gpt-3.5",
    last_updated: 1730517797000,
    count: BigInt(1),
    avg: 0.2844902652759832,
    scores: {
      Factuality: {
        last_updated: 1730517797000,
        count: BigInt(1),
        avg: 0.2844902652759832,
      },
    },
  },
  {
    time: 1730512800000,
    group: null,
    last_updated: 1730522166000,
    count: BigInt(2),
    avg: 0.46234073831144706,
    scores: {
      Factuality: {
        last_updated: 1730520505000,
        count: BigInt(1),
        avg: 0.8474943663474598,
      },
      Toxicity: {
        last_updated: 1730522166000,
        count: BigInt(1),
        avg: null,
      },
    },
  },
  {
    time: 1730523600000,
    group: null,
    last_updated: 1730527019000,
    count: BigInt(4),
    avg: 0.26879117167993216,
    scores: {
      Factuality: {
        last_updated: 1730526550000,
        count: BigInt(2),
        avg: 0.24076378069621257,
      },
      Preference: {
        last_updated: 1730527019000,
        count: BigInt(1),
        avg: 0.36274172943898964,
      },
      Quality: {
        last_updated: 1730527019000,
        count: BigInt(1),
        avg: null,
      },
    },
  },
  {
    time: 1730523600000,
    group: "gpt-3.5",
    last_updated: 1730527651000,
    count: BigInt(5),
    avg: 0.4137201093402398,
    scores: {
      Factuality: {
        last_updated: 1730527265000,
        count: BigInt(1),
        avg: 0.7299530300500745,
      },
      Preference: {
        last_updated: 1730527651000,
        count: BigInt(1),
        avg: 0.07925536129812794,
      },
      Quality: {
        last_updated: 1730527651000,
        count: BigInt(1),
        avg: 0.9320100937422017,
      },
      Toxicity: {
        last_updated: 1730527651000,
        count: BigInt(2),
        avg: null,
      },
    },
  },
  {
    time: 1730523600000,
    group: "gpt-3.5-turbo",
    last_updated: 1730537106000,
    count: BigInt(1),
    avg: 0.19330717143673967,
    scores: {
      Toxicity: {
        last_updated: 1730537106000,
        count: BigInt(1),
        avg: null,
      },
    },
  },
];

describe("groups", () => {
  describe("getSeriesColorMap", () => {
    test("scores data", () => {
      const colorMap = getSeriesColorMap(["Factuality", "Toxicity"]);
      expect(colorMap).toEqual({
        Factuality: { metadata: 0, none: 0, score: 0, metric: 0 },
        Toxicity: { metadata: 1, none: 1, score: 1, metric: 1 },
      });
    });
  });
  describe("getGroupedSeriesNames", () => {
    test("scores data", () => {
      const { names, groupBys } = getGroupedSeriesNames({
        groupNames: ["key1", "key2"],
        selectedSeries: ["Factuality", "Toxicity"],
        groupKeyToGroupBys: new Map([
          ["key1", [{ key: "model", value: "gpt-3.5-turbo" }]],
          ["key2", [{ key: "model", value: "gpt-3.5" }]],
        ]),
      });
      expect(names).toEqual([
        "Factuality (gpt-3.5-turbo)",
        "Toxicity (gpt-3.5-turbo)",
        "Factuality (gpt-3.5)",
        "Toxicity (gpt-3.5)",
      ]);

      expect(groupBys).toEqual([
        [{ key: "model", value: "gpt-3.5-turbo" }],
        [{ key: "model", value: "gpt-3.5-turbo" }],
        [{ key: "model", value: "gpt-3.5" }],
        [{ key: "model", value: "gpt-3.5" }],
      ]);
    });
  });
  describe("getDataSeries", () => {
    test("scores data", () => {
      const data = getDataSeries({
        data: scoresData,
        selectedSeries: ["Factuality", "Toxicity"],
      });
      expect(data).toEqual([
        {
          x: 1727852400000,
          y: [{ value: 0.1804978620159966 }, null],
          metadata: { time: "2024-10-02T07:00:00.000Z", count: BigInt(5) },
        },
        {
          x: 1727852500000,
          y: [{ value: 0.7663173144344797 }, { value: 0.15762461610304773 }],
          metadata: { time: "2024-10-02T07:01:40.000Z", count: BigInt(7) },
        },
        {
          x: 1727852600000,
          y: [{ value: 0.491672740635523 }, { value: 0.0715352348847526 }],
          metadata: { time: "2024-10-02T07:03:20.000Z", count: BigInt(4) },
        },
      ]);
    });

    test("scores data with null avg", () => {
      const data = getDataSeries({
        data: scoresDataWithNullAvg,
        selectedSeries: ["Factuality", "Toxicity"],
      });
      expect(data).toEqual([
        {
          x: 1727852400000,
          y: [null, null],
          metadata: { time: "2024-10-02T07:00:00.000Z", count: BigInt(5) },
        },
        {
          x: 1727852500000,
          y: [{ value: 0.7663173144344797 }, { value: 0.15762461610304773 }],
          metadata: { time: "2024-10-02T07:01:40.000Z", count: BigInt(7) },
        },
        {
          x: 1727852600000,
          y: [{ value: 0.491672740635523 }, null],
          metadata: { time: "2024-10-02T07:03:20.000Z", count: BigInt(4) },
        },
      ]);
    });

    test("token count data", () => {
      const data = getDataSeries({
        data: tokenCountData,
        selectedSeries: ["sum_tokens", "sum_completion_tokens"],
      });
      expect(data).toEqual([
        {
          x: 1727852400000,
          y: [{ value: 61031 }, { value: 18314 }],
          metadata: { time: "2024-10-02T07:00:00.000Z", count: BigInt(519) },
        },
        {
          x: 1727852500000,
          y: [null, null],
          metadata: { time: "2024-10-02T07:01:40.000Z", count: BigInt(1519) },
        },
        {
          x: 1727852600000,
          y: [{ value: 408188 }, { value: 122431 }],
          metadata: { time: "2024-10-02T07:03:20.000Z", count: BigInt(2419) },
        },
      ]);
    });
  });

  describe("isEmptyMetricsData", () => {
    test("ignores time and count fields", () => {
      expect(isEmptyMetricsData({ time: 1, count: BigInt(1), a: null })).toBe(
        true,
      );
    });
    test("returns false if any value is not null", () => {
      expect(
        isEmptyMetricsData({ time: 1, count: BigInt(1), a: 1, b: null }),
      ).toBe(false);
    });
  });

  describe("getGroupedDataSeries", () => {
    test("token count data", () => {
      const data = getGroupedDataSeries({
        groupedData: groupData(groupedTokenCountData),
        groupNames: ["gpt-3.5-turbo", "gpt-3.5"],
        selectedSeries: ["sum_tokens", "sum_prompt_tokens"],
      });

      expect(data).toEqual([
        {
          x: 1727852400000,
          y: [
            { value: 61031 },
            { value: 42717 },
            { value: 49934 },
            { value: 34968 },
          ],
          metadata: { time: "2024-10-02T07:00:00.000Z", count: BigInt(519) },
        },
        {
          x: 1727938800000,
          y: [
            { value: 179225 },
            { value: 125328 },
            { value: 174107 },
            { value: 121854 },
          ],
          metadata: { time: "2024-10-03T07:00:00.000Z", count: BigInt(0) },
        },

        {
          x: 1728025200000,
          y: [
            { value: 158142 },
            { value: 110777 },
            { value: 166151 },
            { value: 116260 },
          ],
          metadata: { time: "2024-10-04T07:00:00.000Z", count: BigInt(0) },
        },
      ]);
    });
    test("scores data", () => {
      const data = getGroupedDataSeries({
        groupedData: groupData(groupedScoresData),
        groupNames: ["gpt-3.5-turbo", "gpt-3.5"],
        selectedSeries: ["Factuality", "Toxicity"],
      });

      expect(data).toEqual([
        {
          x: 1730502000000,
          y: [
            { value: 0.1804978620159966 },
            null,
            { value: 0.7663173144344797 },
            { value: 0.15762461610304773 },
          ],
          metadata: { time: "2024-11-01T23:00:00.000Z", count: BigInt(12) },
        },
        {
          x: 1730512800000,
          y: [
            { value: 0.7555176513578717 },
            null,
            { value: 0.2844902652759832 },
            null,
          ],
          metadata: { time: "2024-11-02T02:00:00.000Z", count: BigInt(2) },
        },
        {
          x: 1730523600000,
          y: [
            null,
            { value: 0.19330717143673967 },
            { value: 0.7299530300500745 },
            { value: 0.16369103080539732 },
          ],
          metadata: { time: "2024-11-02T05:00:00.000Z", count: BigInt(6) },
        },
      ]);
    });

    test("scores data with null avg", () => {
      const data = getGroupedDataSeries({
        groupedData: groupData(groupedScoresDataWithNullAvg),
        groupNames: ["gpt-3.5-turbo", "gpt-3.5"],
        selectedSeries: ["Factuality", "Toxicity"],
      });

      expect(data).toEqual([
        {
          x: 1730502000000,
          y: [
            null,
            null,
            { value: 0.7663173144344797 },
            { value: 0.15762461610304773 },
          ],
          metadata: { time: "2024-11-01T23:00:00.000Z", count: BigInt(12) },
        },
        {
          x: 1730512800000,
          y: [null, null, { value: 0.2844902652759832 }, null],
          metadata: { time: "2024-11-02T02:00:00.000Z", count: BigInt(2) },
        },
        {
          x: 1730523600000,
          y: [null, null, { value: 0.7299530300500745 }, null],
          metadata: { time: "2024-11-02T05:00:00.000Z", count: BigInt(6) },
        },
      ]);
    });
  });

  describe("groupData", () => {
    test("empty data", () => {
      const data = groupData([]);
      expect(data).toEqual({});
    });

    test("cost data", () => {
      const data = groupData(groupedCostData);
      expect(data).toEqual({
        "1731571200000": {
          groups: {
            "gpt-3.5-turbo": {
              inputTokensCost: 0.000348,
              outputTokensCost: 0.000441,
              totalCost: 0.000789,
              time: 1731571200000,
              count: BigInt(1),
              group: "gpt-3.5-turbo",
            },
            "gpt-4o": {
              inputTokensCost: 2.6493450000000003,
              outputTokensCost: 3.01085,
              totalCost: 5.660195,
              time: 1731571200000,
              count: BigInt(1206),
              group: "gpt-4o",
            },
            "gpt-4": {
              inputTokensCost: 1.91172,
              outputTokensCost: 1.64418,
              totalCost: 3.5559000000000003,
              time: 1731571200000,
              count: BigInt(91),
              group: "gpt-4",
            },
          },
        },
      });
    });

    test("token count data", () => {
      const data = groupData(groupedTokenCountData);
      expect(data).toEqual({
        "1727852400000": {
          groups: {
            "gpt-3.5": {
              time: 1727852400000,
              group: "gpt-3.5",
              count: BigInt(0),
              sum_tokens: 49934,
              sum_prompt_tokens: 34968,
              sum_completion_tokens: 14966,
            },
            "gpt-3.5-turbo": {
              time: 1727852400000,
              group: "gpt-3.5-turbo",
              count: BigInt(519),
              sum_tokens: 61031,
              sum_prompt_tokens: 42717,
              sum_completion_tokens: 18314,
            },
            "gpt-4": {
              time: 1727852400000,
              group: "gpt-4",
              count: BigInt(0),
              sum_tokens: 408188,
              sum_prompt_tokens: 285757,
              sum_completion_tokens: 122431,
            },
          },
        },
        "1727938800000": {
          groups: {
            "gpt-3.5": {
              time: 1727938800000,
              group: "gpt-3.5",
              count: BigInt(0),
              sum_tokens: 174107,
              sum_prompt_tokens: 121854,
              sum_completion_tokens: 52253,
            },
            "gpt-3.5-turbo": {
              time: 1727938800000,
              group: "gpt-3.5-turbo",
              count: BigInt(0),
              sum_tokens: 179225,
              sum_prompt_tokens: 125328,
              sum_completion_tokens: 53897,
            },
            "gpt-4": {
              time: 1727938800000,
              group: "gpt-4",
              count: BigInt(0),
              sum_tokens: 1214239,
              sum_prompt_tokens: 849858,
              sum_completion_tokens: 364381,
            },
          },
        },
        "1728025200000": {
          groups: {
            "gpt-3.5": {
              time: 1728025200000,
              group: "gpt-3.5",
              count: BigInt(0),
              sum_tokens: 166151,
              sum_prompt_tokens: 116260,
              sum_completion_tokens: 49891,
            },
            "gpt-3.5-turbo": {
              time: 1728025200000,
              group: "gpt-3.5-turbo",
              count: BigInt(0),
              sum_tokens: 158142,
              sum_prompt_tokens: 110777,
              sum_completion_tokens: 47365,
            },
            "gpt-4": {
              time: 1728025200000,
              group: "gpt-4",
              count: BigInt(0),
              sum_tokens: 1220508,
              sum_prompt_tokens: 854102,
              sum_completion_tokens: 366406,
            },
          },
        },
      });
    });
  });
});
